// Performance optimization configuration
module.exports = {

  // Image optimization settings
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Performance budgets (in KB)
  budgets: {
    maxInitialBundle: 200, // 200KB
    maxPageBundle: 100,    // 100KB per page
    maxAssetSize: 50,      // 50KB per asset
  },

  // Critical CSS settings
  criticalCSS: {
    enabled: true,
    inlineThreshold: 10240, // 10KB
  },

  // Compression settings
  compression: {
    gzip: true,
    brotli: true,
  },

  // Caching strategies
  caching: {
    staticAssets: 'max-age=31536000, immutable', // 1 year
    pages: 'max-age=3600, s-maxage=86400',       // 1 hour, CDN 1 day
    api: 'max-age=300, s-maxage=600',            // 5 min, CDN 10 min
  },

  // Code splitting recommendations
  codeSplitting: {
    chunks: 'all',
    minSize: 20000,
    maxSize: 244000,
    cacheGroups: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        chunks: 'all',
      },
      common: {
        name: 'common',
        minChunks: 2,
        chunks: 'all',
        enforce: true,
      },
    },
  },

  // Performance monitoring
  monitoring: {
    webVitals: true,
  },
};
