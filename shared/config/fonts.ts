// shared/config/fonts.ts
import { Poppins, Rubik } from 'next/font/google';

// Configure Poppins font with optimal loading
export const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap', // Use font-display: swap for better performance
  variable: '--font-poppins',
  preload: true, // Preload the font for better performance
  fallback: ['system-ui', 'arial'], // Add fallback fonts to reduce layout shift
  adjustFontFallback: true, // Automatically adjust fallback fonts to match
});

// Configure Rubik font for login components
export const rubik = Rubik({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap', // Use font-display: swap for better performance
  variable: '--font-rubik',
  preload: true, // Preload the font for better performance
  fallback: ['system-ui', 'arial'], // Add fallback fonts to reduce layout shift
  adjustFontFallback: true, // Automatically adjust fallback fonts to match
});

// Export font class names for use in components
export const fontClassNames = {
  poppins: poppins.className,
  rubik: rubik.className,
  variable: poppins.variable,
  rubikVariable: rubik.variable,
};
