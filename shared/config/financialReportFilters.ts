// shared/config/financialReportFilters.ts - Filter configuration for financial report
import { FilterDefinition } from "@/shared/UI/components";
import { getTransactionTypeOptionsByCategory } from "./transactionTypes";

// Default visible filters for financial report (4 filters always visible)
export const DEFAULT_FINANCIAL_REPORT_VISIBLE_FILTERS = ["username", "transactionId", "dateRange", "actionCategory"];

// All available filters for financial report
export const FINANCIAL_REPORT_FILTERS: FilterDefinition[] = [
	// DEFAULT FILTERS (always visible)
	{
		id: "username",
		label: "Username",
		icon: "ri-user-line",
		component: "userSearch",
		placeholder: "Search username...",
		filterKey: "username"
	},
	{
		id: "transactionId",
		label: "Transaction ID",
		icon: "ri-hashtag",
		component: "input",
		placeholder: "Enter transaction ID...",
		filterKey: "transactionId"
	},
	{
		id: "dateRange",
		label: "Date Range",
		icon: "ri-calendar-line",
		component: "dateRange",
		filterKey: "dateRange"
	},
	{
		id: "actionCategory",
		label: "Action Category",
		icon: "ri-folder-line",
		component: "select",
		filterKey: "actionCategory",
		options: [
			{ value: "", label: "All Categories" },
			{ value: "financial", label: "Financial" }
		]
	},

	// MORE FILTERS (collapsible section)
	{
		id: "debitTransactionId",
		label: "Debit Transaction ID",
		icon: "ri-hashtag",
		component: "input",
		placeholder: "Enter debit transaction ID...",
		filterKey: "debitTransactionId"
	},
	{
		id: "actionType",
		label: "Action Type",
		icon: "ri-exchange-line",
		component: "select",
		filterKey: "actionType",
		options: getTransactionTypeOptionsByCategory("financial")
	}
];
