
/**
 * Performance configuration for the application
 */
export const performanceConfig = {
  // Cache configurations
  cache: {
    // API response caching
    api: {
      ttl: 5 * 60 * 1000, // 5 minutes
      staleWhileRevalidate: 2 * 60 * 1000, // 2 minutes
      maxSize: 500,
    },
    // User data caching
    user: {
      ttl: 10 * 60 * 1000, // 10 minutes
      staleWhileRevalidate: 5 * 60 * 1000, // 5 minutes
      maxSize: 100,
    },
    // Static data caching
    static: {
      ttl: 60 * 60 * 1000, // 1 hour
      staleWhileRevalidate: 30 * 60 * 1000, // 30 minutes
      maxSize: 200,
    },
    // Metadata caching
    metadata: {
      ttl: 24 * 60 * 60 * 1000, // 24 hours
      staleWhileRevalidate: 12 * 60 * 60 * 1000, // 12 hours
      maxSize: 50,
    },
  },

  // Revalidation intervals (in seconds)
  revalidation: {
    // Static pages
    homepage: 3600, // 1 hour
    dashboard: 300, // 5 minutes
    userManagement: 600, // 10 minutes

    // Authentication pages
    signIn: 3600, // 1 hour
    signUp: 3600, // 1 hour

    // Dynamic content
    userDetails: 1800, // 30 minutes
    financialReports: 300, // 5 minutes

    // SEO files
    sitemap: 86400, // 24 hours
    robots: 604800, // 7 days
    manifest: 2592000, // 30 days
  },

  // Resource preloading configuration
  preload: {
    // Critical resources to preload on all pages
    critical: [
      { href: '/assets/css/styles.css', as: 'style', type: 'text/css' },
      { href: '/assets/fonts/inter.woff2', as: 'font', type: 'font/woff2', crossorigin: true },
      { href: '/assets/images/logo.svg', as: 'image', type: 'image/svg+xml' },
    ],

    // Page-specific preloading
    pages: {
      '/': [
        { href: '/dashboard', as: 'document' },
        { href: '/user-management', as: 'document' },
      ],
      '/dashboard': [
        { href: '/user-management', as: 'document' },
        { href: '/assets/js/charts.js', as: 'script' },
      ],
      '/user-management': [
        { href: '/user-management/create', as: 'document' },
        { href: '/assets/js/tables.js', as: 'script' },
      ],
    },
  },

  // DNS prefetch and preconnect domains
  domains: {
    dnsPrefetch: [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      'https://staging-reports-api.8dexsuperadmin.com',
    ],
    preconnect: [
      { href: 'https://fonts.googleapis.com', crossorigin: false },
      { href: 'https://fonts.gstatic.com', crossorigin: true },
    ],
  },

  // Image optimization settings
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year
    quality: 85,
    placeholder: 'blur',
  },

  // Bundle optimization
  bundle: {
    // Maximum bundle sizes (in KB)
    maxInitialBundle: 200,
    maxPageBundle: 100,
    maxAssetSize: 50,

    // Code splitting configuration
    splitChunks: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        priority: 10,
      },
      common: {
        name: 'common',
        minChunks: 2,
        priority: 5,
      },
      ui: {
        test: /[\\/]shared[\\/]UI[\\/]/,
        name: 'ui-components',
        priority: 8,
      },
      userManagement: {
        test: /[\\/]user-management[\\/]/,
        name: 'user-management',
        priority: 7,
      },
    },
  },

  // Performance monitoring thresholds
  monitoring: {
    // Core Web Vitals thresholds
    webVitals: {
      CLS: { good: 0.1, poor: 0.25 },
      INP: { good: 200, poor: 500 },
      FCP: { good: 1800, poor: 3000 },
      LCP: { good: 2500, poor: 4000 },
      TTFB: { good: 800, poor: 1800 },
    },

    // Custom metrics thresholds
    custom: {
      apiResponse: { good: 500, poor: 2000 }, // API response time in ms
    },
  },

  // Lazy loading configuration
  lazyLoading: {
    // Intersection observer options
    rootMargin: '50px',
    threshold: 0.1,

    // Elements to lazy load
    selectors: [
      'img[data-src]',
      'iframe[data-src]',
      '.lazy-component',
    ],
  },

  // Service Worker configuration
  serviceWorker: {
    enabled: process.env.NODE_ENV === 'production',
    cacheFirst: [
      '/assets/images/',
      '/assets/fonts/',
      '/assets/css/',
    ],
    networkFirst: [
      '/api/',
      '/user-management/',
    ],
    staleWhileRevalidate: [
      '/',
      '/dashboard',
    ],
  },
};

/**
 * Get cache configuration for a specific type
 */
export function getCacheConfig(type: keyof typeof performanceConfig.cache) {
  return performanceConfig.cache[type];
}

/**
 * Get revalidation interval for a specific page
 */
export function getRevalidationInterval(page: keyof typeof performanceConfig.revalidation) {
  return performanceConfig.revalidation[page];
}

/**
 * Get preload resources for a specific page
 */
export function getPreloadResources(page: string) {
  const critical = performanceConfig.preload.critical;
  const pageSpecific = performanceConfig.preload.pages[page as keyof typeof performanceConfig.preload.pages] || [];
  return [...critical, ...pageSpecific];
}

/**
 * Initialize performance optimizations
 */
export function initializePerformanceOptimizations() {
  if (typeof window === 'undefined') return;

  // DNS prefetch
  performanceConfig.domains.dnsPrefetch.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = domain;
    document.head.appendChild(link);
  });

  // Preconnect
  performanceConfig.domains.preconnect.forEach(({ href, crossorigin }) => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;
    if (crossorigin) link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
}

export default performanceConfig;
