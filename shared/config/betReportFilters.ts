// shared/config/betReportFilters.ts - Filter configuration for bet report
import { FilterDefinition } from "@/shared/UI/components";

// Default visible filters for bet report (4 filters always visible)
export const DEFAULT_BET_REPORT_VISIBLE_FILTERS = ["dateRange", "username", "transactionId", "betStatus"];

// All available filters for bet report
export const BET_REPORT_FILTERS: FilterDefinition[] = [
	// DEFAULT FILTERS (always visible)
	{
		id: "dateRange",
		label: "Date Range",
		icon: "ri-calendar-line",
		component: "dateRange",
		filterKey: "dateRange"
	},
	{
		id: "username",
		label: "Username",
		icon: "ri-user-line",
		component: "userSearch",
		placeholder: "Search username...",
		filterKey: "username"
	},
	{
		id: "transactionId",
		label: "Bet ID",
		icon: "ri-hashtag",
		component: "input",
		placeholder: "Enter bet ID...",
		filterKey: "transactionId"
	},
	{
		id: "betStatus",
		label: "Bet Status",
		icon: "ri-checkbox-circle-line",
		component: "select",
		filterKey: "status",
		options: [
			{ value: "", label: "All" },
			{ value: "Win", label: "Win" },
			{ value: "Lost", label: "Loss" },
			{ value: "Pending", label: "Pending" },
			{ value: "Cancelled", label: "Cancelled" }
		]
	},

	// MORE FILTERS (collapsible section)
	{
		id: "playerId",
		label: "Player ID",
		icon: "ri-user-line",
		component: "input",
		placeholder: "Enter player ID...",
		filterKey: "playerId"
	},
	{
		id: "marketId",
		label: "Market ID",
		icon: "ri-store-line",
		component: "input",
		placeholder: "Enter market ID...",
		filterKey: "marketId"
	},
	{
		id: "payoutStatus",
		label: "Payout Status",
		icon: "ri-money-dollar-circle-line",
		component: "select",
		filterKey: "payoutStatus",
		options: [
			{ value: "", label: "All" },
			{ value: "1", label: "Settled" },
			{ value: "3", label: "Unsettled" },
			{ value: "2", label: "Rejected" }
		]
	}
];
