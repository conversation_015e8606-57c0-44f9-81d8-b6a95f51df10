// shared/config/exportCenterFilters.ts - Filter configuration for export center

import { FilterDefinition } from "@/shared/UI/components";

// Default visible filters for export center
export const DEFAULT_EXPORT_CENTER_VISIBLE_FILTERS = ["search_id", "status"];

// All available filters for export center
export const EXPORT_CENTER_FILTERS: FilterDefinition[] = [
  {
    id: "search_id",
    label: "Search by ID",
    icon: "ri-search-line",
    component: "input",
    placeholder: "Search by export ID...",
    filterKey: "search_id"
  },
  {
    id: "status",
    label: "Status",
    icon: "ri-checkbox-circle-line",
    component: "select",
    filterKey: "status",
    options: [
      { value: "", label: "All Statuses" },
      { value: "pending", label: "Pending" },
      { value: "processing", label: "Processing" },
      { value: "completed", label: "Completed" },
      { value: "failed", label: "Failed" }
    ]
  }
];
