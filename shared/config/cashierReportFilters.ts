// shared/config/cashierReportFilters.ts - Filter configuration for cashier report
import { FilterDefinition } from "@/shared/UI/components";
import { getTransactionTypeOptions } from "./transactionTypes";

// Default visible filters for cashier report (4 filters always visible)
export const DEFAULT_CASHIER_REPORT_VISIBLE_FILTERS = ["username", "transactionId", "dateRange", "actionType"];

// All available filters for cashier report
export const CASHIER_REPORT_FILTERS: FilterDefinition[] = [
	// DEFAULT FILTERS (always visible)
	{
		id: "username",
		label: "Username",
		icon: "ri-user-line",
		component: "userSearch",
		placeholder: "Search username...",
		filterKey: "username"
	},
	{
		id: "transactionId",
		label: "Transaction ID",
		icon: "ri-hashtag",
		component: "input",
		placeholder: "Enter transaction ID...",
		filterKey: "transactionId"
	},
	{
		id: "dateRange",
		label: "Date Range",
		icon: "ri-calendar-line",
		component: "dateRange",
		filterKey: "dateRange"
	},
	{
		id: "actionType",
		label: "Action Type",
		icon: "ri-exchange-line",
		component: "select",
		filterKey: "actionType",
		options: getTransactionTypeOptions()
	}
];
