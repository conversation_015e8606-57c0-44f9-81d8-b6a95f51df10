// shared/config/loginHistoryFilters.ts - Filter configuration for login history
import { FilterDefinition } from "@/shared/UI/components";

// Default visible filters for login history
export const DEFAULT_LOGIN_HISTORY_VISIBLE_FILTERS = ["dateRange", "playerId"];

// All available filters for login history
export const LOGIN_HISTORY_FILTERS: FilterDefinition[] = [
	{
		id: "dateRange",
		label: "Date Range",
		icon: "ri-calendar-line",
		component: "dateRange",
		filterKey: "dateRange"
	},
	{
		id: "playerId",
		label: "Player ID",
		icon: "ri-user-line",
		component: "input",
		placeholder: "Enter player ID...",
		filterKey: "playerId"
	}
];
