// shared/config/betHistoryFilters.ts - Filter configuration for bet history
import { FilterDefinition } from "@/shared/UI/components";

// Default visible filters for bet history
export const DEFAULT_BET_HISTORY_VISIBLE_FILTERS = ["search", "gameProvider", "transactionType", "status"];

// All available filters for bet history
export const BET_HISTORY_FILTERS: FilterDefinition[] = [
	{
		id: "search",
		label: "Search",
		icon: "ri-search-line",
		component: "input",
		placeholder: "Search transactions...",
		filterKey: "search"
	},
	{
		id: "gameProvider",
		label: "Game Provider",
		icon: "ri-gamepad-line",
		component: "select",
		filterKey: "gameProvider",
		options: [
			{ value: "", label: "All Providers" },
			{ value: "TurboStars", label: "TurboStars" },
			{ value: "Evolution", label: "Evolution" },
			{ value: "Pragmatic", label: "Pragmatic Play" },
			{ value: "NetEnt", label: "NetEnt" }
		]
	},
	{
		id: "transactionType",
		label: "Transaction Type",
		icon: "ri-exchange-line",
		component: "select",
		filterKey: "actionType",
		options: [
			{ value: "", label: "All Types" },
			{ value: "bet", label: "Bet" },
			{ value: "win", label: "Win" },
			{ value: "deposit", label: "Deposit" },
			{ value: "withdrawal", label: "Withdrawal" }
		]
	},
	{
		id: "status",
		label: "Status",
		icon: "ri-checkbox-circle-line",
		component: "select",
		filterKey: "status",
		options: [
			{ value: "", label: "All Status" },
			{ value: "completed", label: "Completed" },
			{ value: "pending", label: "Pending" },
			{ value: "failed", label: "Failed" },
			{ value: "cancelled", label: "Cancelled" }
		]
	},
	{
		id: "gameType",
		label: "Game Type",
		icon: "ri-trophy-line",
		component: "select",
		filterKey: "gameType",
		options: [
			{ value: "", label: "All Games" },
			{ value: "sports", label: "Sports" },
			{ value: "casino", label: "Casino" },
			{ value: "live_casino", label: "Live Casino" },
			{ value: "slots", label: "Slots" }
		]
	},
	{
		id: "transactionId",
		label: "Transaction ID",
		icon: "ri-hashtag",
		component: "input",
		placeholder: "Enter transaction ID...",
		filterKey: "transactionId"
	},
	{
		id: "amount",
		label: "Amount",
		icon: "ri-money-dollar-circle-line",
		component: "input",
		placeholder: "Enter amount...",
		filterKey: "amount"
	},
	{
		id: "roundId",
		label: "Round ID",
		icon: "ri-refresh-line",
		component: "input",
		placeholder: "Enter round ID...",
		filterKey: "roundId"
	},
	{
		id: "debitTransactionId",
		label: "Debit Transaction ID",
		icon: "ri-subtract-line",
		component: "input",
		placeholder: "Enter debit transaction ID...",
		filterKey: "debitTransactionId"
	},
	{
		id: "utrNumber",
		label: "UTR Number",
		icon: "ri-barcode-line",
		component: "input",
		placeholder: "Enter UTR number...",
		filterKey: "utrNumber"
	},
	{
		id: "dateRange",
		label: "Date Range",
		icon: "ri-calendar-line",
		component: "dateRange",
		filterKey: "dateTime"
	}
];
