// shared/providers/Global401ErrorProvider.tsx
'use client';

import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { configureGlobalApiErrorHandler, createFetchInterceptor } from '@/shared/utils/globalApiErrorHandler';
import { AuthErrorModal } from '@/shared/components/ui-elements/alerts';

/**
 * Context interface for global 401 error handling
 */
interface Global401ErrorContextType {
  /** Whether the 401 error modal is currently visible */
  isModalVisible: boolean;
  /** Show the 401 error modal */
  showModal: () => void;
  /** Hide the 401 error modal */
  hideModal: () => void;
  /** Manually trigger 401 error handling */
  trigger401Error: (message?: string) => void;
  /** Current error message */
  errorMessage: string;
  /** Set custom error message */
  setErrorMessage: (message: string) => void;
}

/**
 * Default context value
 */
const defaultContextValue: Global401ErrorContextType = {
  isModalVisible: false,
  showModal: () => {},
  hideModal: () => {},
  trigger401Error: () => {},
  errorMessage: '',
  setErrorMessage: () => {},
};

/**
 * Global 401 Error Context
 */
const Global401ErrorContext = createContext<Global401ErrorContextType>(defaultContextValue);

/**
 * Hook to use the Global 401 Error Context
 */
export const useGlobal401Error = (): Global401ErrorContextType => {
  const context = useContext(Global401ErrorContext);
  if (!context) {
    throw new Error('useGlobal401Error must be used within a Global401ErrorProvider');
  }
  return context;
};

/**
 * Props for the Global401ErrorProvider
 */
interface Global401ErrorProviderProps {
  children: ReactNode;
  /** Custom error message for 401 errors */
  defaultErrorMessage?: string;
  /** Custom modal title */
  modalTitle?: string;
  /** Whether to enable debug logging */
  enableDebugLogging?: boolean;
}

/**
 * Global 401 Error Provider Component
 * Provides global 401 error handling throughout the application
 */
export const Global401ErrorProvider: React.FC<Global401ErrorProviderProps> = ({
  children,
  defaultErrorMessage = "Your session has expired or you are not authorized to access this resource. Please sign in again to continue.",
  modalTitle = "Authentication Required",
  enableDebugLogging = process.env.NODE_ENV === 'development'
}) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  
  // State management
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState(defaultErrorMessage);

  /**
   * Show the 401 error modal
   */
  const showModal = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  /**
   * Hide the 401 error modal and redirect to login
   */
  const hideModal = useCallback(() => {
    setIsModalVisible(false);

    // Small delay to allow modal close animation
    setTimeout(() => {
      router.replace('/authentication/sign-in');
    }, 300);
  }, [router]);

  /**
   * Manually trigger 401 error handling
   */
  const trigger401Error = useCallback((message?: string) => {
    if (message) {
      setErrorMessage(message);
    }
    showModal();
  }, [showModal]);

  /**
   * Redirect to login page
   */
  const redirectToLogin = useCallback(() => {
    router.replace('/authentication/sign-in');
  }, [router]);

  /**
   * Initialize global error handler configuration
   */
  useEffect(() => {
    // Configure the global API error handler
    configureGlobalApiErrorHandler({
      queryClient,
      show401Modal: showModal,
      redirectToLogin,
      enableDebugLogging
    });

    // Create fetch interceptor for automatic 401 handling
    const restoreFetch = createFetchInterceptor();

    // Cleanup function to restore original fetch
    return () => {
      restoreFetch();
    };
  }, [queryClient, showModal, redirectToLogin, enableDebugLogging]);

  /**
   * Context value
   */
  const contextValue: Global401ErrorContextType = {
    isModalVisible,
    showModal,
    hideModal,
    trigger401Error,
    errorMessage,
    setErrorMessage
  };

  return (
    <Global401ErrorContext.Provider value={contextValue}>
      {children}
      
      {/* Global 401 Error Modal */}
      <AuthErrorModal
        isOpen={isModalVisible}
        onClose={hideModal}
        message={errorMessage}
        title={modalTitle}
        showBackdrop={true}
        className="z-[9999]"
      />
    </Global401ErrorContext.Provider>
  );
};

/**
 * HOC to wrap components with Global 401 Error handling
 */
export const withGlobal401ErrorHandling = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  const WrappedComponent: React.FC<P> = (props) => {
    return (
      <Global401ErrorProvider>
        <Component {...props} />
      </Global401ErrorProvider>
    );
  };

  WrappedComponent.displayName = `withGlobal401ErrorHandling(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default Global401ErrorProvider;
