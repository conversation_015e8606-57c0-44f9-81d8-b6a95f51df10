import React, { Fragment } from 'react';

export interface SpkLoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'border' | 'grow';
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  className?: string;
  label?: string;
  children?: React.ReactNode;
  inline?: boolean;
  centered?: boolean;
}

const SpkLoadingSpinner: React.FC<SpkLoadingSpinnerProps> = ({
  size = 'md',
  variant = 'border',
  color = 'primary',
  className = '',
  label = 'Loading...',
  children,
  inline = false,
  centered = false
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'xs':
        return 'w-3 h-3';
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-8 h-8';
      case 'xl':
        return 'w-12 h-12';
      default:
        return 'w-6 h-6';
    }
  };

  const getVariantClass = () => {
    if (variant === 'grow') {
      return `animate-ping bg-${color}`;
    }
    return `animate-spin border-2 border-${color}/20 border-t-${color}`;
  };

  const spinnerClasses = [
    getSizeClass(),
    getVariantClass(),
    'rounded-full',
    className
  ].filter(Boolean).join(' ');

  const containerClasses = [
    inline ? 'inline-flex' : 'flex',
    'items-center',
    centered ? 'justify-center' : '',
    centered && !inline ? 'py-8' : ''
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div className={containerClasses}>
        <div className={spinnerClasses} role="status" aria-label={label}>
          <span className="sr-only">{label}</span>
        </div>
        {children && (
          <span className="ms-2">
            {children}
          </span>
        )}
      </div>
    </Fragment>
  );
};

export default SpkLoadingSpinner;
