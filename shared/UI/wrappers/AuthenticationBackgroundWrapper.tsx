"use client";

import backgroundCycleStyles from '@/app/css/animations/background-cycle.module.css';
import Image from "next/image";
import React, { Fragment } from "react";

export interface AuthenticationBackgroundWrapperProps {
  children: React.ReactNode;
  backgroundImages: string[];
  showLogo?: boolean;
  logoSrc?: string;
  logoAlt?: string;
  logoWidth?: number;
  logoHeight?: number;
  className?: string;
}

/**
 * Reusable authentication background wrapper component
 * Provides animated background cycling and dark overlay for authentication pages
 * Extracted from SignInFormUI to be reusable across authentication components
 */
export const AuthenticationBackgroundWrapper: React.FC<AuthenticationBackgroundWrapperProps> = ({
  children,
  backgroundImages,
  showLogo = true,
  logoSrc = "/assets/login/Golden-Island.webp",
  logoAlt = "Golden Island Logo",
  logoWidth = 340,
  logoHeight = 180,
  className = "",
}) => {
  return (
    <Fragment>
      <div className={`flex min-h-screen w-full items-center justify-center relative overflow-hidden bg-background ${className}`}>
        {/* CSS Animated backgrounds */}
        {backgroundImages.map((image, index) => (
          <div
            key={index}
            className={`${backgroundCycleStyles.signinBackgroundLayer} ${backgroundCycleStyles[`signinBg${index + 1}`]}`}
            style={{
              backgroundImage: `url(${image})`
            }}
          />
        ))}

        {/* Dark overlay for improved text readability */}
        <div className={backgroundCycleStyles.signinOverlay} />

        <div className="relative z-10 flex flex-col items-center w-full px-4 sm:px-6 md:px-8">
          {/* Logo - responsive sizing */}
          {showLogo && (
            <div className="mb-6 sm:mb-8">
              <Image
                src={logoSrc}
                alt={logoAlt}
                width={logoWidth}
                height={logoHeight}
                className="w-auto h-auto max-w-[250px] sm:max-w-[300px] md:max-w-[340px]"
                priority
              />
            </div>
          )}

          {/* Content */}
          {children}
        </div>
      </div>
    </Fragment>
  );
};

export default AuthenticationBackgroundWrapper;
