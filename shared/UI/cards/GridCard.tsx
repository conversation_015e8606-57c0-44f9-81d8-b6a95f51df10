"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { SpkBadge } from '../components';

export interface GridCardProps {
  title: string;
  description?: string;
  imageSrc?: string;
  imageAlt?: string;
  imageClassName?: string;
  href?: string;
  buttonText?: string;
  buttonColor?: string;
  badge?: {
    text: string;
    className?: string;
    color?: string;
  };
  className?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  showImage?: boolean;
  showButton?: boolean;
  showDescription?: boolean;
  isClickable?: boolean;
  onClick?: () => void;
  children?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * GridCard Component
 * 
 * A reusable card component for grid layouts with images, titles, descriptions, and actions.
 * Consolidates spkgridcards, spkgridmarkupcard, and spktitlecards patterns.
 * 
 * Features:
 * - Image support with Next.js Image optimization
 * - Flexible content areas (header, body, footer)
 * - Badge support with customizable styling
 * - Button/link actions with routing
 * - Clickable card functionality
 * - Multiple size variants
 * - Full dark theme compatibility
 * - TypeScript interfaces for type safety
 * - Customizable styling for all sections
 */
const GridCard: React.FC<GridCardProps> = ({
  title,
  description,
  imageSrc,
  imageAlt = 'Card Image',
  imageClassName = '',
  href,
  buttonText,
  buttonColor = 'primary',
  badge,
  className = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  titleClassName = '',
  descriptionClassName = '',
  showImage = true,
  showButton = true,
  showDescription = true,
  isClickable = false,
  onClick,
  children,
  size = 'md'
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          imageHeight: 150,
          imageWidth: 250,
          titleSize: 'text-sm',
          descriptionSize: 'text-xs',
          padding: 'p-3',
          spacing: 'space-y-2'
        };
      case 'lg':
        return {
          imageHeight: 250,
          imageWidth: 400,
          titleSize: 'text-lg',
          descriptionSize: 'text-base',
          padding: 'p-6',
          spacing: 'space-y-4'
        };
      case 'md':
      default:
        return {
          imageHeight: 200,
          imageWidth: 300,
          titleSize: 'text-base',
          descriptionSize: 'text-sm',
          padding: 'p-4',
          spacing: 'space-y-3'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  const cardClasses = [
    'box',
    'overflow-hidden',
    isClickable || onClick ? 'cursor-pointer hover:shadow-lg transition-all duration-300 group' : '',
    className
  ].filter(Boolean).join(' ');

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    } else if (isClickable && href) {
      // Handle navigation if needed
    }
  };

  const renderImage = () => {
    if (!showImage || !imageSrc) return null;

    return (
      <Image
        src={imageSrc}
        alt={imageAlt}
        width={sizeClasses.imageWidth}
        height={sizeClasses.imageHeight}
        className={`card-img-top object-cover w-full ${imageClassName}`}
        style={{ height: `${sizeClasses.imageHeight}px` }}
      />
    );
  };

  const renderTitle = () => (
    <h6 className={`box-title font-medium ${sizeClasses.titleSize} ${titleClassName} group-hover:text-primary transition-colors`}>
      {title}
      {badge && (
        <SpkBadge className={badge.className} variant={badge.color}>
          {badge.text}
        </SpkBadge>
      )}
    </h6>
  );

  const renderDescription = () => {
    if (!showDescription || !description) return null;

    return (
      <p className={`card-text ${sizeClasses.descriptionSize} text-text-secondary ${descriptionClassName}`}>
        {description}
      </p>
    );
  };

  const renderButton = () => {
    if (!showButton || !buttonText || !href) return null;

    return (
      <Link
        href={href}
        className={`ti-btn ti-btn-${buttonColor} hover:scale-105 transition-transform duration-200`}
      >
        {buttonText}
      </Link>
    );
  };

  const renderContent = () => (
    <>
      {/* Header Section */}
      {headerClassName && (
        <div className={`box-header ${headerClassName}`}>
          {/* Custom header content can be added here */}
        </div>
      )}

      {/* Image Section */}
      {renderImage()}

      {/* Clickable overlay for full card navigation */}
      {isClickable && href && (
        <Link href={href} className="card-anchor absolute inset-0 z-10" aria-label="Navigate to card content" />
      )}

      {/* Body Section */}
      <div className={`box-body ${sizeClasses.padding} ${sizeClasses.spacing} ${bodyClassName}`}>
        {renderTitle()}
        {renderDescription()}
        {children}
        {renderButton()}
      </div>

      {/* Footer Section */}
      {footerClassName && (
        <div className={`box-footer ${footerClassName}`}>
          {/* Custom footer content can be added here */}
        </div>
      )}
    </>
  );

  return (
    <div className={cardClasses} onClick={handleCardClick}>
      {renderContent()}
    </div>
  );
};

export default GridCard;
