"use client";

import React from 'react';
import { SpkLoadingSpinner } from '../components';

export interface StatisticsCardProps {
  title: string;
  value: string | number;
  icon?: string;
  iconComponent?: React.ReactNode;
  backgroundColor?: string;
  iconBackgroundColor?: string;
  textColor?: string;
  isLoading?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  percentageChange?: {
    value: number;
    isIncrease: boolean;
    label?: string;
  };
  additionalInfo?: string;
  showAvatar?: boolean;
  avatarVariant?: 'rounded' | 'circle';
}

/**
 * StatisticsCard Component
 * 
 * A reusable card component for displaying dashboard statistics and metrics.
 * Consolidates the spk-cards, spk-card1, and widget card patterns.
 * 
 * Features:
 * - Dashboard-style layout with icon and statistics
 * - Customizable colors and backgrounds
 * - Percentage change indicators with trend arrows
 * - Avatar-style icon containers
 * - Loading states with spinner
 * - Multiple size variants
 * - Clickable cards with hover effects
 * - Full dark theme compatibility
 * - TypeScript interfaces for type safety
 */
const StatisticsCard: React.FC<StatisticsCardProps> = ({
  title,
  value,
  icon,
  iconComponent,
  backgroundColor = 'bg-background',
  iconBackgroundColor = 'bg-primary',
  textColor = 'text-text-muted',
  isLoading = false,
  className = '',
  size = 'md',
  onClick,
  percentageChange,
  additionalInfo,
  showAvatar = true,
  avatarVariant = 'rounded'
}) => {
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return val.toLocaleString();
    }
    return val;
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          padding: 'p-3',
          titleSize: 'text-xs',
          valueSize: 'text-lg',
          iconSize: 'text-sm',
          avatarSize: 'avatar-sm',
          spacing: 'gap-2'
        };
      case 'lg':
        return {
          padding: 'p-6',
          titleSize: 'text-base',
          valueSize: 'text-3xl',
          iconSize: 'text-xl',
          avatarSize: 'avatar-lg',
          spacing: 'gap-4'
        };
      case 'md':
      default:
        return {
          padding: 'p-4',
          titleSize: 'text-sm',
          valueSize: 'text-2xl',
          iconSize: 'text-lg',
          avatarSize: 'avatar-md',
          spacing: 'gap-3'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  const cardClasses = [
    'box',
    backgroundColor,
    onClick ? 'cursor-pointer hover:shadow-lg transition-all duration-300 group' : '',
    className
  ].filter(Boolean).join(' ');

  const avatarClasses = [
    'avatar',
    sizeClasses.avatarSize,
    avatarVariant === 'rounded' ? 'avatar-rounded' : '',
    iconBackgroundColor,
    'svg-white'
  ].filter(Boolean).join(' ');

  if (isLoading) {
    return (
      <div className={cardClasses}>
        <div className="box-body">
          <div className="flex items-center justify-center h-24">
            <SpkLoadingSpinner size="md" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cardClasses} onClick={onClick}>
      <div className="box-body">
        <div className={`flex items-start justify-between ${sizeClasses.spacing}`}>
          <div className="grow">
            {percentageChange && (
              <span className={`inline-block badge bg-${percentageChange.isIncrease ? 'success' : 'danger'}/10 text-${percentageChange.isIncrease ? 'success' : 'danger'} font-semibold me-1 mb-2`}>
                <i className={`fe fe-arrow-${percentageChange.isIncrease ? 'up' : 'down'} text-[0.75rem]`}></i>
                {percentageChange.value}%
                {percentageChange.label && ` ${percentageChange.label}`}
              </span>
            )}
            
            <span className={`${sizeClasses.titleSize} text-textmuted dark:text-textmuted/50 block mb-2`}>
              {title}
            </span>
            
            <h4 className={`${sizeClasses.valueSize} font-medium ${textColor} mb-2 group-hover:scale-105 transition-transform duration-200`}>
              {formatValue(value)}
            </h4>
            
            {additionalInfo && (
              <p className={`${sizeClasses.titleSize} text-textmuted dark:text-textmuted/50 mb-0`}>
                {additionalInfo}
              </p>
            )}
          </div>
          
          {showAvatar && (icon || iconComponent) && (
            <div className="leading-none">
              <span className={`${avatarClasses} group-hover:scale-110 transition-transform duration-200`}>
                {iconComponent || (
                  <i className={`${icon} ${sizeClasses.iconSize}`}></i>
                )}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatisticsCard;
