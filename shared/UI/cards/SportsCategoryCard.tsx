"use client";

import React from "react";
import { SportsCategoryCardProps } from "@/shared/types/sportsbook-types";
import Image from "next/image";

// Re-export the type for convenience
export type { SportsCategoryCardProps };

/**
 * Sports category card component with specified styling
 * Displays sport icon and name with gradient background and hover effects
 */
export const SportsCategoryCard: React.FC<SportsCategoryCardProps> = ({
  discipline,
  onClick,
  className = "",
}) => {
  const handleClick = () => {
    onClick(discipline);
  };

  const getPlaceholderIcon = (sportName: string) => {
    const iconMap: Record<string, string> = {
      soccer: "/assets/sportlist_icons/soccer.svg",
      football: "/assets/sportlist_icons/GaelicFootball.svg",
      basketball: "/assets/sportlist_icons/Basketball.svg",
      tennis: "/assets/sportlist_icons/Racket.svg",
      baseball: "/assets/sportlist_icons/BowlingBall.svg",
      hockey: "/assets/sportlist_icons/Hockey.svg",
      golf: "/assets/sportlist_icons/Golf.svg",
      volleyball: "/assets/sportlist_icons/Volleyball.svg",
      cricket: "🏏",
      boxing: "/assets/sportlist_icons/BoxingGloves.svg",
      swimming: "🏊",
      cycling: "🚴",
      running: "🏃",
      wrestling: "🤼",
      badminton: "/assets/sportlist_icons/Bats.svg",
      "table tennis": "/assets/sportlist_icons/Bats.svg",
      skiing: "/assets/sportlist_icons/Baithlon.svg",
      snowboarding: "/assets/sportlist_icons/Baithlon.svg",
      surfing: "/assets/sportlist_icons/Baithlon.svg",
      climbing: "🧗",
    };

    const lowerName = sportName.toLowerCase();
    for (const [key, icon] of Object.entries(iconMap)) {
      if (lowerName.includes(key)) {
        return icon;
      }
    }
    return "🏆";
  };

  const icon = getPlaceholderIcon(discipline.name);

  return (
    <div
      className={`
        flex flex-col items-center justify-center gap-[16.41px]
        w-[195.71px] h-[182.56px]
        rounded-[20.51px]
        px-[16.41px]
        border-2 border-[#FFFFFF1A]
        cursor-pointer
        transition-all duration-200
        hover:scale-105 hover:border-[var(--golden)]/50
        ${className}
      `}
      style={{
        background:
          "linear-gradient(193.45deg, #1D1C16 9.62%, #2D2100 90.24%)",
        boxShadow:
          "0px 4px 12px 0px #0000008C, 4px 4px 8px 0px #FFD37426 inset",
      }}
      onClick={handleClick}
    >
      {/* Sports Icon */}
      <div
        className="flex items-center justify-center text-4xl"
        style={{
          width: "57.44px",
          height: "57.44px",
        }}
      >
        {
          // Render emoji directly or use <img> for SVG
          typeof icon === "string" && icon.endsWith(".svg") ? (
            <Image width={57} height={57} src={icon} alt={discipline.name} className="w-full h-full object-contain" />
          ) : (
            icon
          )
        }
      </div>

      {/* Sports Title */}
      <div
        className="text-center text-white font-rubik font-bold"
        style={{
          fontSize: "20px",
          lineHeight: "100%",
        }}
      >
        {discipline.name}
      </div>
    </div>
  );
};

export default SportsCategoryCard;
