"use client";

import React from 'react';
import { <PERSON><PERSON><PERSON>oader, type SVGIconName } from '../components/icons';
import { SpkLoadingSpinner, CurrencyDisplay } from '../components';

export interface ReportSummaryCardData {
  /** Display label for the card */
  label: string;
  /** Numeric or string value to display */
  value: string | number;
  /** Card type for automatic icon mapping */
  type: 'bet-amount' | 'win-amount' | 'ggr' | 'total-deposit' | 'total-withdraw' | 'total-bet-placed' | 'balance-report';
}

export interface ReportSummaryCardsProps {
  /** Array of card data objects */
  cards: ReportSummaryCardData[];
  /** Background class variant - determines texture and styling */
  backgroundType?: 'general' | 'cashier';
  /** Loading state for all cards */
  isLoading?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Grid columns (default: 3) */
  gridColumns?: 1 | 2 | 3 | 4;
  /** Card height (default: 130px) */
  height?: string;
}

/**
 * ReportSummaryCards Component
 *
 * A reusable component for displaying summary statistics cards in report pages.
 * Consolidates the card patterns used across bet-report, cashier-report, and financial-report pages.
 *
 * Features:
 * - Data-driven approach with configurable card array
 * - Automatic SVG icon mapping based on card type
 * - Multiple background types (general reports vs cashier reports)
 * - Consistent text-white styling for all amounts
 * - Responsive grid layout with configurable columns
 * - Loading states with spinner
 * - Consistent styling matching existing card patterns
 * - Full dark theme compatibility
 * - TypeScript interfaces for type safety
 * - Type-safe icon loading from appropriate directories
 *
 * Background Types:
 * - 'general': Uses bg-card-general-texture (bet-report, financial-report)
 * - 'cashier': Uses bg-texture (cashier-report, user-details)
 *
 * Icon Mapping:
 * - Automatically maps card types to corresponding SVG files
 * - bet-amount → betamount.svg (from betReports/)
 * - win-amount → totalwin.svg (from betReports/)
 * - ggr → totalggr.svg (from betReports/)
 * - total-deposit → totalDeposite.svg (from cashierReports/ or financialReports/)
 * - total-withdraw → totalWithdraw.svg (from cashierReports/ or financialReports/)
 * - total-bet-placed → totalBetPlaced.svg (from cashierReports/)
 * - balance-report → balanceReport.svg (from financialReports/)
 */
const ReportSummaryCards: React.FC<ReportSummaryCardsProps> = ({
  cards,
  backgroundType = 'general',
  isLoading = false,
  className = '',
  gridColumns = 3,
  height = '130px'
}) => {
  // Map card types to their corresponding SVG file names
  const getIconName = (type: ReportSummaryCardData['type']): SVGIconName => {
    const iconMap: Record<ReportSummaryCardData['type'], SVGIconName> = {
      'bet-amount': 'betamount',
      'win-amount': 'totalwin',
      'ggr': 'totalggr',
      'total-deposit': 'totalDeposite',
      'total-withdraw': 'totalWithdraw',
      'total-bet-placed': 'totalBetPlaced',
      'balance-report': 'balanceReport'
    };
    return iconMap[type];
  };

  // Get icon size based on card type (80px for bet reports, 100px for financial/cashier reports)
  const getIconSize = (type: ReportSummaryCardData['type']) => {
    const betReportTypes: ReportSummaryCardData['type'][] = ['bet-amount', 'win-amount', 'ggr'];
    return betReportTypes.includes(type) ? 80 : 100;
  };

  // Get padding class based on card type (py-[28px] for bet reports, py-[13px] for financial/cashier reports)
  const getPaddingClass = (type: ReportSummaryCardData['type']) => {
    const betReportTypes: ReportSummaryCardData['type'][] = ['bet-amount', 'win-amount', 'ggr'];
    return betReportTypes.includes(type) ? 'py-[28px]' : 'py-[13px]';
  };

  // Check if card type should display currency
  const shouldShowCurrency = (type: ReportSummaryCardData['type']): boolean => {
    const currencyTypes: ReportSummaryCardData['type'][] = [
      'bet-amount', 'win-amount', 'ggr', 'total-deposit', 'total-withdraw', 'balance-report'
    ];
    return currencyTypes.includes(type);
  };

  // Get background class based on type
  const getBackgroundClass = () => {
    switch (backgroundType) {
      case 'cashier':
        return 'bg-texture';
      case 'general':
      default:
        return 'bg-card-general-texture';
    }
  };

  // Get grid columns class
  const getGridColumnsClass = () => {
    switch (gridColumns) {
      case 1:
        return 'grid-cols-1';
      case 2:
        return 'grid-cols-2';
      case 4:
        return 'grid-cols-4';
      case 3:
      default:
        return 'grid-cols-3';
    }
  };

  const backgroundClass = getBackgroundClass();
  const gridClass = getGridColumnsClass();

  // Loading state
  if (isLoading) {
    return (
      <div className={`grid ${gridClass} gap-4 ${className}`} style={{ height }}>
        {Array.from({ length: cards.length || 3 }).map((_, index) => (
          <div key={index} className={`${backgroundClass} flex gap-[12px] px-[24px] py-[28px] h-full rounded-lg`}>
            <div className="flex items-center justify-center h-full w-full">
              <SpkLoadingSpinner size="md" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid ${gridClass} gap-4 ${className}`} style={{ height }}>
      {cards.map((card, index) => {
        const iconSize = getIconSize(card.type);
        const paddingClass = getPaddingClass(card.type);
        return (
          <div key={index} className={`${backgroundClass} flex gap-[12px] px-[24px] ${paddingClass} gap-[1rem] h-full rounded-lg`}>
            {/* Icon Container */}
            <div>
              <SVGLoader
                name={getIconName(card.type)}
                size={iconSize}
                className={`w-[${iconSize}px] h-[${iconSize}px]`}
                fallback={
                  <div className={`w-[${iconSize}px] h-[${iconSize}px] bg-gray-500 rounded-lg flex items-center justify-center text-white text-xs`}>
                    Icon
                  </div>
                }
              />
            </div>

            {/* Text Content */}
            <div className="flex flex-col items-start justify-center">
              <div className="text-sm text-text-secondary">{card.label}</div>
              <div className="text-2xl font-bold text-white">
                {shouldShowCurrency(card.type) ? (
                  <CurrencyDisplay
                    amount={card.value}
                    context="card"
                    size={20}
                    amountClassName="text-2xl font-bold"
                    gap="sm"
                  />
                ) : (
                  <span>{typeof card.value === 'number' ? card.value.toLocaleString() : card.value}</span>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ReportSummaryCards;
