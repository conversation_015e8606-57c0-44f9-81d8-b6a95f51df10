import React from 'react';
import PrimaryButton from './PrimaryButton';

export interface SpkPrimaryButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xsm';
  variant?: 'primary' | 'secondary';
  loading?: boolean;
  loadingText?: string;
  icon?: string;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  id?: string;
  'aria-label'?: string;
  title?: string;
}

/**
 * SpkPrimaryButton Component (Legacy Wrapper)
 *
 * @deprecated This component is maintained for backward compatibility.
 * For new development, use PrimaryButton directly from '@/shared/UI/components'.
 *
 * This component wraps the PrimaryButton component to maintain backward compatibility
 * while eliminating code duplication. It provides the same API as the original
 * SpkPrimaryButton but uses the more comprehensive PrimaryButton implementation internally.
 *
 * Features:
 * - Exact golden gradient: linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)
 * - Multi-layered box shadows matching SignInFormUI exactly
 * - Loading state with spinner
 * - Icon support with positioning
 * - Multiple sizes and variants
 * - Full accessibility support
 */
const SpkPrimaryButton: React.FC<SpkPrimaryButtonProps> = ({
  children,
  onClick,
  disabled = false,
  type = 'button',
  className = '',
  size = 'md',
  variant = 'primary',
  loading = false,
  loadingText,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  id,
  'aria-label': ariaLabel,
  title,
}) => {
  // Map SpkPrimaryButton size to PrimaryButton size
  const mapSize = (spkSize: string) => {
    switch (spkSize) {
      case 'xsm':
        return 'xsm' as const;
      case 'sm':
        return 'sm' as const;
      case 'lg':
        return 'lg' as const;
      case 'md':
      default:
        return undefined; // Use PrimaryButton default
    }
  };

  // Create icon prop for PrimaryButton if icon is provided
  const iconProp = icon ? {
    type: 'FONT_ICON' as const,
    iconClass: icon,
    library: 'remix' as const
  } : undefined;

  // For secondary variant, we'll use a custom className since PrimaryButton doesn't have variants
  const customClassName = variant === 'secondary'
    ? `bg-gradient-to-r from-gray-500 to-gray-600 shadow-md hover:shadow-lg ${className}`
    : className;

  return (
    <PrimaryButton
      as="button"
      onClick={onClick}
      disabled={disabled}
      type={type}
      className={customClassName}
      size={mapSize(size)}
      loading={loading}
      loadingText={loadingText}
      icon={iconProp}
      iconPosition={iconPosition}
      fullWidth={fullWidth}
      id={id}
      aria-label={ariaLabel}
      title={title}
    >
      {children}
    </PrimaryButton>
  );
};

export default SpkPrimaryButton;
