"use client";

import React from 'react';
import PrimaryButton from './PrimaryButton';

/**
 * Demo component showcasing the new size variants of PrimaryButton
 * 
 * This component demonstrates all the size variants with and without icons
 * to help developers understand the visual differences and choose the right size.
 */
const PrimaryButtonSizeDemo: React.FC = () => {
  const handleClick = () => {
    // Button clicked - demo functionality
  };

  const iconProps = {
    type: 'SVG' as const,
    url: '/assets/icon-fonts/tabler-icons/icons/plus.svg',
  };

  return (
    <div className="p-8 bg-background min-h-screen">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold text-white mb-8">PrimaryButton Size Variants</h1>
        
        {/* Size Variants without Icons */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold text-white">Size Variants (Text Only)</h2>
          <div className="flex flex-wrap gap-4 items-center">
            <PrimaryButton size="xsm" onClick={handleClick}>
              Extra Small
            </PrimaryButton>
            <PrimaryButton size="sm" onClick={handleClick}>
              Small
            </PrimaryButton>
            <PrimaryButton onClick={handleClick}>
              Default
            </PrimaryButton>
            <PrimaryButton size="lg" onClick={handleClick}>
              Large
            </PrimaryButton>
          </div>
        </section>

        {/* Size Variants with Left Icons */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold text-white">Size Variants (With Left Icons)</h2>
          <div className="flex flex-wrap gap-4 items-center">
            <PrimaryButton size="xsm" icon={iconProps} iconPosition="left" onClick={handleClick}>
              Extra Small
            </PrimaryButton>
            <PrimaryButton size="sm" icon={iconProps} iconPosition="left" onClick={handleClick}>
              Small
            </PrimaryButton>
            <PrimaryButton icon={iconProps} iconPosition="left" onClick={handleClick}>
              Default
            </PrimaryButton>
            <PrimaryButton size="lg" icon={iconProps} iconPosition="left" onClick={handleClick}>
              Large
            </PrimaryButton>
          </div>
        </section>

        {/* Size Variants with Right Icons */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold text-white">Size Variants (With Right Icons)</h2>
          <div className="flex flex-wrap gap-4 items-center">
            <PrimaryButton size="xsm" icon={iconProps} iconPosition="right" onClick={handleClick}>
              Extra Small
            </PrimaryButton>
            <PrimaryButton size="sm" icon={iconProps} iconPosition="right" onClick={handleClick}>
              Small
            </PrimaryButton>
            <PrimaryButton icon={iconProps} iconPosition="right" onClick={handleClick}>
              Default
            </PrimaryButton>
            <PrimaryButton size="lg" icon={iconProps} iconPosition="right" onClick={handleClick}>
              Large
            </PrimaryButton>
          </div>
        </section>

        {/* Loading States */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold text-white">Loading States</h2>
          <div className="flex flex-wrap gap-4 items-center">
            <PrimaryButton size="xsm" loading loadingText="Loading..." onClick={handleClick}>
              Extra Small
            </PrimaryButton>
            <PrimaryButton size="sm" loading loadingText="Loading..." onClick={handleClick}>
              Small
            </PrimaryButton>
            <PrimaryButton loading loadingText="Loading..." onClick={handleClick}>
              Default
            </PrimaryButton>
            <PrimaryButton size="lg" loading loadingText="Loading..." onClick={handleClick}>
              Large
            </PrimaryButton>
          </div>
        </section>

        {/* Full Width Variants */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold text-white">Full Width Variants</h2>
          <div className="space-y-4">
            <PrimaryButton size="xsm" fullWidth onClick={handleClick}>
              Extra Small Full Width
            </PrimaryButton>
            <PrimaryButton size="sm" fullWidth onClick={handleClick}>
              Small Full Width
            </PrimaryButton>
            <PrimaryButton fullWidth onClick={handleClick}>
              Default Full Width
            </PrimaryButton>
            <PrimaryButton size="lg" fullWidth onClick={handleClick}>
              Large Full Width
            </PrimaryButton>
          </div>
        </section>

        {/* Size Specifications */}
        <section className="space-y-6">
          <h2 className="text-xl font-semibold text-white">Size Specifications</h2>
          <div className="bg-section rounded-lg p-6 text-white">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <h3 className="font-bold text-primary">Extra Small (xsm)</h3>
                <ul className="text-sm space-y-1">
                  <li>Font: Rubik 500, 14px</li>
                  <li>Padding: 10px/12px</li>
                  <li>Gap: 8px</li>
                  <li>Radius: 8px</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h3 className="font-bold text-primary">Small (sm)</h3>
                <ul className="text-sm space-y-1">
                  <li>Font: Rubik 700, 14px</li>
                  <li>Padding: 10px/24px</li>
                  <li>Gap: 4px</li>
                  <li>Radius: 8px</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h3 className="font-bold text-primary">Default</h3>
                <ul className="text-sm space-y-1">
                  <li>Font: Rubik 700, 18px</li>
                  <li>Padding: 12px/16px</li>
                  <li>Gap: 4px</li>
                  <li>Radius: 8px</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h3 className="font-bold text-primary">Large (lg)</h3>
                <ul className="text-sm space-y-1">
                  <li>Font: Rubik 700, 18px</li>
                  <li>Padding: 12px/16px</li>
                  <li>Gap: 8px</li>
                  <li>Radius: 8px</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default PrimaryButtonSizeDemo;
