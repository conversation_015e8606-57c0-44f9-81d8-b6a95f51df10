import React from "react";
import { SpkAlert } from "@/shared/UI/components";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

export interface SpkErrorMessageProps {
	message: string;
	onRetry?: () => void;
	className?: string;
	title?: string;
	variant?: "alert" | "box";
	size?: "sm" | "md" | "lg";
}

const SpkErrorMessage: React.FC<SpkErrorMessageProps> = ({
	message,
	onRetry,
	className = "",
	title = "Something went wrong",
	variant = "alert",
	size = "md"
}) => {
	const getSizeClasses = () => {
		switch (size) {
			case "sm":
				return {
					container: "py-4",
					icon: "text-4xl mb-2",
					iconContainer: "w-12 h-12",
					title: "text-sm font-medium",
					message: "text-xs",
					button: "ti-btn-xs"
				};
			case "lg":
				return {
					container: "py-12",
					icon: "text-8xl mb-6",
					iconContainer: "w-20 h-20",
					title: "text-xl font-semibold",
					message: "text-base",
					button: "ti-btn-lg"
				};
			default:
				return {
					container: "py-8",
					icon: "text-6xl mb-4",
					iconContainer: "w-16 h-16",
					title: "text-base font-medium",
					message: "text-sm",
					button: "ti-btn-sm"
				};
		}
	};

	const sizeClasses = getSizeClasses();

	if (variant === "alert") {
		const errorIcon = (
			<div className={`text-danger-500 ${sizeClasses.icon}`}>
				<i className="ri-error-warning-line"></i>
			</div>
		);

		const retryAction = onRetry ? (
			<SpkButton
				type="button"
				customClass={`ti-btn ti-btn-primary ${sizeClasses.button}`}
				onclickfunc={onRetry}
			>
				<i className="ri-refresh-line me-2"></i>
				Try Again
			</SpkButton>
		) : undefined;

		return (
			<div className={`text-center ${sizeClasses.container} ${className}`}>
				<SpkAlert
					variant="danger"
					style="modern"
					bordered={true}
					borderPosition="left"
					icon={errorIcon}
					title={title}
					actions={retryAction}
					className="max-w-md mx-auto"
				>
					{message}
				</SpkAlert>
			</div>
		);
	}

	// Box variant
	return (
		<div className={`box ${className}`}>
			<div className="box-body">
				<div className={`text-center ${sizeClasses.container}`}>
					<div className={`${sizeClasses.iconContainer} mx-auto mb-4 bg-danger-50 dark:bg-danger-500/20 rounded-full flex items-center justify-center`}>
						<i className="ri-error-warning-line text-2xl text-danger-500"></i>
					</div>
					<h6 className={`${sizeClasses.title} text-white mb-2`}>
						{title}
					</h6>
					<p className={`${sizeClasses.message} text-text-secondary mb-4`}>
						{message}
					</p>
					{onRetry && (
						<SpkButton
							type="button"
							customClass={`ti-btn ti-btn-primary ${sizeClasses.button}`}
							onclickfunc={onRetry}
						>
							<i className="ri-refresh-line me-1"></i>
							Try Again
						</SpkButton>
					)}
				</div>
			</div>
		</div>
	);
};

export default SpkErrorMessage;
