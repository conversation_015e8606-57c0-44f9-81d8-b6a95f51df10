import React from 'react';

export interface FormSkeletonProps {
  fields?: number;
  showTitle?: boolean;
  showActions?: boolean;
  layout?: 'single' | 'double' | 'triple';
  className?: string;
}

/**
 * FormSkeleton Component
 *
 * A reusable skeleton component for form loading states.
 * Supports different field layouts and configurations.
 * Updated to use dark theme design system colors.
 */
const FormSkeleton: React.FC<FormSkeletonProps> = ({
  fields = 6,
  showTitle = true,
  showActions = true,
  layout = 'double',
  className = ''
}) => {
  const getGridClasses = () => {
    switch (layout) {
      case 'single':
        return 'grid grid-cols-1 gap-4';
      case 'double':
        return 'grid grid-cols-1 md:grid-cols-2 gap-4';
      case 'triple':
        return 'grid grid-cols-1 md:grid-cols-3 gap-4';
      default:
        return 'grid grid-cols-1 md:grid-cols-2 gap-4';
    }
  };

  return (
    <div className={`animate-pulse space-y-6 ${className}`}>
      {/* Form Title Skeleton */}
      {showTitle && (
        <div className="h-6 bg-[#404040] rounded w-1/4 mb-4"></div>
      )}

      {/* Form Fields Skeleton */}
      <div className={getGridClasses()}>
        {Array.from({ length: fields }).map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="h-4 bg-[#404040] rounded w-1/3"></div>
            <div className="h-10 bg-[#333333] rounded"></div>
          </div>
        ))}
      </div>

      {/* Form Actions Skeleton */}
      {showActions && (
        <div className="flex gap-2 pt-4">
          <div className="h-10 bg-[#404040] rounded w-24"></div>
          <div className="h-10 bg-[#404040] rounded w-24"></div>
        </div>
      )}
    </div>
  );
};

export default FormSkeleton;
