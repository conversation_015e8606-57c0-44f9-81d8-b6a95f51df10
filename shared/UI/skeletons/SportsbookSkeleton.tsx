import React from 'react';

export interface SportsbookSkeletonProps {
  type?: 'bet-slip' | 'odds-grid' | 'match-card' | 'live-scores' | 'bet-history';
  itemCount?: number;
  showHeader?: boolean;
  className?: string;
}

/**
 * SportsbookSkeleton Component
 * 
 * A specialized skeleton component for sportsbook loading states.
 * Matches sportsbook layouts and dark theme design system.
 */
const SportsbookSkeleton: React.FC<SportsbookSkeletonProps> = ({
  type = 'bet-slip',
  itemCount = 3,
  showHeader = true,
  className = ''
}) => {
  const renderBetSlipSkeleton = () => (
    <div className="bg-elevated rounded-lg border border-border-primary p-4 w-80 animate-pulse">
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between mb-4 pb-3 border-b border-border-secondary">
          <div className="h-5 bg-[#404040] rounded w-24"></div>
          <div className="h-4 bg-[#404040] rounded w-16"></div>
        </div>
      )}

      {/* Bet Items */}
      <div className="space-y-3 mb-4">
        {Array.from({ length: itemCount }).map((_, i) => (
          <div key={i} className="bg-section rounded p-3 space-y-2">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="h-4 bg-[#404040] rounded w-32 mb-1"></div>
                <div className="h-3 bg-[#333333] rounded w-24"></div>
              </div>
              <div className="h-6 bg-[#404040] rounded w-12"></div>
            </div>
            <div className="h-8 bg-[#333333] rounded w-full"></div>
          </div>
        ))}
      </div>

      {/* Total Section */}
      <div className="border-t border-border-secondary pt-3">
        <div className="flex justify-between items-center mb-2">
          <div className="h-4 bg-[#404040] rounded w-20"></div>
          <div className="h-5 bg-[#404040] rounded w-16"></div>
        </div>
        <div className="h-10 bg-[#404040] rounded w-full"></div>
      </div>
    </div>
  );

  const renderOddsGridSkeleton = () => (
    <div className="bg-elevated rounded-lg border border-border-primary animate-pulse">
      {/* Header */}
      {showHeader && (
        <div className="bg-section px-4 py-3 border-b border-border-primary rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="h-5 bg-[#404040] rounded w-32"></div>
            <div className="h-4 bg-[#404040] rounded w-20"></div>
          </div>
        </div>
      )}

      {/* Odds Grid */}
      <div className="p-4">
        <div className="grid grid-cols-3 gap-2 mb-4">
          <div className="h-4 bg-[#404040] rounded"></div>
          <div className="h-4 bg-[#404040] rounded"></div>
          <div className="h-4 bg-[#404040] rounded"></div>
        </div>
        
        {Array.from({ length: itemCount }).map((_, i) => (
          <div key={i} className="grid grid-cols-3 gap-2 mb-2">
            <div className="h-8 bg-[#333333] rounded"></div>
            <div className="h-8 bg-[#333333] rounded"></div>
            <div className="h-8 bg-[#333333] rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderMatchCardSkeleton = () => (
    <div className="bg-elevated rounded-lg border border-border-primary p-4 animate-pulse">
      {/* Match Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-[#404040] rounded-full"></div>
          <div>
            <div className="h-4 bg-[#404040] rounded w-24 mb-1"></div>
            <div className="h-3 bg-[#333333] rounded w-16"></div>
          </div>
        </div>
        <div className="h-6 bg-[#404040] rounded w-16"></div>
      </div>

      {/* Teams */}
      <div className="space-y-3 mb-4">
        {Array.from({ length: 2 }).map((_, i) => (
          <div key={i} className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-[#404040] rounded-full"></div>
              <div className="h-4 bg-[#404040] rounded w-32"></div>
            </div>
            <div className="h-8 bg-[#333333] rounded w-16"></div>
          </div>
        ))}
      </div>

      {/* Additional Markets */}
      <div className="border-t border-border-secondary pt-3">
        <div className="grid grid-cols-3 gap-2">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="h-8 bg-[#333333] rounded"></div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderLiveScoresSkeleton = () => (
    <div className="bg-elevated rounded-lg border border-border-primary animate-pulse">
      {/* Header */}
      {showHeader && (
        <div className="bg-section px-4 py-3 border-b border-border-primary rounded-t-lg">
          <div className="h-5 bg-[#404040] rounded w-28"></div>
        </div>
      )}

      {/* Live Matches */}
      <div className="p-4 space-y-3">
        {Array.from({ length: itemCount }).map((_, i) => (
          <div key={i} className="flex items-center justify-between p-3 bg-section rounded">
            <div className="flex items-center gap-4">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div>
                <div className="h-4 bg-[#404040] rounded w-40 mb-1"></div>
                <div className="h-3 bg-[#333333] rounded w-24"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="h-5 bg-[#404040] rounded w-12 mb-1"></div>
              <div className="h-3 bg-[#333333] rounded w-16"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderBetHistorySkeleton = () => (
    <div className="bg-elevated rounded-lg border border-border-primary animate-pulse">
      {/* Header */}
      {showHeader && (
        <div className="bg-section px-4 py-3 border-b border-border-primary rounded-t-lg">
          <div className="h-5 bg-[#404040] rounded w-32"></div>
        </div>
      )}

      {/* Bet History Items */}
      <div className="p-4 space-y-4">
        {Array.from({ length: itemCount }).map((_, i) => (
          <div key={i} className="border border-border-secondary rounded p-3">
            <div className="flex justify-between items-start mb-3">
              <div>
                <div className="h-4 bg-[#404040] rounded w-36 mb-1"></div>
                <div className="h-3 bg-[#333333] rounded w-24"></div>
              </div>
              <div className="h-6 bg-[#404040] rounded w-16"></div>
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <div className="h-3 bg-[#333333] rounded w-12 mb-1"></div>
                <div className="h-4 bg-[#404040] rounded w-16"></div>
              </div>
              <div>
                <div className="h-3 bg-[#333333] rounded w-16 mb-1"></div>
                <div className="h-4 bg-[#404040] rounded w-20"></div>
              </div>
              <div>
                <div className="h-3 bg-[#333333] rounded w-14 mb-1"></div>
                <div className="h-4 bg-[#404040] rounded w-18"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSkeleton = () => {
    switch (type) {
      case 'bet-slip':
        return renderBetSlipSkeleton();
      case 'odds-grid':
        return renderOddsGridSkeleton();
      case 'match-card':
        return renderMatchCardSkeleton();
      case 'live-scores':
        return renderLiveScoresSkeleton();
      case 'bet-history':
        return renderBetHistorySkeleton();
      default:
        return renderBetSlipSkeleton();
    }
  };

  return (
    <div className={className}>
      {renderSkeleton()}
    </div>
  );
};

export default SportsbookSkeleton;
