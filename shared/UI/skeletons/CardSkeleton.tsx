import React from 'react';

export interface CardSkeletonProps {
  count?: number;
  showAvatar?: boolean;
  showActions?: boolean;
  showStats?: boolean;
  layout?: 'grid' | 'list' | 'single';
  className?: string;
}

/**
 * CardSkeleton Component
 *
 * A reusable skeleton component for card loading states.
 * Supports different layouts and configurations.
 * Updated to use dark theme design system colors.
 */
const CardSkeleton: React.FC<CardSkeletonProps> = ({
  count = 3,
  showAvatar = true,
  showActions = true,
  showStats = false,
  layout = 'grid',
  className = ''
}) => {
  const renderSingleCard = (index: number) => (
    <div key={index} className="bg-elevated rounded-lg border border-border-primary animate-pulse">
      <div className="p-6">
        <div className="flex items-start gap-6">
          {/* Avatar Skeleton */}
          {showAvatar && (
            <div className="w-20 h-20 bg-[#404040] rounded-full flex-shrink-0"></div>
          )}

          <div className="flex-1">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="h-6 bg-[#404040] rounded w-32 mb-2"></div>
                <div className="h-4 bg-[#333333] rounded w-48 mb-2"></div>
                <div className="h-4 bg-[#333333] rounded w-24"></div>
              </div>

              {/* Actions Skeleton */}
              {showActions && (
                <div className="flex items-center gap-2">
                  <div className="h-8 bg-[#404040] rounded w-20"></div>
                  <div className="h-8 bg-[#404040] rounded w-24"></div>
                </div>
              )}
            </div>

            {/* Stats Skeleton */}
            {showStats && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="bg-section rounded-lg p-4 border border-border-secondary">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-[#404040] rounded-lg"></div>
                      <div>
                        <div className="h-3 bg-[#333333] rounded w-16 mb-1"></div>
                        <div className="h-4 bg-[#404040] rounded w-20"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const getLayoutClasses = () => {
    switch (layout) {
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
      case 'list':
        return 'space-y-4';
      case 'single':
        return '';
      default:
        return 'space-y-4';
    }
  };

  if (layout === 'single') {
    return (
      <div className={className}>
        {renderSingleCard(0)}
      </div>
    );
  }

  return (
    <div className={`${getLayoutClasses()} ${className}`}>
      {Array.from({ length: count }).map((_, index) => renderSingleCard(index))}
    </div>
  );
};

export default CardSkeleton;
