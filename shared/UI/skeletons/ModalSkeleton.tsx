import React from 'react';

export interface ModalSkeletonProps {
  type?: 'user-management' | 'wallet-transaction' | 'bet-details' | 'generic';
  showHeader?: boolean;
  showForm?: boolean;
  showActions?: boolean;
  className?: string;
}

/**
 * ModalSkeleton Component
 * 
 * A specialized skeleton component for modal loading states.
 * Matches the modal layouts and dark theme design system.
 */
const ModalSkeleton: React.FC<ModalSkeletonProps> = ({
  type = 'generic',
  showHeader = true,
  showForm = true,
  showActions = true,
  className = ''
}) => {
  const renderUserManagementSkeleton = () => (
    <div className="w-[462px] bg-elevated rounded-lg border border-border-primary animate-pulse">
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between p-4 border-b border-border-primary">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-[#404040] rounded"></div>
            <div className="h-5 bg-[#404040] rounded w-32"></div>
          </div>
          <div className="w-6 h-6 bg-[#404040] rounded"></div>
        </div>
      )}

      {/* Form Content */}
      {showForm && (
        <div className="p-4 space-y-4">
          {/* Form Fields */}
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="h-4 bg-[#404040] rounded w-20"></div>
              <div className="h-10 bg-[#333333] rounded w-full"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-[#404040] rounded w-16"></div>
              <div className="h-10 bg-[#333333] rounded w-full"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-[#404040] rounded w-24"></div>
              <div className="h-10 bg-[#333333] rounded w-full"></div>
            </div>
          </div>

          {/* Toggle Switches */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="h-4 bg-[#404040] rounded w-28"></div>
              <div className="w-12 h-6 bg-[#333333] rounded-full"></div>
            </div>
            <div className="flex items-center justify-between">
              <div className="h-4 bg-[#404040] rounded w-32"></div>
              <div className="w-12 h-6 bg-[#333333] rounded-full"></div>
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      {showActions && (
        <div className="flex justify-end gap-2 p-4 border-t border-border-primary">
          <div className="h-10 bg-[#404040] rounded w-20"></div>
          <div className="h-10 bg-[#404040] rounded w-24"></div>
        </div>
      )}
    </div>
  );

  const renderWalletTransactionSkeleton = () => (
    <div className="w-[462px] bg-elevated rounded-lg border border-border-primary animate-pulse">
      {/* Header with Balance */}
      {showHeader && (
        <div className="p-4 border-b border-border-primary">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-[#404040] rounded"></div>
              <div className="h-5 bg-[#404040] rounded w-40"></div>
            </div>
            <div className="w-6 h-6 bg-[#404040] rounded"></div>
          </div>
          
          {/* Balance Display */}
          <div className="text-center">
            <div className="h-4 bg-[#333333] rounded w-20 mx-auto mb-2"></div>
            <div className="h-8 bg-[#404040] rounded w-32 mx-auto"></div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="p-4 pb-0">
        <div className="flex bg-section rounded">
          <div className="flex-1 h-10 bg-[#404040] rounded-l"></div>
          <div className="flex-1 h-10 bg-[#333333] rounded-r"></div>
        </div>
      </div>

      {/* Form Content */}
      {showForm && (
        <div className="p-4 space-y-4">
          {/* User Selection */}
          <div className="space-y-2">
            <div className="h-4 bg-[#404040] rounded w-24"></div>
            <div className="h-10 bg-[#333333] rounded w-full"></div>
          </div>

          {/* Amount Input */}
          <div className="space-y-2">
            <div className="h-4 bg-[#404040] rounded w-16"></div>
            <div className="h-10 bg-[#333333] rounded w-full"></div>
          </div>

          {/* Submit Button */}
          <div className="h-12 bg-[#404040] rounded w-full"></div>
        </div>
      )}
    </div>
  );

  const renderBetDetailsSkeleton = () => (
    <div className="w-80 bg-elevated rounded-lg border border-border-primary animate-pulse">
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between p-3 border-b border-border-primary">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-[#404040] rounded"></div>
            <div className="h-4 bg-[#404040] rounded w-24"></div>
          </div>
          <div className="w-5 h-5 bg-[#404040] rounded"></div>
        </div>
      )}

      {/* Content */}
      <div className="p-4 space-y-3">
        {/* Loading State */}
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-2 border-[#404040] border-t-primary"></div>
          <div className="ml-2 h-4 bg-[#333333] rounded w-32"></div>
        </div>

        {/* Details Skeleton */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <div className="h-3 bg-[#404040] rounded w-16"></div>
            <div className="h-5 bg-[#333333] rounded w-12"></div>
          </div>
          <div className="flex justify-between items-center">
            <div className="h-3 bg-[#404040] rounded w-20"></div>
            <div className="h-4 bg-[#333333] rounded w-24"></div>
          </div>
          <div className="flex justify-between items-center">
            <div className="h-3 bg-[#404040] rounded w-18"></div>
            <div className="h-4 bg-[#333333] rounded w-16"></div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderGenericSkeleton = () => (
    <div className="bg-elevated rounded-lg border border-border-primary p-6 animate-pulse">
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between mb-4 pb-4 border-b border-border-primary">
          <div className="h-5 bg-[#404040] rounded w-32"></div>
          <div className="w-6 h-6 bg-[#404040] rounded"></div>
        </div>
      )}

      {/* Content */}
      <div className="space-y-4">
        <div className="h-4 bg-[#333333] rounded w-3/4"></div>
        <div className="h-4 bg-[#333333] rounded w-1/2"></div>
        <div className="h-32 bg-[#404040] rounded"></div>
      </div>

      {/* Actions */}
      {showActions && (
        <div className="flex justify-end gap-2 mt-6 pt-4 border-t border-border-primary">
          <div className="h-10 bg-[#404040] rounded w-20"></div>
          <div className="h-10 bg-[#404040] rounded w-24"></div>
        </div>
      )}
    </div>
  );

  const renderSkeleton = () => {
    switch (type) {
      case 'user-management':
        return renderUserManagementSkeleton();
      case 'wallet-transaction':
        return renderWalletTransactionSkeleton();
      case 'bet-details':
        return renderBetDetailsSkeleton();
      default:
        return renderGenericSkeleton();
    }
  };

  return (
    <div className={className}>
      {renderSkeleton()}
    </div>
  );
};

export default ModalSkeleton;
