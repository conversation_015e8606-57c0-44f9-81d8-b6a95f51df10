import React from 'react';

export interface UserStatisticsSkeletonProps {
  showProfileHeader?: boolean;
  showStatisticsCards?: boolean;
  className?: string;
}

/**
 * UserStatisticsSkeleton Component
 * 
 * A specialized skeleton component for user statistics and profile sections.
 * Matches the user details page layout and dark theme design system.
 */
const UserStatisticsSkeleton: React.FC<UserStatisticsSkeletonProps> = ({
  showProfileHeader = true,
  showStatisticsCards = true,
  className = ''
}) => {
  return (
    <div className={`space-y-5 animate-pulse ${className}`}>
      {/* User Profile Header Skeleton */}
      {showProfileHeader && (
        <div className="h-auto lg:h-[144px] bg-[#333333] rounded-[1rem] px-3 py-4 flex flex-col lg:flex-row justify-between gap-4">
          {/* Profile Section */}
          <div className="flex items-center gap-4">
            {/* Profile Image */}
            <div className="w-20 h-20 rounded-full bg-[#404040]"></div>
            
            {/* User Details */}
            <div className="flex flex-col gap-2">
              {/* Username */}
              <div className="h-6 bg-[#404040] rounded w-32"></div>
              
              {/* User Metadata */}
              <div className="flex flex-col lg:flex-row lg:items-center gap-2 lg:gap-4">
                <div className="h-4 bg-[#333333] rounded w-20"></div>
                <div className="h-4 bg-[#333333] rounded w-24"></div>
                <div className="h-4 bg-[#333333] rounded w-28"></div>
              </div>
            </div>
          </div>
          
          {/* Wallet Balance Section */}
          <div className="flex flex-col justify-center items-center p-4 h-full w-[250px] bg-[#404040] rounded-lg min-h-[80px]">
            <div className="h-8 bg-[#333333] rounded w-24 mb-2"></div>
            <div className="h-4 bg-[#333333] rounded w-20"></div>
          </div>
        </div>
      )}

      {/* Statistics Cards Skeleton */}
      {showStatisticsCards && (
        <div className="h-auto lg:h-[182px] flex flex-col lg:flex-row gap-3 bg-[#333333] rounded-[16px] p-[12px]">
          {/* Statistics Card 1 - Overall Deposit */}
          <div className="flex-1 bg-[#404040] rounded-lg p-4">
            <div className="flex items-center gap-4 h-full">
              {/* Icon */}
              <div className="w-12 h-12 bg-[#333333] rounded-lg flex-shrink-0"></div>
              
              {/* Content */}
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-[#333333] rounded w-24"></div>
                <div className="h-6 bg-[#333333] rounded w-20"></div>
                <div className="h-8 bg-[#333333] rounded w-16"></div>
              </div>
            </div>
          </div>

          {/* Statistics Card 2 - Overall Withdraw */}
          <div className="flex-1 bg-[#404040] rounded-lg p-4">
            <div className="flex items-center gap-4 h-full">
              {/* Icon */}
              <div className="w-12 h-12 bg-[#333333] rounded-lg flex-shrink-0"></div>
              
              {/* Content */}
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-[#333333] rounded w-28"></div>
                <div className="h-6 bg-[#333333] rounded w-20"></div>
                <div className="h-8 bg-[#333333] rounded w-18"></div>
              </div>
            </div>
          </div>

          {/* Statistics Card 3 - Last Transactions */}
          <div className="flex-1 bg-[#404040] rounded-lg p-4">
            <div className="flex items-center gap-4 h-full">
              {/* Icon */}
              <div className="w-12 h-12 bg-[#333333] rounded-lg flex-shrink-0"></div>
              
              {/* Content */}
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-[#333333] rounded w-32"></div>
                <div className="space-y-1">
                  <div className="h-3 bg-[#333333] rounded w-24"></div>
                  <div className="h-3 bg-[#333333] rounded w-20"></div>
                  <div className="h-3 bg-[#333333] rounded w-28"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Statistics Card 4 - Betting Stats */}
          <div className="flex-1 bg-[#404040] rounded-lg p-4">
            <div className="flex items-center gap-4 h-full">
              {/* Icon */}
              <div className="w-12 h-12 bg-[#333333] rounded-lg flex-shrink-0"></div>
              
              {/* Content */}
              <div className="flex-1 space-y-2">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="h-4 bg-[#333333] rounded w-16"></div>
                    <div className="h-5 bg-[#333333] rounded w-12"></div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="h-4 bg-[#333333] rounded w-16"></div>
                    <div className="h-5 bg-[#333333] rounded w-12"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserStatisticsSkeleton;
