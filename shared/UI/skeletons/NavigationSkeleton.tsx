import React from 'react';

export interface NavigationSkeletonProps {
  type?: 'header' | 'sidebar' | 'breadcrumb' | 'tabs' | 'dropdown';
  itemCount?: number;
  orientation?: 'horizontal' | 'vertical';
  showIcons?: boolean;
  className?: string;
}

/**
 * NavigationSkeleton Component
 * 
 * A specialized skeleton component for navigation loading states.
 * Supports different navigation patterns and dark theme design system.
 */
const NavigationSkeleton: React.FC<NavigationSkeletonProps> = ({
  type = 'header',
  itemCount = 4,
  orientation = 'horizontal',
  showIcons = true,
  className = ''
}) => {
  const renderHeaderSkeleton = () => (
    <div className="flex items-center justify-between h-16 px-4 bg-nav animate-pulse">
      {/* Left Section */}
      <div className="flex items-center gap-4">
        <div className="w-8 h-8 bg-[#404040] rounded"></div>
        <div className="h-6 bg-[#404040] rounded w-32"></div>
      </div>

      {/* Center Section */}
      <div className="flex items-center gap-6">
        {Array.from({ length: itemCount }).map((_, i) => (
          <div key={i} className="flex items-center gap-2">
            {showIcons && <div className="w-4 h-4 bg-[#404040] rounded"></div>}
            <div className="h-4 bg-[#404040] rounded w-20"></div>
          </div>
        ))}
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-[#404040] rounded-full"></div>
        <div className="w-8 h-8 bg-[#404040] rounded"></div>
      </div>
    </div>
  );

  const renderSidebarSkeleton = () => (
    <div className="w-60 h-screen bg-nav p-4 animate-pulse">
      {/* Logo */}
      <div className="h-12 bg-[#404040] rounded mb-6"></div>

      {/* Navigation Items */}
      <div className="space-y-2">
        {Array.from({ length: itemCount }).map((_, i) => (
          <div key={i} className="flex items-center gap-3 p-3 rounded">
            {showIcons && <div className="w-5 h-5 bg-[#404040] rounded"></div>}
            <div className="h-4 bg-[#404040] rounded flex-1"></div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderBreadcrumbSkeleton = () => (
    <div className="flex items-center gap-2 animate-pulse">
      {Array.from({ length: itemCount }).map((_, i) => (
        <React.Fragment key={i}>
          <div className="h-4 bg-[#404040] rounded w-20"></div>
          {i < itemCount - 1 && <div className="h-4 bg-[#404040] rounded w-2"></div>}
        </React.Fragment>
      ))}
    </div>
  );

  const renderTabsSkeleton = () => (
    <div className={`flex ${orientation === 'vertical' ? 'flex-col space-y-2' : 'space-x-6'} animate-pulse`}>
      {Array.from({ length: itemCount }).map((_, i) => (
        <div key={i} className={`flex items-center gap-2 ${orientation === 'vertical' ? 'p-2' : 'pb-2'}`}>
          {showIcons && <div className="w-4 h-4 bg-[#404040] rounded"></div>}
          <div className="h-4 bg-[#404040] rounded w-16"></div>
        </div>
      ))}
    </div>
  );

  const renderDropdownSkeleton = () => (
    <div className="bg-elevated rounded-lg border border-border-primary p-2 w-48 animate-pulse">
      {Array.from({ length: itemCount }).map((_, i) => (
        <div key={i} className="flex items-center gap-3 p-2 rounded hover:bg-section">
          {showIcons && <div className="w-4 h-4 bg-[#404040] rounded"></div>}
          <div className="h-4 bg-[#404040] rounded flex-1"></div>
        </div>
      ))}
    </div>
  );

  const renderSkeleton = () => {
    switch (type) {
      case 'header':
        return renderHeaderSkeleton();
      case 'sidebar':
        return renderSidebarSkeleton();
      case 'breadcrumb':
        return renderBreadcrumbSkeleton();
      case 'tabs':
        return renderTabsSkeleton();
      case 'dropdown':
        return renderDropdownSkeleton();
      default:
        return renderHeaderSkeleton();
    }
  };

  return (
    <div className={className}>
      {renderSkeleton()}
    </div>
  );
};

export default NavigationSkeleton;
