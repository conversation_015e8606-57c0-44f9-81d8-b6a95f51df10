import React from 'react';

export interface PageSkeletonProps {
  showHeader?: boolean;
  showBreadcrumb?: boolean;
  showContent?: boolean;
  contentBlocks?: number;
  className?: string;
}

/**
 * PageSkeleton Component
 *
 * A reusable skeleton component for page loading states.
 * Provides a generic page structure skeleton.
 * Updated to use dark theme design system colors.
 */
const PageSkeleton: React.FC<PageSkeletonProps> = ({
  showHeader = true,
  showBreadcrumb = true,
  showContent = true,
  contentBlocks = 3,
  className = ''
}) => {
  return (
    <div className={`animate-pulse space-y-6 ${className}`}>
      {/* Breadcrumb Skeleton */}
      {showBreadcrumb && (
        <div className="flex items-center space-x-1 mb-4">
          <div className="h-4 bg-[#404040] rounded w-32"></div>
          <div className="h-4 bg-[#404040] rounded w-2"></div>
          <div className="h-4 bg-[#404040] rounded w-20"></div>
        </div>
      )}

      {/* Page Header Skeleton */}
      {showHeader && (
        <div className="bg-section rounded-lg border border-border-primary p-6 shadow-sm">
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex-1">
              <div className="h-8 bg-[#404040] rounded w-1/4 mb-2"></div>
              <div className="h-4 bg-[#333333] rounded w-1/2"></div>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-10 bg-[#404040] rounded w-24"></div>
              <div className="h-10 bg-[#404040] rounded w-32"></div>
            </div>
          </div>
        </div>
      )}

      {/* Content Skeleton */}
      {showContent && (
        <div className="space-y-6">
          {Array.from({ length: contentBlocks }).map((_, index) => (
            <div key={index} className="bg-elevated rounded-lg border border-border-primary">
              <div className="bg-section px-4 py-3 border-b border-border-primary rounded-t-lg">
                <div className="h-5 bg-[#404040] rounded w-32"></div>
              </div>
              <div className="p-4 space-y-4">
                <div className="h-4 bg-[#333333] rounded w-3/4"></div>
                <div className="h-4 bg-[#333333] rounded w-1/2"></div>
                <div className="h-4 bg-[#333333] rounded w-5/6"></div>
                <div className="h-32 bg-[#404040] rounded"></div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PageSkeleton;
