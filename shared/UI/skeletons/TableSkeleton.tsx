import React from 'react';

export interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  showHeader?: boolean;
  showPagination?: boolean;
  showFilters?: boolean;
  className?: string;
}

/**
 * TableSkeleton Component
 *
 * A reusable skeleton component for table loading states.
 * Consolidates table skeleton patterns across the application.
 * Updated to use dark theme design system colors.
 */
const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 6,
  showHeader = true,
  showPagination = true,
  showFilters = false,
  className = ''
}) => {
  return (
    <div className={`space-y-6 animate-pulse ${className}`}>
      {/* Filters Skeleton */}
      {showFilters && (
        <div className="bg-filter rounded-lg border border-border-primary">
          <div className="bg-section px-4 py-3 border-b border-border-primary rounded-t-lg">
            <div className="h-5 bg-[#404040] rounded w-24"></div>
          </div>
          <div className="p-4 bg-filter">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-[#404040] rounded w-20"></div>
                  <div className="h-10 bg-[#333333] rounded w-full"></div>
                </div>
              ))}
            </div>
            <div className="flex items-center gap-2">
              <div className="h-8 bg-[#404040] rounded w-20"></div>
              <div className="h-8 bg-[#404040] rounded w-24"></div>
            </div>
          </div>
        </div>
      )}

      {/* Table Skeleton */}
      <div className="bg-table-section rounded-lg border border-border-primary">
        {showHeader && (
          <div className="bg-section px-4 py-3 border-b border-border-primary rounded-t-lg">
            <div className="flex items-center justify-between w-full">
              <div className="h-5 bg-[#404040] rounded w-24"></div>
              <div className="h-4 bg-[#404040] rounded w-32"></div>
            </div>
          </div>
        )}
        <div className="bg-elevated rounded-b-lg">
          {/* Table Header */}
          <div className={`grid grid-cols-${columns} gap-4 p-4 bg-table-head border-b border-border-tertiary rounded-t-lg`}>
            {Array.from({ length: columns }).map((_, i) => (
              <div key={i} className="h-4 bg-[#404040] rounded"></div>
            ))}
          </div>

          {/* Table Rows */}
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <div key={rowIndex} className={`grid grid-cols-${columns} gap-4 p-4 border-b border-border-tertiary last:border-b-0`}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <div key={colIndex} className="h-4 bg-[#333333] rounded"></div>
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* Pagination Skeleton */}
      {showPagination && (
        <div className="flex items-center justify-between bg-elevated p-4 rounded-lg border border-border-primary">
          <div className="h-4 bg-[#404040] rounded w-32"></div>
          <div className="flex items-center gap-2">
            <div className="h-8 bg-table-total rounded w-8"></div>
            <div className="h-8 bg-table-total rounded w-8"></div>
            <div className="h-8 bg-table-total rounded w-8"></div>
            <div className="h-8 bg-table-total rounded w-8"></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TableSkeleton;
