import React from 'react';

export interface AuthenticationSkeletonProps {
  type?: 'signin' | 'signup' | '2fa' | 'generic';
  showLogo?: boolean;
  showForm?: boolean;
  className?: string;
}

/**
 * AuthenticationSkeleton Component
 * 
 * A specialized skeleton component for authentication page loading states.
 * Matches the authentication page layout and dark theme design system.
 */
const AuthenticationSkeleton: React.FC<AuthenticationSkeletonProps> = ({
  type = 'generic',
  showLogo = true,
  showForm = true,
  className = ''
}) => {
  const renderSignInSkeleton = () => (
    <div className="flex min-h-screen w-full items-center justify-center bg-background animate-pulse">
      <div className="w-full max-w-md">
        {/* Logo Skeleton */}
        {showLogo && (
          <div className="text-center mb-8">
            <div className="h-12 bg-[#404040] rounded w-48 mx-auto mb-4"></div>
          </div>
        )}

        {/* Form Container Skeleton */}
        {showForm && (
          <div className="bg-elevated rounded-lg border border-border-primary p-8">
            <div className="space-y-6">
              {/* Form Title */}
              <div className="text-center">
                <div className="h-8 bg-[#404040] rounded w-32 mx-auto mb-2"></div>
                <div className="h-4 bg-[#333333] rounded w-48 mx-auto"></div>
              </div>

              {/* Form Fields */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="h-4 bg-[#404040] rounded w-16"></div>
                  <div className="h-12 bg-[#333333] rounded w-full"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-[#404040] rounded w-20"></div>
                  <div className="h-12 bg-[#333333] rounded w-full"></div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="h-12 bg-[#404040] rounded w-full"></div>

              {/* Additional Links */}
              <div className="text-center space-y-2">
                <div className="h-4 bg-[#333333] rounded w-32 mx-auto"></div>
                <div className="h-4 bg-[#333333] rounded w-24 mx-auto"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const render2FASkeleton = () => (
    <div className="flex min-h-screen w-full items-center justify-center bg-background animate-pulse">
      <div className="w-full max-w-sm">
        {/* Logo Skeleton */}
        {showLogo && (
          <div className="text-center mb-8">
            <div className="h-12 bg-[#404040] rounded w-48 mx-auto mb-4"></div>
          </div>
        )}

        {/* 2FA Form Container Skeleton */}
        {showForm && (
          <div className="bg-elevated rounded-lg border border-border-primary p-8">
            <div className="space-y-6">
              {/* Form Title */}
              <div className="text-center">
                <div className="h-8 bg-[#404040] rounded w-40 mx-auto mb-2"></div>
                <div className="h-4 bg-[#333333] rounded w-56 mx-auto"></div>
              </div>

              {/* PIN Input Fields */}
              <div className="flex justify-center gap-3">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="w-12 h-12 bg-[#333333] rounded"></div>
                ))}
              </div>

              {/* Verify Button */}
              <div className="h-12 bg-[#404040] rounded w-full"></div>

              {/* Back Link */}
              <div className="text-center">
                <div className="h-4 bg-[#333333] rounded w-28 mx-auto"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderGenericSkeleton = () => (
    <div className="flex min-h-screen w-full items-center justify-center bg-background animate-pulse">
      <div className="bg-elevated rounded-lg border border-border-primary p-12 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#404040] border-t-primary mx-auto"></div>
        <div className="mt-4 h-4 bg-[#333333] rounded w-40 mx-auto"></div>
      </div>
    </div>
  );

  const renderSkeleton = () => {
    switch (type) {
      case 'signin':
      case 'signup':
        return renderSignInSkeleton();
      case '2fa':
        return render2FASkeleton();
      default:
        return renderGenericSkeleton();
    }
  };

  return (
    <div className={className}>
      {renderSkeleton()}
    </div>
  );
};

export default AuthenticationSkeleton;
