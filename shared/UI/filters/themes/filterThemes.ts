/**
 * Predefined Filter Theme Configurations
 * 
 * This file contains predefined theme configurations for the GlobalFilterSection component.
 * These themes follow the established dark theme design system and provide easy customization
 * options for different use cases.
 */

import type { FilterSectionTheme } from "../GlobalFilterSection";

// Re-export the type for convenience
export type { FilterSectionTheme } from "../GlobalFilterSection";

/**
 * Default Dark Theme - Follows the established dark theme design system
 * Uses semantic color classes for consistency across the application
 */
export const DARK_THEME: FilterSectionTheme = {
  // Container styling
  containerBackground: "bg-filter", // #1D1D1F
  containerBorder: "border-transparent",
  containerBorderRadius: "rounded-lg",

  // Header styling
  headerBackground: "bg-section", // #272729
  headerBorder: "border-filter-heading", // #333333
  headerBorderRadius: "rounded-t-lg",

  // Title styling
  titleColor: "text-white", // #FFFFFF
  titleFont: "font-rubik",
  titleSize: "text-lg",
  titleWeight: "font-medium",

  // Description styling
  descriptionColor: "text-secondary", // #AEAEAE
  descriptionFont: "font-rubik",
  descriptionSize: "text-sm",

  // Icon styling
  iconColor: "text-primary-400", // var(--golden)
  iconSize: "text-lg",

  // Filter label styling
  labelTextColor: "text-secondary", // #AEAEAE
  labelFont: "font-rubik",
  labelSize: "text-sm",
  labelWeight: "font-medium",

  // Filter input styling
  inputBackground: "bg-form-input", // #2C2C2F
  inputBorder: "border-secondary", // #FFFFFF33
  inputTextColor: "text-white", // #FFFFFF
  inputPlaceholderColor: "text-tertiary", // #616161

  // Button styling
  buttonBackground: "bg-golden-button",
  buttonHoverBackground: "hover:bg-golden-gradient",
  buttonTextColor: "text-white",
  buttonBorder: "border-transparent",

  // Dropdown styling
  dropdownBackground: "bg-section", // #272729
  dropdownBorder: "border-secondary", // #FFFFFF33
  dropdownTextColor: "text-white", // #FFFFFF
  dropdownHoverBackground: "hover:bg-elevated", // #272729
};

/**
 * Light Label Theme - Dark theme with lighter label text for better readability
 * Useful for sections where you want softer label appearance
 */
export const LIGHT_LABEL_THEME: FilterSectionTheme = {
  ...DARK_THEME,
  labelTextColor: "text-secondary", // #AEAEAE - Softer label color
  iconColor: "text-secondary", // #AEAEAE - Matching icon color
};

/**
 * Compact Theme - Smaller sizing for dense layouts
 * Maintains dark theme colors but with reduced spacing and font sizes
 */
export const COMPACT_THEME: FilterSectionTheme = {
  ...DARK_THEME,
  titleSize: "text-base",
  labelSize: "text-xs",
  descriptionSize: "text-xs",
  iconSize: "text-base",
};

/**
 * High Contrast Theme - Enhanced contrast for accessibility
 * Uses brighter colors for better visibility
 */
export const HIGH_CONTRAST_THEME: FilterSectionTheme = {
  ...DARK_THEME,
  labelTextColor: "text-white", // Pure white for maximum contrast
  titleColor: "text-white",
  descriptionColor: "text-gray-300", // Lighter description
  iconColor: "text-primary-300", // Brighter golden color
  dropdownTextColor: "text-white",
};

/**
 * Minimal Theme - Clean, minimal appearance with reduced visual elements
 * Good for simple filter sections
 */
export const MINIMAL_THEME: FilterSectionTheme = {
  ...DARK_THEME,
  headerBorder: "border-transparent", // Remove header border
  containerBorder: "border-transparent",
  iconColor: "text-gray-400", // Subdued icon color
  labelTextColor: "text-gray-300", // Subdued label color
};

/**
 * Custom theme examples for specific use cases
 */

/**
 * User Management Theme - Optimized for user management pages
 * Uses purple accents to match user management design
 */
export const USER_MANAGEMENT_THEME: FilterSectionTheme = {
  ...DARK_THEME,
  iconColor: "text-purple-400", // Purple accent for user management
  titleColor: "text-purple-100", // Subtle purple tint for title
};

/**
 * Financial Reports Theme - Optimized for financial/betting reports
 * Uses golden accents throughout for consistency with financial data
 */
export const FINANCIAL_THEME: FilterSectionTheme = {
  ...DARK_THEME,
  iconColor: "text-primary-400", // Golden accent
  titleColor: "text-primary-100", // Subtle golden tint
  labelTextColor: "text-primary-50", // Very light golden tint for labels
};

/**
 * Export all themes for easy importing
 */
export const FILTER_THEMES = {
  DARK: DARK_THEME,
  LIGHT_LABEL: LIGHT_LABEL_THEME,
  COMPACT: COMPACT_THEME,
  HIGH_CONTRAST: HIGH_CONTRAST_THEME,
  MINIMAL: MINIMAL_THEME,
  USER_MANAGEMENT: USER_MANAGEMENT_THEME,
  FINANCIAL: FINANCIAL_THEME,
} as const;

/**
 * Type for theme names
 */
export type FilterThemeName = keyof typeof FILTER_THEMES;

/**
 * Helper function to get a theme by name
 */
export function getFilterTheme(themeName: FilterThemeName): FilterSectionTheme {
  return FILTER_THEMES[themeName];
}

/**
 * Helper function to create a custom theme by extending an existing theme
 */
export function createCustomTheme(
  baseTheme: FilterSectionTheme,
  overrides: Partial<FilterSectionTheme>
): FilterSectionTheme {
  return { ...baseTheme, ...overrides };
}
