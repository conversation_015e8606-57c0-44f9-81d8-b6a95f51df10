// shared/UI/filters/UserSearchFilter.tsx - Username search filter component with API integration
"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { SpkFormInput } from '@/shared/UI/components';
import { useAuthStore } from '@/shared/stores/authStore';

interface UserSearchOption {
  id: number;
  username: string;
  email?: string;
  playerId?: number;
  userID?: number;
}

interface UserSearchFilterProps {
  value?: string;
  onChange: (userInfo: { username: string; playerId?: number; userID?: number } | null) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: "sm" | "md" | "lg";
  className?: string;
}

const UserSearchFilter: React.FC<UserSearchFilterProps> = ({
  value = '',
  onChange,
  placeholder = "Search username...",
  disabled = false,
  size = "sm",
  className = ""
}) => {
  const [searchTerm, setSearchTerm] = useState(value || '');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<UserSearchOption[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserSearchOption | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { token } = useAuthStore();

  // Track if the last change was from user input
  const isUserInputRef = useRef(false);

  // Sync with value prop changes only when it's not from user input
  useEffect(() => {
    // If this is not from user input and the value is different, sync it
    if (!isUserInputRef.current && value !== searchTerm) {
      setSearchTerm(value || '');
      if (!value) {
        setSelectedUser(null);
      }
    }
    // Reset the flag after processing
    isUserInputRef.current = false;
  }, [value, searchTerm]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // API call to search users
  const searchUsers = useCallback(async (searchTerm: string) => {
    if (!token || searchTerm.length < 3) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        `https://adminapi.ingrandstation.com/api/admin/transactions/users/new2?owner_type=User&search=${encodeURIComponent(searchTerm)}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();

        // Check if the response is successful and has records
        if (data.success === 1 && data.record && Array.isArray(data.record)) {
          // Transform API response to our expected format
          const users: UserSearchOption[] = data.record.map((user: any) => {
            // Extract username and email from the text field
            // Format: "email(username)id" or similar
            const textParts = user.text || '';
            let username = '';
            let email = '';

            // Try to parse the text field to extract username and email
            const emailMatch = textParts.match(/^([^(]+)/);
            const usernameMatch = textParts.match(/\(([^)]+)\)/);

            if (emailMatch) {
              email = emailMatch[1].trim();
            }
            if (usernameMatch) {
              username = usernameMatch[1].trim();
            }

            // If we couldn't parse, use the text as username
            if (!username && !email) {
              username = textParts;
            }

            return {
              id: user.id,
              username: username || email || textParts,
              email: email || undefined,
              playerId: user.id,
              userID: user.id
            };
          });
          setSearchResults(users);
        } else {
          // Handle case where API returns success but no records
          setSearchResults([]);
        }
      } else {
        //eslint-disable-next-line no-console
        console.error('Failed to search users:', response.statusText);
        setSearchResults([]);
      }
    } catch (error) {
      //eslint-disable-next-line no-console
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  // Handle input change with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm.length >= 3) {
        // Only search and open dropdown if:
        // 1. No user is currently selected, OR
        // 2. The search term doesn't match the selected user's username (user is typing to find someone else)
        const shouldSearch = !selectedUser || searchTerm.toLowerCase() !== selectedUser.username.toLowerCase();

        if (shouldSearch) {
          searchUsers(searchTerm);
          setIsOpen(true);
        } else {
          // User has selected someone and the search term matches - don't search
          setSearchResults([]);
          setIsOpen(false);
        }
      } else {
        setSearchResults([]);
        setIsOpen(false);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, searchUsers, selectedUser]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Mark this as user input to prevent sync issues
    isUserInputRef.current = true;
    setSearchTerm(newValue);

    // Clear selection if user is typing something different from the selected user
    if (selectedUser && newValue.toLowerCase() !== selectedUser.username.toLowerCase()) {
      setSelectedUser(null);
      onChange(null);
    }
  };

  // Handle user selection
  const handleUserSelect = (user: UserSearchOption) => {
    setSelectedUser(user);
    setSearchTerm(user.username);
    setIsOpen(false);
    setSearchResults([]); // Clear search results to prevent dropdown from reopening

    // Call onChange with user info including playerId/userID
    onChange({
      username: user.username,
      playerId: user.playerId,
      userID: user.userID
    });
  };

  // Handle clear selection
  const handleClear = () => {
    setSelectedUser(null);
    setSearchTerm('');
    setSearchResults([]);
    setIsOpen(false);
    onChange(null);
    inputRef.current?.focus();
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="relative">
        <SpkFormInput
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          size={size}
          className="!mb-0 h-10 pr-8"
        />

        {/* Clear button */}
        {searchTerm && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            title="Clear selection"
          >
            <i className="ri-close-line text-sm"></i>
          </button>
        )}
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-elevated border border-secondary rounded-md shadow-lg max-h-60 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center py-3">
              <i className="ri-loader-4-line animate-spin text-primary mr-2"></i>
              <span className="text-sm text-white">Searching users...</span>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="p-3 text-center text-gray-400 text-sm">
              {searchTerm.length < 3 ? (
                <>
                  <i className="ri-search-line mr-1"></i>
                  Type at least 3 characters to search
                </>
              ) : (
                <>
                  <i className="ri-user-search-line mr-1"></i>
                  No users found matching "{searchTerm}"
                </>
              )}
            </div>
          ) : (
            <div className="max-h-48 overflow-y-auto">
              {searchResults.map((user) => (
                <button
                  key={user.id}
                  type="button"
                  onClick={() => handleUserSelect(user)}
                  className="w-full px-3 py-2 text-left hover:bg-section transition-colors flex items-center gap-2 text-white text-sm"
                >
                  <i className="ri-user-line text-primary flex-shrink-0"></i>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{user.username}</div>
                    {user.email && (
                      <div className="text-xs text-gray-400 truncate">{user.email}</div>
                    )}
                  </div>
                  {user.playerId && (
                    <div className="text-xs text-gray-500">ID: {user.playerId}</div>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UserSearchFilter;
