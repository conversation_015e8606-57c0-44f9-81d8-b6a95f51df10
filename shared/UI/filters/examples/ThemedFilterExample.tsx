/**
 * Themed Filter Example Component
 * 
 * This component demonstrates how to use the GlobalFilterSection theming system
 * with different theme configurations and customization options.
 */

"use client";

import React, { useState } from "react";
import GlobalFilterSection from "../GlobalFilterSection";
import { FILTER_THEMES, createCustomTheme } from "../themes/filterThemes";
import type { FilterDefinition } from "../SpkAdvancedFilters";

// Example filter definitions
const EXAMPLE_FILTERS: FilterDefinition[] = [
  {
    id: "search",
    label: "Search",
    icon: "ri-search-line",
    component: "input",
    placeholder: "Search users...",
    filterKey: "search"
  },
  {
    id: "status",
    label: "Status",
    icon: "ri-user-line",
    component: "select",
    filterKey: "status",
    options: [
      { value: "", label: "All Statuses" },
      { value: "active", label: "Active" },
      { value: "inactive", label: "Inactive" },
      { value: "suspended", label: "Suspended" }
    ]
  },
  {
    id: "dateRange",
    label: "Date Range",
    icon: "ri-calendar-line",
    component: "dateRange",
    filterKey: "dateRange"
  },
  {
    id: "amount",
    label: "Amount Range",
    icon: "ri-money-dollar-circle-line",
    component: "numberRange",
    filterKey: "amount"
  }
];

const DEFAULT_VISIBLE_FILTERS = ["search", "status"];

interface ExampleFilters {
  search: string;
  status: string;
  dateRange: { startDate: string; endDate: string };
  amount: { min: string; max: string };
  page: number;
  size: number;
}

const ThemedFilterExample: React.FC = () => {
  const [filters, setFilters] = useState<ExampleFilters>({
    search: "",
    status: "",
    dateRange: { startDate: "", endDate: "" },
    amount: { min: "", max: "" },
    page: 1,
    size: 10
  });

  const [selectedTheme, setSelectedTheme] = useState<keyof typeof FILTER_THEMES>("DARK");

  const handleFilterChange = (newFilters: Partial<ExampleFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleExport = () => {
    // alert("Export functionality triggered!");
  };

  // Create a custom theme example
  const customTheme = createCustomTheme(FILTER_THEMES.DARK, {
    labelTextColor: "text-emerald-300",
    iconColor: "text-emerald-400",
    titleColor: "text-emerald-100",
    headerBackground: "bg-emerald-900/20",
  });

  return (
    <div className="space-y-8 p-6 bg-background min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-2">Filter Theming Examples</h1>
        <p className="text-secondary mb-8">
          Demonstration of the GlobalFilterSection theming system with different configurations.
        </p>

        {/* Theme Selector */}
        <div className="mb-8 p-4 bg-section rounded-lg">
          <h2 className="text-xl font-semibold text-white mb-4">Select Theme</h2>
          <div className="flex flex-wrap gap-2">
            {Object.keys(FILTER_THEMES).map((themeName) => (
              <button
                key={themeName}
                onClick={() => setSelectedTheme(themeName as keyof typeof FILTER_THEMES)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${selectedTheme === themeName
                  ? "bg-primary text-white"
                  : "bg-elevated text-secondary hover:bg-section hover:text-white"
                  }`}
              >
                {themeName.replace(/_/g, " ")}
              </button>
            ))}
          </div>
        </div>

        {/* Default Theme Example */}
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-white mb-4">
              Current Theme: {selectedTheme.replace(/_/g, " ")}
            </h2>
            <GlobalFilterSection
              filters={filters}
              onFilterChange={handleFilterChange}
              availableFilters={EXAMPLE_FILTERS}
              defaultVisibleFilters={DEFAULT_VISIBLE_FILTERS}
              title="User Management Filters"
              description="Filter users by search terms, status, date range, and amount"
              showExportButton={true}
              onExport={handleExport}
              exportLabel="Export Users"
              exportIcon="ri-download-line"
              theme={FILTER_THEMES[selectedTheme]}
            />
          </div>

          {/* Custom Theme Example */}
          <div>
            <h2 className="text-xl font-semibold text-white mb-4">Custom Emerald Theme</h2>
            <GlobalFilterSection
              filters={filters}
              onFilterChange={handleFilterChange}
              availableFilters={EXAMPLE_FILTERS}
              defaultVisibleFilters={DEFAULT_VISIBLE_FILTERS}
              title="Custom Themed Filters"
              description="Example of a custom theme with emerald colors"
              showExportButton={true}
              onExport={handleExport}
              theme={customTheme}
            />
          </div>

          {/* Custom Styles Example */}
          <div>
            <h2 className="text-xl font-semibold text-white mb-4">Custom Styles Example</h2>
            <GlobalFilterSection
              filters={filters}
              onFilterChange={handleFilterChange}
              availableFilters={EXAMPLE_FILTERS}
              defaultVisibleFilters={DEFAULT_VISIBLE_FILTERS}
              title="Advanced Styled Filters"
              description="Example with custom styles and theme combination"
              showExportButton={true}
              onExport={handleExport}
              theme={FILTER_THEMES.HIGH_CONTRAST}
              customStyles={{
                container: "shadow-2xl border border-primary/20",
                header: "bg-gradient-to-r from-primary/10 to-purple-500/10",
                title: "text-2xl font-bold bg-gradient-to-r from-primary to-purple-400 bg-clip-text text-transparent",
                description: "italic text-primary/80",
                icon: "text-purple-400"
              }}
            />
          </div>

          {/* Compact Theme Example */}
          <div>
            <h2 className="text-xl font-semibold text-white mb-4">Compact Theme</h2>
            <GlobalFilterSection
              filters={filters}
              onFilterChange={handleFilterChange}
              availableFilters={EXAMPLE_FILTERS}
              defaultVisibleFilters={DEFAULT_VISIBLE_FILTERS}
              title="Compact Filters"
              description="Space-efficient filter layout"
              theme={FILTER_THEMES.COMPACT}
            />
          </div>
        </div>

        {/* Current Filter Values Display */}
        <div className="mt-8 p-4 bg-section rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Current Filter Values</h3>
          <pre className="text-sm text-secondary bg-elevated p-4 rounded overflow-auto">
            {JSON.stringify(filters, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default ThemedFilterExample;
