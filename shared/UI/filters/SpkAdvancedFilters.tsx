// shared/UI/components/SpkAdvancedFilters.tsx - Reusable advanced filter component
"use client";

import SpkDatepickr from "@/shared/@spk-reusable-components/spk-packages/datepicker-component";
import FormCheckbox from "@/shared/components/forms/checkboxes/FormCheckbox";
import { SpkFormInput, SpkFormSelect, SpkFormSelectOption } from "@/shared/UI/components";
import { useCallback, useEffect, useRef, useState } from "react";
import UserSearchFilter from "./UserSearchFilter";

// Import theme types from GlobalFilterSection
import type { FilterSectionTheme } from "./GlobalFilterSection";

// Generic filter type definition
export interface FilterDefinition {
	id: string;
	label: string;
	icon: string;
	component: "input" | "select" | "dateRange" | "number" | "numberRange" | "userSearch";
	placeholder?: string;
	options?: SpkFormSelectOption[];
	filterKey: string;
}

// Generic props interface
export interface SpkAdvancedFiltersProps<T> {
	filters: T;
	onFilterChange: (filters: Partial<T>) => void;
	isLoading?: boolean;
	availableFilters: FilterDefinition[];
	defaultVisibleFilters: string[];
	title?: string;
	description?: string;
	showTimeSelect?: boolean;
	theme?: Partial<FilterSectionTheme>;
}

function SpkAdvancedFilters<T extends Record<string, any>>({
	filters,
	onFilterChange,
	isLoading = false,
	availableFilters,
	defaultVisibleFilters,
	title: _title = "Filters",
	description: _description,
	showTimeSelect = false,
	theme = {}
}: SpkAdvancedFiltersProps<T>) {
	const [localFilters, setLocalFilters] = useState(filters);
	const [showAddFilterDropdown, setShowAddFilterDropdown] = useState(false);
	const [visibleFilters, setVisibleFilters] = useState<string[]>(defaultVisibleFilters);
	const dropdownRef = useRef<HTMLDivElement>(null);

	// Calculate if "More Filters" button should be shown
	const shouldShowMoreFiltersButton = () => {
		// Rule 1: Show only when there are more than 3 available filters
		if (availableFilters.length <= 3) {
			return false;
		}

		// Get filters that are NOT in the default visible filters (these are the "More Filters")
		const moreFilters = availableFilters.filter(filter => !defaultVisibleFilters.includes(filter.id));

		// Rule 2: If there are no "more filters" available, don't show the button
		if (moreFilters.length === 0) {
			return false;
		}

		// Rule 3: If all "more filters" are currently visible/selected, hide the button
		const allMoreFiltersVisible = moreFilters.every(filter => visibleFilters.includes(filter.id));
		if (allMoreFiltersVisible) {
			return false;
		}

		// Show the button if there are unselected "more filters"
		return true;
	};

	// Build dynamic class names based on theme configuration
	const labelClasses = [
		theme.labelTextColor || "text-secondary",
		theme.labelFont || "font-rubik",
		theme.labelSize || "text-sm",
		theme.labelWeight || "font-medium",
		"whitespace-nowrap"
	].filter(Boolean).join(" ");

	const dropdownClasses = [
		theme.dropdownBackground || "bg-section",
		theme.dropdownBorder || "border-secondary",
		"rounded-lg shadow-modal z-50"
	].filter(Boolean).join(" ");

	const dropdownTextClasses = [
		theme.dropdownTextColor || "text-white",
		theme.labelFont || "font-rubik",
		"text-sm font-medium mb-2"
	].filter(Boolean).join(" ");

	const dropdownItemClasses = [
		theme.dropdownHoverBackground || "hover:bg-elevated",
		"rounded"
	].filter(Boolean).join(" ");

	// Update local filters when props change
	useEffect(() => {
		setLocalFilters(filters);
	}, [filters]);

	// Close dropdown when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
				setShowAddFilterDropdown(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	// Handle local filter changes
	const handleLocalFilterChange = useCallback((key: string, value: any) => {
		setLocalFilters(prev => ({
			...prev,
			[key]: value
		}));
	}, []);

	// Apply filters
	const handleApplyFilters = useCallback(() => {
		onFilterChange(localFilters);
	}, [localFilters, onFilterChange]);

	// Clear all filters
	const handleClearFilters = useCallback(() => {
		const clearedFilters = Object.keys(localFilters).reduce((acc, key) => {
			if (key === 'size' || key === 'page') {
				acc[key] = localFilters[key]; // Keep pagination
			} else {
				acc[key] = typeof localFilters[key] === 'string' ? '' :
					typeof localFilters[key] === 'number' ? 0 :
						Array.isArray(localFilters[key]) ? [] :
							localFilters[key] && typeof localFilters[key] === 'object' ? {} : '';
			}
			return acc;
		}, {} as any);

		setLocalFilters(clearedFilters);
		onFilterChange(clearedFilters);
	}, [localFilters, onFilterChange]);

	// Add filter to visible list
	const handleAddFilter = (filterId: string) => {
		if (!visibleFilters.includes(filterId)) {
			setVisibleFilters(prev => [...prev, filterId]);
		}
		setShowAddFilterDropdown(false);
	};

	// Remove filter from visible list
	const handleRemoveFilter = (filterId: string) => {
		if (!defaultVisibleFilters.includes(filterId)) {
			setVisibleFilters(prev => prev.filter(id => id !== filterId));
			// Clear the filter value
			const filter = availableFilters.find(f => f.id === filterId);
			if (filter) {
				handleLocalFilterChange(filter.filterKey, '');
			}
		}
	};

	// Toggle filter visibility
	const handleFilterToggle = (filterId: string, checked: boolean) => {
		if (checked) {
			handleAddFilter(filterId);
		} else {
			handleRemoveFilter(filterId);
		}
	};

	// Render individual filter component
	const renderFilterComponent = (filter: FilterDefinition) => {
		const commonProps = {
			size: "sm" as const,
			className: "!mb-0 h-10"
		};

		const filterContainerClass = "h-10 flex items-center";

		switch (filter.component) {
			case "input":
				return (
					<div className={filterContainerClass}>
						<SpkFormInput
							type="text"
							placeholder={filter.placeholder}
							value={localFilters[filter.filterKey] as string || ""}
							onChange={(e) => handleLocalFilterChange(filter.filterKey, e.target.value)}
							{...commonProps}
						/>
					</div>
				);

			case "number":
				return (
					<div className={filterContainerClass}>
						<SpkFormInput
							type="number"
							placeholder={filter.placeholder}
							value={localFilters[filter.filterKey] as number || 0}
							onChange={(e) => handleLocalFilterChange(filter.filterKey, Number(e.target.value))}
							min={0}
							{...commonProps}
						/>
					</div>
				);

			case "select":
				return (
					<div className={filterContainerClass}>
						<SpkFormSelect
							options={filter.options || []}
							value={localFilters[filter.filterKey] as string || ""}
							onChange={(e) => {
								const selectedValue = e.target.value;
								// Find the original option to get its actual value type
								const selectedOption = filter.options?.find(opt => String(opt.value) === selectedValue);
								// Use the original value type (number/string/null) instead of always string
								const actualValue = selectedOption ? selectedOption.value : selectedValue;
								handleLocalFilterChange(filter.filterKey, actualValue);
							}}
							aria-label={filter.label}
							{...commonProps}
						/>
					</div>
				);

			case "dateRange": {
				// Handle date range with proper startDate/endDate format
				const dateRangeValue = localFilters[filter.filterKey];
				let startDate = null;
				let endDate = null;

				// Extract dates from the stored value
				if (dateRangeValue && typeof dateRangeValue === 'object') {
					if (dateRangeValue.startDate) {
						startDate = new Date(dateRangeValue.startDate);
					}
					if (dateRangeValue.endDate) {
						endDate = new Date(dateRangeValue.endDate);
					}
				}

				return (
					<div className={filterContainerClass}>
						<SpkDatepickr
							placeholderText="Select date and time range"
							startDate={startDate}
							endDate={endDate}
							selected={startDate} // For range picker, selected should be startDate
							onChange={(dates) => {
								// Handle date range selection
								if (Array.isArray(dates)) {
									const [start, end] = dates;
									const dateRange = {
										startDate: start ? start.toISOString().slice(0, 16) : '',
										endDate: end ? end.toISOString().slice(0, 16) : ''
									};
									handleLocalFilterChange(filter.filterKey, dateRange);
								} else if (dates === null) {
									// Clear the date range
									handleLocalFilterChange(filter.filterKey, { startDate: '', endDate: '' });
								}
							}}
							selectsRange={true}
							showTimeSelect={showTimeSelect}
							timeFormat="HH:mm"
							timeIntervals={15}
							dateFormat="yyyy-MM-dd HH:mm"
							isClearable={true}
							className="form-control h-10"
						/>
					</div>
				);
			}

			case "numberRange": {
				const rangeValue = localFilters[filter.filterKey] || { min: '', max: '' };
				return (
					<div className={filterContainerClass}>
						<div className="flex items-center gap-2 w-full">
							<SpkFormInput
								type="number"
								placeholder="Min"
								value={rangeValue.min || ""}
								onChange={(e) => handleLocalFilterChange(filter.filterKey, { ...rangeValue, min: e.target.value })}
								min={0}
								className="!mb-0 h-10 flex-1"
								size="sm"
							/>
							<span className="text-secondary text-sm">-</span>
							<SpkFormInput
								type="number"
								placeholder="Max"
								value={rangeValue.max || ""}
								onChange={(e) => handleLocalFilterChange(filter.filterKey, { ...rangeValue, max: e.target.value })}
								min={0}
								className="!mb-0 h-10 flex-1"
								size="sm"
							/>
						</div>
					</div>
				);
			}

			case "userSearch":
				return (
					<div className={filterContainerClass}>
						<UserSearchFilter
							value={localFilters[filter.filterKey] as string || ""}
							onChange={(userInfo) => {
								if (userInfo) {
									// Store the username for display (but don't pass it back as value)
									handleLocalFilterChange(filter.filterKey, userInfo.username);

									// Set ALL possible user ID fields that different reports might need
									// Convert to string for all report types as they all expect string IDs
									if (userInfo.playerId) {
										// For bet reports (POST requests) and financial reports - use string
										handleLocalFilterChange('playerId', userInfo.playerId.toString());
									}

									if (userInfo.userID) {
										// For APIs that use userID (legacy field) - convert to string
										handleLocalFilterChange('userID', userInfo.userID.toString());
									}

									// For APIs that use userId (cashier reports) - convert to string
									if (userInfo.playerId || userInfo.userID) {
										const userIdValue = userInfo.playerId ? userInfo.playerId.toString() : userInfo.userID!.toString();
										handleLocalFilterChange('userId', userIdValue);
									}
								} else {
									// Clear all related fields
									handleLocalFilterChange(filter.filterKey, '');
									// Clear user ID fields that exist in the current filter structure
									if ('playerId' in localFilters) {
										handleLocalFilterChange('playerId', '');
									}
									if ('userId' in localFilters) {
										handleLocalFilterChange('userId', '');
									}
									// Legacy cleanup for userID field
									if ('userID' in localFilters) {
										handleLocalFilterChange('userID', '');
									}
								}
							}}
							placeholder={filter.placeholder}
							size="sm"
							className="w-full"
						/>
					</div>
				);

			default:
				return null;
		}
	};

	return (

		<div className="p-[10px] flex flex-wrap justify-between items-end ">
			{/* Main filter row - horizontal layout matching design */}
			<div className="flex flex-wrap items-end gap-[10px] mb-[10px]">
				{visibleFilters.map(filterId => {
					const filter = availableFilters.find(f => f.id === filterId);

					if (!filter) return null;

					const isDefaultFilter = defaultVisibleFilters.includes(filterId);

					return (
						<div key={filterId} className="flex flex-col items-start gap-1">
							<div className="flex  items-center gap-1 min-w-0 w-full justify-between">
								{/* <i className={`${filter.icon} ${theme.iconColor || "text-secondary"} text-sm flex-shrink-0`}></i> */}
								<span className={labelClasses}>
									{filter.label}
								</span>
								{!isDefaultFilter && (
									<button
										type="button"
										onClick={() => handleRemoveFilter(filterId)}
										className="text-tertiary hover:text-danger-500 transition-colors px-1"
										title={`Remove ${filter.label} filter`}
									>
										<i className="ri-close-line text-sm"></i>
									</button>
								)}
							</div>
							<div className="min-w-[200px]">
								{renderFilterComponent(filter)}
							</div>

						</div>
					);
				})}

				{/* More Filters Button - Conditionally rendered */}
				{shouldShowMoreFiltersButton() && (
					<div className="relative" ref={dropdownRef}>
						<div className="flex flex-col items-start gap-1">
							<button
								type="button"
								// style={{ borderColor: "rgb(var(--default-border))" }}
								onClick={() => setShowAddFilterDropdown(!showAddFilterDropdown)}
								className="
									border border-[var(--default-border)]
									text-white font-rubik placeholder-tertiary
									px-[0.85rem] py-[0.375rem] text-[0.875rem]
									flex items-center justify-between gap-2
									rounded-[0.35rem]
									min-w-[200px] h-10
									transition-all duration-200
									hover:border-accent
									focus:border-accent focus:ring-accent/50
								"
							>
								<span>More Filters</span>
								<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M14.5 2.50001H12.675C12.5629 2.00291 12.285 1.55874 11.887 1.2405C11.489 0.922267 10.9946 0.748901 10.485 0.748901C9.97541 0.748901 9.48099 0.922267 9.08299 1.2405C8.68499 1.55874 8.40711 2.00291 8.295 2.50001H1.5C1.36739 2.50001 1.24021 2.55269 1.14645 2.64646C1.05268 2.74023 1 2.86741 1 3.00001C1 3.13262 1.05268 3.2598 1.14645 3.35357C1.24021 3.44734 1.36739 3.50001 1.5 3.50001H8.295C8.40711 3.99711 8.68499 4.44129 9.08299 4.75952C9.48099 5.07776 9.97541 5.25113 10.485 5.25113C10.9946 5.25113 11.489 5.07776 11.887 4.75952C12.285 4.44129 12.5629 3.99711 12.675 3.50001H14.5C14.6326 3.50001 14.7598 3.44734 14.8536 3.35357C14.9473 3.2598 15 3.13262 15 3.00001C15 2.86741 14.9473 2.74023 14.8536 2.64646C14.7598 2.55269 14.6326 2.50001 14.5 2.50001ZM10.5 4.25001C10.2522 4.25299 10.009 4.18221 9.8015 4.04668C9.59397 3.91115 9.43143 3.71698 9.33451 3.48885C9.2376 3.26072 9.21069 3.00893 9.25722 2.76548C9.30374 2.52202 9.42159 2.29789 9.5958 2.12157C9.77 1.94525 9.99269 1.8247 10.2356 1.77524C10.4784 1.72578 10.7305 1.74965 10.9598 1.8438C11.1891 1.93795 11.3852 2.09814 11.5233 2.30402C11.6613 2.50989 11.735 2.75215 11.735 3.00001C11.735 3.32895 11.6054 3.64464 11.3742 3.87864C11.143 4.11263 10.8289 4.24607 10.5 4.25001Z" fill="#616161" />
									<path d="M14.5 7.56001H7.705C7.59289 7.06291 7.315 6.61874 6.91701 6.3005C6.51901 5.98226 6.02458 5.8089 5.515 5.8089C5.00541 5.8089 4.51099 5.98226 4.11299 6.3005C3.71499 6.61874 3.43711 7.06291 3.325 7.56001H1.5C1.36739 7.56001 1.24021 7.61269 1.14645 7.70646C1.05268 7.80023 1 7.9274 1 8.06001C1 8.19262 1.05268 8.3198 1.14645 8.41356C1.24021 8.50733 1.36739 8.56001 1.5 8.56001H3.325C3.43711 9.05711 3.71499 9.50128 4.11299 9.81952C4.51099 10.1378 5.00541 10.3111 5.515 10.3111C6.02458 10.3111 6.51901 10.1378 6.91701 9.81952C7.315 9.50128 7.59289 9.05711 7.705 8.56001H14.5C14.6326 8.56001 14.7598 8.50733 14.8536 8.41356C14.9473 8.3198 15 8.19262 15 8.06001C15 7.9274 14.9473 7.80023 14.8536 7.70646C14.7598 7.61269 14.6326 7.56001 14.5 7.56001ZM5.5 9.31001C5.25277 9.31001 5.0111 9.2367 4.80554 9.09935C4.59998 8.962 4.43976 8.76677 4.34515 8.53837C4.25054 8.30996 4.22579 8.05862 4.27402 7.81615C4.32225 7.57367 4.4413 7.35094 4.61612 7.17613C4.79093 7.00131 5.01366 6.88226 5.25614 6.83403C5.49861 6.7858 5.74995 6.81055 5.97835 6.90516C6.20676 6.99977 6.40199 7.15999 6.53934 7.36555C6.67669 7.57111 6.75 7.81279 6.75 8.06001C6.75 8.22416 6.71767 8.38671 6.65485 8.53837C6.59203 8.69002 6.49996 8.82782 6.38388 8.9439C6.26781 9.05997 6.13001 9.15204 5.97835 9.21486C5.8267 9.27768 5.66415 9.31001 5.5 9.31001Z" fill="#616161" />
									<path d="M14.5 12.5H12.675C12.5629 12.0029 12.285 11.5587 11.887 11.2405C11.489 10.9223 10.9946 10.7489 10.485 10.7489C9.97541 10.7489 9.48099 10.9223 9.08299 11.2405C8.68499 11.5587 8.40711 12.0029 8.295 12.5H1.5C1.36739 12.5 1.24021 12.5527 1.14645 12.6465C1.05268 12.7402 1 12.8674 1 13C1 13.1326 1.05268 13.2598 1.14645 13.3536C1.24021 13.4473 1.36739 13.5 1.5 13.5H8.295C8.40711 13.9971 8.68499 14.4413 9.08299 14.7595C9.48099 15.0778 9.97541 15.2511 10.485 15.2511C10.9946 15.2511 11.489 15.0778 11.887 14.7595C12.285 14.4413 12.5629 13.9971 12.675 13.5H14.5C14.6326 13.5 14.7598 13.4473 14.8536 13.3536C14.9473 13.2598 15 13.1326 15 13C15 12.8674 14.9473 12.7402 14.8536 12.6465C14.7598 12.5527 14.6326 12.5 14.5 12.5ZM10.5 14.25C10.2528 14.25 10.0111 14.1767 9.80554 14.0394C9.59998 13.902 9.43976 13.7068 9.34515 13.4784C9.25054 13.25 9.22579 12.9986 9.27402 12.7562C9.32225 12.5137 9.4413 12.2909 9.61612 12.1161C9.79093 11.9413 10.0137 11.8223 10.2561 11.774C10.4986 11.7258 10.7499 11.7506 10.9784 11.8452C11.2068 11.9398 11.402 12.1 11.5393 12.3056C11.6767 12.5111 11.75 12.7528 11.75 13C11.75 13.3315 11.6183 13.6495 11.3839 13.8839C11.1495 14.1183 10.8315 14.25 10.5 14.25Z" fill="#616161" />
								</svg>

							</button>
						</div>

						{showAddFilterDropdown && (
							<div className={`absolute top-full left-0 mt-2 w-64 ${dropdownClasses}`}>
								<div className="p-3">
									<div className={dropdownTextClasses}>
										Available Filters
									</div>
									<div className="space-y-1 max-h-60 overflow-y-auto">
										{availableFilters.map(filter => {
											const isVisible = visibleFilters.includes(filter.id);
											const isDefault = defaultVisibleFilters.includes(filter.id);

											return (
												<div key={filter.id} className={`flex items-center gap-3 py-2 px-2 ${dropdownItemClasses}`}>
													<FormCheckbox
														id={`filter-${filter.id}`}
														checked={isVisible}
														disabled={isDefault}
														onChange={(e) => handleFilterToggle(filter.id, e.target.checked)}
														size="sm"
													/>
													<label
														htmlFor={`filter-${filter.id}`}
														className={`flex items-center gap-2 ${theme.labelSize || "text-sm"} ${theme.labelFont || "font-rubik"} cursor-pointer flex-1 ${isDefault ? "text-tertiary" : (theme.dropdownTextColor || "text-white")}`}
													>
														<i className={`${filter.icon} text-sm`}></i>
														{filter.label}
														{isDefault && (
															<span className="text-xs text-tertiary">(Default)</span>
														)}
													</label>
												</div>
											);
										})}
									</div>
								</div>
							</div>
						)}
					</div>
				)}
			</div>

			{/* Action buttons */}
			<div className="flex items-center gap-3">
				<button
					type="button"
					onClick={handleApplyFilters}
					disabled={isLoading}
					className="
							bg-golden-button hover:bg-golden-gradient
							shadow-golden-button hover:shadow-golden-button-hover
							text-white font-medium px-4 py-2 rounded-lg
							flex items-center gap-2
							transition-all duration-200
							hover:transform hover:-translate-y-0.5
							disabled:opacity-50 disabled:cursor-not-allowed
							disabled:hover:transform-none
						"
				>
					<i className="ri-search-line"></i>
					Apply
				</button>

				<button
					type="button"
					onClick={handleClearFilters}
					disabled={isLoading}
					className="
							bg-elevated hover:bg-section
							border border-secondary hover:border-accent
							text-white font-rubik font-medium px-4 py-2 rounded-lg
							flex items-center gap-2
							transition-all duration-200
							hover:transform hover:-translate-y-0.5
							disabled:opacity-50 disabled:cursor-not-allowed
							disabled:hover:transform-none
						"
				>
					<i className="ri-refresh-line"></i>
					Clear
				</button>
			</div>
		</div>
	);
}

export default SpkAdvancedFilters;
