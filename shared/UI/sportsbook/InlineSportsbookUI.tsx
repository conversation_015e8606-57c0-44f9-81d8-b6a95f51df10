'use client';

import React from 'react';
import { WebSocketStatusIndicator } from '@/shared/providers/TurboStarsWebSocketProvider';

export interface InlineSportsbookUIProps {
  className?: string;
  isOpen: boolean;
  url: string | null;
  userName: string | null;
  error: string | null;
  isIframeLoading: boolean;
  iframeError: string | null;
  iframeRef: React.RefObject<HTMLIFrameElement | null>;
  onIframeLoad: () => void;
  onIframeError: () => void;
  onClose: () => void;
}

/**
 * Pure UI component for inline sportsbook
 * 
 * This component only handles presentation and receives all data via props.
 * All business logic is handled by the useInlineSportsbookLogic hook.
 */
export const InlineSportsbookUI: React.FC<InlineSportsbookUIProps> = ({
  className = "",
  isOpen,
  url,
  userName,
  error,
  isIframeLoading,
  iframeError,
  iframeRef,
  onIframeLoad,
  onIframeError,
  onClose,
}) => {
  if (!isOpen) {
    return null;
  }

  return (
    <div className={`fixed inset-0 z-[9999] bg-[#0F0F0F] ${className}`}>
      {/* Fullscreen sportsbook container matching sportsbook page layout */}
      <div className="w-full h-full relative overflow-hidden">
        {/* Header with sportsbook styling */}
        <div className="absolute top-0 left-0 right-0 z-20 flex items-center justify-between p-4 bg-[#1D1D1D]/90 backdrop-blur-sm border-b border-[#626573]">
          <div className="flex items-center space-x-3">
            <h2 className="text-lg font-rubik font-medium text-white">
              TurboStars Sportsbook
            </h2>
            {userName && (
              <span className="text-sm text-[#9FA3B6] font-rubik">
                - {userName}
              </span>
            )}
            <WebSocketStatusIndicator />
          </div>
          <button
            onClick={onClose}
            className="p-2 text-[#9FA3B6] hover:text-white rounded-lg hover:bg-[#272729] transition-all duration-200"
            aria-label="Close sportsbook"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content - Fullscreen with sportsbook background */}
        <div className="absolute inset-0 pt-16 bg-[#0F0F0F]">
          {/* Loading State with sportsbook styling */}
          {isIframeLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F0F]/90 backdrop-blur-sm">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--golden)] mx-auto mb-4"></div>
                <p className="text-white font-rubik">Loading sportsbook...</p>
              </div>
            </div>
          )}

          {/* Error State with sportsbook styling */}
          {(error || iframeError) && (
            <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F0F]/95">
              <div className="text-center max-w-md mx-auto p-6">
                <div className="w-16 h-16 mx-auto mb-4 text-red-500">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-rubik font-medium text-white mb-2">
                  Sportsbook Error
                </h3>
                <p className="text-[#9FA3B6] font-rubik mb-4">
                  {error || iframeError}
                </p>
                <button
                  onClick={onClose}
                  className="px-6 py-3 text-white font-rubik font-medium rounded-lg transition-all duration-200 hover:-translate-y-0.5 bg-golden-button shadow-golden-button hover:shadow-golden-button-hover"
                  style={{
                    background: 'linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)',
                  }}
                >
                  Close
                </button>
              </div>
            </div>
          )}

          {/* Iframe with SDK button styling injection */}
          {url && !error && (
            <iframe
              ref={iframeRef}
              src={url}
              className="w-full h-full border-0"
              onLoad={() => {
                onIframeLoad();
                // Inject custom styling for SDK buttons
                try {
                  const iframe = iframeRef.current;
                  if (iframe && iframe.contentDocument) {
                    const doc = iframe.contentDocument;

                    // Create style element for SDK button customization
                    const style = doc.createElement('style');
                    style.textContent = `
                      /* Import Rubik font */
                      @import url('https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700&display=swap');

                      /* SDK Button Styling to match PrimaryButton */
                      button, .btn, [role="button"], input[type="button"], input[type="submit"] {
                        font-family: 'Rubik', sans-serif !important;
                        background: linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%) !important;
                        color: white !important;
                        border: none !important;
                        border-radius: 8px !important;
                        padding: 12px 24px !important;
                        font-weight: 500 !important;
                        font-size: 14px !important;
                        line-height: 100% !important;
                        letter-spacing: 0% !important;
                        cursor: pointer !important;
                        transition: all 0.2s ease !important;
                        box-shadow:
                          0px 4px 12px 0px rgba(0, 0, 0, 0.55),
                          4px 4px 8px 0px rgba(255, 211, 116, 0.15) inset !important;
                      }

                      /* Hover effects for SDK buttons */
                      button:hover, .btn:hover, [role="button"]:hover,
                      input[type="button"]:hover, input[type="submit"]:hover {
                        transform: translateY(-2px) !important;
                        box-shadow:
                          0px 6px 16px 0px rgba(0, 0, 0, 0.65),
                          4px 4px 8px 0px rgba(255, 211, 116, 0.25) inset !important;
                      }

                      /* Category buttons specific styling */
                      .category-btn, .sport-category, .discipline-btn {
                        width: 195px !important;
                        height: 182px !important;
                        border-radius: 20px !important;
                        background: linear-gradient(193.45deg, #1D1C16 9.62%, #2D2100 90.24%) !important;
                        border: 2px solid rgba(255, 255, 255, 0.1) !important;
                        box-shadow:
                          0px 4px 12px 0px rgba(0, 0, 0, 0.55),
                          4px 4px 8px 0px rgba(255, 211, 116, 0.15) inset !important;
                        display: flex !important;
                        flex-direction: column !important;
                        align-items: center !important;
                        justify-content: center !important;
                        gap: 16px !important;
                        padding: 16px !important;
                      }

                      .category-btn:hover, .sport-category:hover, .discipline-btn:hover {
                        transform: scale(1.05) !important;
                        border-color: rgba(225, 182, 73, 0.5) !important;
                      }

                      /* SDK background matching */
                      body, html {
                        background-color: #0F0F0F !important;
                        font-family: 'Rubik', sans-serif !important;
                      }

                      /* Text styling */
                      .category-btn span, .sport-category span, .discipline-btn span,
                      .category-btn div, .sport-category div, .discipline-btn div {
                        color: white !important;
                        font-family: 'Rubik', sans-serif !important;
                        font-weight: 700 !important;
                        font-size: 20px !important;
                        line-height: 100% !important;
                        text-align: center !important;
                      }
                    `;

                    // Append style to head
                    const head = doc.head || doc.getElementsByTagName('head')[0];
                    if (head) {
                      head.appendChild(style);
                    }
                  }
                } catch (error) {
                  // eslint-disable-next-line no-console
                  console.warn('Could not inject SDK styling:', error);
                }
              }}
              onError={onIframeError}
              title={`TurboStars Sportsbook - ${userName || "User"}`}
              sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
              allow="fullscreen; payment; geolocation"
              style={{
                backgroundColor: '#0F0F0F',
              }}
            />
          )}
        </div>

        {/* Footer with sportsbook styling */}
        <div className="absolute bottom-0 left-0 right-0 z-20 p-3 border-t border-[#626573] bg-[#1D1D1D]/90 backdrop-blur-sm">
          <div className="flex items-center justify-between text-xs text-[#9FA3B6] font-rubik">
            <span>Press ESC to close</span>
            <span>TurboStars Sportsbook Integration</span>
          </div>
        </div>
      </div>
    </div>
  );
};
