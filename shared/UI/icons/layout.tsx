// shared/UI/icons/layout.tsx - Server-side layout for icons showcase with SEO
import React from "react";
import type { Metada<PERSON> } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface IconsLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for icons showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Icons - Icon Library Showcase",
	"Comprehensive showcase of icon libraries including Bootstrap Icons, Remix Icons, Feather Icons, and more for the Xintra platform.",
	"/ui/icons",
	{
		keywords: ["icons", "icon library", "bootstrap icons", "remix icons", "feather icons", "UI icons"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for icons showcase page
 * Provides SEO optimization and structured data for the icons page
 */
export default function IconsLayout({ children }: IconsLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: 'Home', url: '/' },
					{ name: 'UI Components', url: '/ui' },
					{ name: 'Icons' }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Icon Library Showcase"
				description="Comprehensive icon library showcase for modern web applications"
				url="/ui/icons"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
