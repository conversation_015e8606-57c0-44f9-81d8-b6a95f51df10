// shared/UI/components/modal/UserManagementModal.tsx
"use client";

import React from 'react';
import { UserDetailsData } from '@/shared/types/user-management-types';
import { CreateUserData, EditUserData } from '@/shared/query';
import { PrimaryButton } from '@/shared/UI/components';
import UserManagementModalForm from './UserManagementModalForm';
import UserManagementIcon from '@/shared/UI/components/icons/UserManagementIcon';
import BaseModal from './BaseModal';

export type UserManagementModalMode = 'create' | 'edit' | 'deactivate' | 'activate';

export interface UserManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: UserManagementModalMode;
  userData?: UserDetailsData;
  onSuccess?: () => void;
  onSubmit?: (formData: CreateUserData | EditUserData) => Promise<void>;
  showBackdrop?: boolean;
  className?: string;
  isLoading?: boolean;
}

/**
 * UserManagementModal Component
 * 
 * A reusable modal component for user management operations (create, edit, deactivate)
 * that follows the dark theme design system and replaces separate create user pages.
 * 
 * Features:
 * - Dimensions: 462px width × 518px height
 * - Dark theme styling with bg-background and bg-elevated
 * - Three modes: create, edit, deactivate
 * - Form validation and error handling
 * - Proper accessibility and keyboard support
 * - Integration with existing user management API endpoints
 */
const UserManagementModal: React.FC<UserManagementModalProps> = ({
  isOpen,
  onClose,
  mode,
  userData,
  onSuccess,
  onSubmit,
  showBackdrop = true,
  className = '',
  isLoading = false,
}) => {

  // Get modal title based on mode
  const getModalTitle = () => {
    switch (mode) {
      case 'create':
        return 'Add New User';
      case 'edit':
        return 'Edit User';
      case 'deactivate':
        return 'Deactivate User';
      case 'activate':
        return 'Activate User';
      default:
        return 'User Management';
    }
  };

  // Get action button text based on mode
  const getActionButtonText = () => {
    switch (mode) {
      case 'create':
        return 'Create User';
      case 'edit':
        return 'Update User';
      case 'deactivate':
        return 'Deactivate User';
      case 'activate':
        return 'Activate User';
      default:
        return 'Submit';
    }
  };

  // Get loading text based on mode
  const getLoadingText = () => {
    switch (mode) {
      case 'create':
        return 'Creating...';
      case 'edit':
        return 'Updating...';
      case 'deactivate':
        return 'Deactivating...';
      case 'activate':
        return 'Activating...';
      default:
        return 'Loading...';
    }
  };

  // Get confirmation message based on mode
  const getConfirmationMessage = () => {
    switch (mode) {
      case 'deactivate':
        return 'Do you confirm the deactivation of this user account from our platform.';
      case 'activate':
        return 'Do you confirm the activation of this user account on our platform.';
      default:
        return 'Are you sure you want to proceed?';
    }
  };

  // Create header icon
  const headerIcon = (
    <UserManagementIcon
      action={mode === 'create' ? 'add' : mode === 'edit' ? 'edit' : mode === 'activate' ? 'activate' : 'deactivate'}
      size={40}
      ariaHidden={true}
    />
  );

  // Modal content based on mode
  const modalContent = (mode === 'deactivate' || mode === 'activate') ? (
    // Deactivate/Activate Mode Content
    <div className="flex flex-col items-center justify-center h-full" style={{ gap: '8px', padding: '12px 16px' }}>
      {/* Title */}
      <h4
        className="font-rubik font-black text-center capitalize"
        style={{
          color: 'var(--golden)',
          fontSize: '20px',
          fontWeight: 900
        }}
      >
        Are you sure?
      </h4>

      {/* Subtitle */}
      <p
        className="font-rubik text-center px-[1rem]"
        style={{
          color: '#D4D4D4',
          fontSize: '16px',
          fontWeight: 500
        }}
      >
        {getConfirmationMessage()}
      </p>

      {/* Action Buttons */}
      <div className="flex w-[100%] gap-[8px] justify-between mt-4">
        {/* Cancel Button */}
        <PrimaryButton
          onClick={onClose}
          className="w-auto flex-1"
        >
          Cancel
        </PrimaryButton>

        {/* Confirm Button */}
        <button
          type="button"
          className="font-rubik flex-1 font-bold bg-secondary p-[16px] rounded-[8px] text-[18px] text-white transition-colors duration-200 hover:bg-opacity-80 disabled:opacity-50"
          disabled={isLoading}
          onClick={async () => {
            if (onSubmit && userData) {
              try {
                // Only send the required fields for activate/deactivate operations
                const updatePayload: Partial<EditUserData> & { id: number } = {
                  id: Number(userData.id),
                  active: mode === 'activate'
                };

                await onSubmit(updatePayload as EditUserData);
              } catch (error) {
                // eslint-disable-next-line no-console
                console.error('Error while deactivating/activating user:', error);
              }
            } else {
              onSuccess?.();
              onClose();
            }
          }}
        >
          {isLoading ? getLoadingText() : getActionButtonText()}
        </button>
      </div>
    </div>
  ) : (
    // Create/Edit Mode Content
    <div style={{ padding: '12px 16px', gap: '16px' }}>
      <UserManagementModalForm
        mode={mode}
        userData={userData}
        onSubmit={onSubmit || (async () => {
          // Default implementation if no onSubmit provided
          onSuccess?.();
          onClose();
        })}
        isLoading={isLoading}
      />
    </div>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={getModalTitle()}
      headerIcon={headerIcon}
      size="user-management"
      showBackdrop={showBackdrop}
      className={className}
      bodyClassName="p-0"
    >
      {modalContent}
    </BaseModal>
  );
};

export default UserManagementModal;
