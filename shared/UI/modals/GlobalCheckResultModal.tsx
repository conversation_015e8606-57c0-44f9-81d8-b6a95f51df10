// shared/UI/modals/GlobalCheckResultModal.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import CheckResultModal from './CheckResultModal';

/**
 * GlobalCheckResultModal Component
 * 
 * A global modal component that renders the Check Result modal based on URL parameters.
 * This provides a URL parameter-based approach for modal control.
 * 
 * Features:
 * - Controlled by URL parameters (?modal=check-result)
 * - Globally accessible from any page via navigation
 * - High z-index to appear on top of everything
 * - Copy/paste URL support - opening URL in new browser will show modal
 * - Works from horizontal navigation Check Result menu item
 */
const GlobalCheckResultModal: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  // Read modal state from URL parameters
  const modalParam = searchParams.get('modal');
  const isOpen = modalParam === 'check-result';

  // Handle modal close
  const handleClose = () => {
    const currentUrl = new URL(window.location.href);
    
    // Remove modal parameter
    currentUrl.searchParams.delete('modal');
    
    // Navigate to clean URL
    const newUrl = `${pathname}${currentUrl.search}`;
    router.push(newUrl, { scroll: false });
  };

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render on server or if not mounted
  if (!mounted || !isOpen) {
    return null;
  }

  return (
    <CheckResultModal
      isOpen={true}
      onClose={handleClose}
    />
  );
};

export default GlobalCheckResultModal;
