// shared/UI/modals/GlobalExportCenterModal.tsx - Global Export Center Modal Component

"use client";

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import ExportCenterModal from './ExportCenterModal';
import { DEFAULT_EXPORT_CENTER_FILTERS } from '@/shared/types/export-types';

/**
 * GlobalExportCenterModal Component
 * 
 * A global modal component that renders the Export Center based on URL parameters.
 * This provides a URL parameter-based approach for modal control.
 * 
 * Features:
 * - Controlled by URL parameters (?modal=export-center)
 * - Globally accessible from any page via navigation
 * - High z-index to appear on top of everything
 * - Copy/paste URL support - opening URL in new browser will show modal
 * - Works from horizontal navigation Export Center menu item
 */
const GlobalExportCenterModal: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  // Read modal state from URL parameters
  const modalParam = searchParams.get('modal');
  const isOpen = modalParam === 'export-center';

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle modal close
  const handleClose = () => {
    // Remove modal parameter from URL
    const currentParams = new URLSearchParams(searchParams.toString());
    currentParams.delete('modal');

    // Navigate to the same page without the modal parameter
    const newUrl = currentParams.toString()
      ? `${window.location.pathname}?${currentParams.toString()}`
      : window.location.pathname;

    router.push(newUrl);
  };

  // Don't render on server or if not mounted
  if (!mounted || !isOpen) {
    return null;
  }

  return (
    <ExportCenterModal
      isOpen={isOpen}
      onClose={handleClose}
      initialExportCenterResponse={undefined}
      initialFilters={DEFAULT_EXPORT_CENTER_FILTERS}
    />
  );
};

export default GlobalExportCenterModal;
