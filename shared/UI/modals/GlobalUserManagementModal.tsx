// shared/UI/components/modal/GlobalUserManagementModal.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { UserManagementModal } from '@/shared/UI/components';
import { useUserManagementModal } from '@/shared/hooks/business/useUserManagementModal';
import { useUserDetailsQuery } from '@/shared/query';

/**
 * GlobalUserManagementModal Component
 * 
 * A global modal component that renders based on URL parameters.
 * This provides a URL parameter-based approach for modal control.
 * 
 * Features:
 * - Controlled by URL parameters (?modal=user-management&mode=create/edit/deactivate/activate&userId=123)
 * - Globally accessible from any page
 * - High z-index to appear on top of everything
 * - Copy/paste URL support - opening URL in new browser will show modal
 * - Works from both user-management page and horizontal navigation
 */
const GlobalUserManagementModal: React.FC = () => {
  const searchParams = useSearchParams();
  const [mounted, setMounted] = useState(false);

  // Read modal state from URL parameters
  const modalParam = searchParams.get('modal');
  const modeParam = searchParams.get('mode');
  const userIdParam = searchParams.get('userId');

  const isOpen = modalParam === 'user-management';
  const mode = (modeParam as 'create' | 'edit' | 'deactivate' | 'activate') || 'create';
  const userId = userIdParam ? parseInt(userIdParam) : undefined;

  // Fetch user data if needed for edit/deactivate/activate modes
  const { data: userDetailsResponse, isLoading: isLoadingUser } = useUserDetailsQuery(
    userId?.toString() || ''
  );

  const {
    handleCreateUser,
    handleEditUser,
    handleDeactivateUser,
    handleActivateUser,
    closeModal,
    isCreating,
    isEditing,
    isDeactivating,
    isActivating,
  } = useUserManagementModal({
    onSuccess: () => {
      // Modal will be closed by the hook
    }
  });

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render on server or if not mounted
  if (!mounted || !isOpen) {
    return null;
  }

  // Show loading state while fetching user data
  if ((mode === 'edit' || mode === 'deactivate' || mode === 'activate') && userId && isLoadingUser) {
    return (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50">
        <div className="bg-background rounded-lg p-6">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[var(--golden)]"></div>
            <span className="text-white">Loading user data...</span>
          </div>
        </div>
      </div>
    );
  }

  // Handle form submission based on mode
  const handleSubmit = async (formData: any) => {
    switch (mode) {
      case 'create':
        await handleCreateUser(formData);
        break;
      case 'edit':
        if (userDetailsResponse?.data) {
          await handleEditUser(formData);
        }
        break;
      case 'deactivate':
        if (userDetailsResponse?.data) {
          await handleDeactivateUser(Number(userDetailsResponse.data.id));
        }
        break;
      case 'activate':
        if (userDetailsResponse?.data) {
          await handleActivateUser(Number(userDetailsResponse.data.id));
        }
        break;
    }
  };

  const isLoading = isCreating || isEditing || isDeactivating || isActivating;

  return (
    <UserManagementModal
      isOpen={true}
      onClose={closeModal}
      mode={mode}
      userData={userDetailsResponse?.data}
      onSubmit={handleSubmit}
      onSuccess={closeModal}
      isLoading={isLoading}
      showBackdrop={true}
    />
  );
};

export default GlobalUserManagementModal;
