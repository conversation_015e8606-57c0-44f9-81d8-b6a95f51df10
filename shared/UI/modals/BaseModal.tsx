"use client";

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

export interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  className?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
  showBackdrop?: boolean;
  closeOnBackdropClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full' | 'user-management';
  position?: 'center' | 'top' | 'bottom';
  animation?: 'fade' | 'slide' | 'scale';
  footer?: React.ReactNode;
  header?: React.ReactNode;
  headerIcon?: React.ReactNode;
  zIndex?: number;
  width?: string;
  maxHeight?: string;
}

/**
 * BaseModal Component
 *
 * A unified base modal component that provides common modal functionality following
 * the established design system. All other modal components should use this as a wrapper
 * to eliminate code duplication and ensure consistent styling.
 *
 * Features:
 * - Portal rendering for proper z-index layering (default z-index: 9999)
 * - Backdrop click and escape key handling
 * - Multiple size variants including 'user-management' (462px width)
 * - Custom width and maxHeight support
 * - Dark theme styling (bg-table-section, bg-elevated, border-border-secondary)
 * - Header with optional icon support and proper typography (Rubik font)
 * - Customizable animations (fade, slide, scale)
 * - Header, body, and footer sections with consistent padding
 * - Accessibility features (focus management, ARIA attributes, keyboard support)
 * - TypeScript interfaces for type safety
 * - Matches UserManagementModal design patterns and styling
 */
const BaseModal: React.FC<BaseModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  className = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  showBackdrop = true,
  closeOnBackdropClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  size = 'md',
  position = 'center',
  animation = 'fade',
  footer,
  header,
  headerIcon,
  zIndex = 9999,
  width,
  maxHeight = '90vh'
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (closeOnEscape && event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, closeOnEscape, onClose]);

  const getSizeClasses = () => {
    if (width) {
      return `mx-4`;
    }

    switch (size) {
      case 'sm':
        return 'max-w-sm w-full mx-4';
      case 'lg':
        return 'max-w-4xl w-full mx-4';
      case 'xl':
        return 'max-w-6xl w-full mx-4';
      case 'full':
        return 'w-full h-full m-0';
      case 'user-management':
        return 'mx-4';
      case 'md':
      default:
        return 'max-w-md w-full mx-4';
    }
  };

  const getModalWidth = () => {
    if (width) return width;
    if (size === 'user-management') return '462px';
    return undefined;
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'items-start pt-16';
      case 'bottom':
        return 'items-end pb-16';
      case 'center':
      default:
        return 'items-center';
    }
  };

  const getAnimationClasses = () => {
    const baseClasses = 'transition-all duration-300 ease-in-out';

    if (!isOpen) {
      switch (animation) {
        case 'slide':
          return `${baseClasses} transform translate-y-full opacity-0`;
        case 'scale':
          return `${baseClasses} transform scale-95 opacity-0`;
        case 'fade':
        default:
          return `${baseClasses} opacity-0`;
      }
    }

    return `${baseClasses} transform translate-y-0 scale-100 opacity-100`;
  };

  const handleBackdropClick = (event: React.MouseEvent) => {
    if (closeOnBackdropClick && event.target === event.currentTarget) {
      onClose();
    }
  };

  if (!mounted || !isOpen) return null;

  const modalContent = (
    <div
      className={`fixed inset-0 flex ${getPositionClasses()} justify-center p-4 transition-all overflow-x-hidden overflow-y-auto`}
      style={{ zIndex }}
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? 'modal-title' : undefined}
      tabIndex={-1}
    >
      {/* Backdrop */}
      {showBackdrop && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          style={{ zIndex: zIndex - 1 }}
          aria-hidden="true"
        />
      )}

      {/* Modal Container */}
      <div
        className={`relative ${getSizeClasses()} ${getAnimationClasses()}`}
        style={{
          width: getModalWidth(),
          maxHeight,
          zIndex: zIndex + 1
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Content */}
        <div className={`bg-table-section rounded-lg shadow-2xl overflow-hidden ${className}`}
          style={{ borderRadius: '8px' }}
        >
          {/* Header */}
          {(title || header || headerIcon || showCloseButton) && (
            <div className={`bg-elevated border-b border-border-secondary flex items-center justify-between ${headerClassName}`}
              style={{
                borderTopLeftRadius: '8px',
                borderTopRightRadius: '8px',
                padding: '16px'
              }}
            >
              <div className="flex items-center gap-3 flex-1">
                {headerIcon}
                {header || (
                  title && (
                    <h3
                      id="modal-title"
                      className="text-white font-rubik font-bold text-xl leading-none"
                      style={{ fontSize: '20px', lineHeight: '100%' }}
                    >
                      {title}
                    </h3>
                  )
                )}
              </div>
              {showCloseButton && (
                <button
                  type="button"
                  onClick={onClose}
                  className="w-6 h-6 flex items-center justify-center text-white hover:text-gray-300 transition-colors duration-200"
                  aria-label="Close modal"
                >
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          )}

          {/* Body */}
          <div className={`${bodyClassName}`}>
            {children}
          </div>

          {/* Footer */}
          {footer && (
            <div className={`bg-elevated border-t border-border-secondary ${footerClassName}`}
              style={{ padding: '16px' }}
            >
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default BaseModal;
