// shared/UI/modals/GlobalBulkUploadModal.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import BulkUploadModal from './BulkUploadModal';

/**
 * GlobalBulkUploadModal Component
 * 
 * A global modal component that renders the Bulk Upload modal based on URL parameters.
 * This provides a URL parameter-based approach for modal control.
 * 
 * Features:
 * - Controlled by URL parameters (?modal=bulk-upload)
 * - Globally accessible from any page via navigation
 * - High z-index to appear on top of everything
 * - Copy/paste URL support - opening URL in new browser will show modal
 * - Works from header bulk upload button
 */
const GlobalBulkUploadModal: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  // Read modal state from URL parameters
  const modalParam = searchParams.get('modal');
  const isOpen = modalParam === 'bulk-upload';

  // Handle modal close
  const handleClose = () => {
    const currentUrl = new URL(window.location.href);

    // Remove modal parameter
    currentUrl.searchParams.delete('modal');

    // Navigate to clean URL
    const newUrl = `${pathname}${currentUrl.search}`;
    router.push(newUrl, { scroll: false });
  };

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render on server or if not mounted
  if (!mounted || !isOpen) {
    return null;
  }

  return (
    <BulkUploadModal
      isOpen={true}
      onClose={handleClose}
    />
  );
};

export default GlobalBulkUploadModal;
