// shared/UI/components/modal/WalletTransactionModal.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { WalletTransactionModalProps, UserSelectionOption } from '@/shared/types/user-management-types';
import WalletTransactionForm from '../forms/WalletTransactionForm';
import { TabNavigation } from '../navigation/TabNavigation';
import BaseModal from './BaseModal';
import { CurrencyDisplay } from '../components';

export type WalletTransactionType = 'deposit' | 'withdraw';

interface ExtendedWalletTransactionModalProps extends WalletTransactionModalProps {
  userOptions?: UserSelectionOption[];
  showBackdrop?: boolean;
  className?: string;
  showUserSelection?: boolean;
  context?: 'global' | 'user-details'; // Context prop to control form behavior
  balance?: number; // User's current balance
}

/**
 * WalletTransactionModal Component
 *
 * A reusable modal component for wallet transactions (deposit/withdraw) that follows
 * the UserManagementModal structure and dark theme design system.
 *
 * Features:
 * - Dimensions: 462px width × dynamic height
 * - Dark theme styling with bg-background and bg-elevated
 * - Tab navigation for deposit/withdraw selection
 * - Wallet icon with cyan/teal theme (#00FFF2)
 * - Balance display with golden color (var(--golden))
 * - Form validation and error handling
 * - Integration with existing wallet transaction API endpoints
 */
const WalletTransactionModal: React.FC<ExtendedWalletTransactionModalProps> = ({
  isOpen,
  onClose,
  transactionType = 'deposit',
  preSelectedUser,
  onSuccess,
  showBackdrop = true,
  className = '',
  showUserSelection = true,
  balance = 10000.00 // Default balance for demo
}) => {
  const [currentTransactionType, setCurrentTransactionType] = useState<WalletTransactionType>(transactionType);

  // Handle successful transaction
  const handleTransactionSuccess = (transactionId: number) => {
    if (onSuccess) {
      onSuccess(transactionId);
    }

    // Auto-close modal after successful transaction (with delay for user to see success message)
    setTimeout(() => {
      onClose();
    }, 3000);
  };

  // Reset transaction type when modal opens
  useEffect(() => {
    if (isOpen) {
      setCurrentTransactionType(transactionType);
    }
  }, [isOpen, transactionType]);

  // Tab configuration for deposit/withdraw selection
  const tabs = [
    { id: 'withdraw' as WalletTransactionType, label: 'Withdraw' },
    { id: 'deposit' as WalletTransactionType, label: 'Deposit' }
  ];

  // Create wallet icon for header
  const walletIcon = (
    <div className="w-10 h-10 flex items-center justify-center">
      <svg width="40" height="41" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M35 12.5H30V8.5C30 6.29086 28.2091 4.5 26 4.5H6C3.79086 4.5 2 6.29086 2 8.5V32.5C2 34.7091 3.79086 36.5 6 36.5H34C36.2091 36.5 38 34.7091 38 32.5V16.5C38 14.2909 36.2091 12.5 34 12.5H35ZM6 8.5H26V12.5H6V8.5ZM34 32.5H6V16.5H34V32.5Z" fill="#00FFF2" />
        <circle cx="28" cy="24.5" r="2" fill="#00FFF2" />
      </svg>
    </div>
  );

  // Create custom header with balance display
  const customHeader = (
    <div className="flex items-center gap-3">
      {walletIcon}
      <div className="flex flex-col">
        {/* Heading */}
        <h3 className="font-rubik font-bold text-lg text-white capitalize">
          Wallet
        </h3>
        {/* Balance Display */}
        <div className="flex items-center gap-2 mt-1">
          <span className="font-rubik text-sm text-secondary">Balance</span>
          <div style={{ color: 'var(--golden)' }}>
            <CurrencyDisplay
              amount={balance}
              context="header"
              size={14}
              amountClassName="font-rubik text-sm font-semibold"
              gap="sm"
            />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      header={customHeader}
      size="user-management"
      showBackdrop={showBackdrop}
      className={className}
      bodyClassName="p-4 space-y-4"
    >
      {/* Tab Navigation for Withdraw/Deposit */}
      <div className="mb-4">
        <TabNavigation
          tabs={tabs}
          activeTab={currentTransactionType}
          onTabChange={setCurrentTransactionType}
          variant="golden"
          radius={"4px"}
          className="w-full bg-elevated rounded-[4px]"
        />
      </div>

      {/* Transaction Form */}
      <div>
        <WalletTransactionForm
          transactionType={currentTransactionType}
          preSelectedUser={preSelectedUser}
          onSuccess={handleTransactionSuccess}
          onCancel={onClose}
          showUserSelection={showUserSelection}
        />
      </div>
    </BaseModal>
  );
};

export default WalletTransactionModal;
