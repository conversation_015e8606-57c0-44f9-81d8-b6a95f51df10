// shared/UI/components/AccessToggleSwitch.tsx
"use client";

import React from 'react';
import { Icon } from './icons';

interface AccessToggleSwitchProps {
  id: string;
  label: string;
  icon: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

/**
 * AccessToggleSwitch Component
 * 
 * A custom toggle switch component designed for access control settings
 * with icon support and golden theme styling (var(--golden)).
 * 
 * Features:
 * - Icon integration with remix icons
 * - Golden theme active state (var(--golden))
 * - Dark theme compatibility
 * - Smooth transitions
 * - Accessibility support
 */
const AccessToggleSwitch: React.FC<AccessToggleSwitchProps> = ({
  id,
  label,
  icon,
  checked,
  onChange,
  disabled = false,
  className = ''
}) => {
  const handleToggle = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };

  return (
    <div className={`flex items-center justify-start gap-[6px] ${className}`}>
      {/* Label and Icon */}
      <div className="flex items-center gap-3">
        <Icon
          type="FONT_ICON"
          iconClass={icon}
          library="remix"
          size={20}
          className={`${checked ? 'text-[var(--golden)]' : 'text-gray-400'} transition-colors duration-200`}
        />
        <label 
          htmlFor={id}
          className="font-rubik font-semibold text-white text-sm cursor-pointer"
        >
          {label}
        </label>
      </div>

      {/* Toggle Switch */}
      <div className="relative">
        <input
          type="checkbox"
          id={id}
          checked={checked}
          onChange={handleToggle}
          disabled={disabled}
          className="sr-only"
        />
        <div
          onClick={handleToggle}
          className={`
            relative w-12 h-6 rounded-full cursor-pointer transition-all duration-200 ease-in-out
            ${checked 
              ? 'bg-[var(--golden)]' 
              : 'bg-gray-600'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          {/* Toggle Thumb */}
          <div
            className={`
              absolute top-0.5 w-5 h-5 bg-white rounded-full shadow-md transition-transform duration-200 ease-in-out
              ${checked ? 'translate-x-6' : 'translate-x-0.5'}
            `}
          />
        </div>
      </div>
    </div>
  );
};

export default AccessToggleSwitch;
