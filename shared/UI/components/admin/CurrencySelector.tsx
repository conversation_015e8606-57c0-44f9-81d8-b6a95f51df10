// shared/UI/components/admin/CurrencySelector.tsx
// Admin component for switching application currency
// This is a demo component - in production, you'd change the config file directly

"use client";

import React, { useState } from 'react';
import { useCurrency } from '@/shared/hooks/useCurrency';
import { AVAILABLE_CURRENCIES } from '@/shared/config/currencyConfig';
import { CurrencyDisplay, CurrencyIcon } from '@/shared/UI/components';

export interface CurrencySelectorProps {
  /** Additional CSS classes */
  className?: string;
  /** Show as compact selector */
  compact?: boolean;
}

/**
 * Currency Selector Component
 * 
 * Demo admin component that shows how the centralized currency system works.
 * In a real application, you would change the DEFAULT_CURRENCY_CODE in 
 * currencyConfig.ts directly, but this component demonstrates the system's
 * flexibility for future multi-currency support.
 * 
 * Features:
 * - Live preview of currency changes
 * - Shows all available currencies
 * - Demonstrates icon variants and formatting
 * - Educational tool for understanding the system
 * 
 * Usage:
 * ```tsx
 * <CurrencySelector />
 * <CurrencySelector compact />
 * ```
 */
const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  className = '',
  compact = false
}) => {
  const { currency, setCurrency, formatCurrency: _formatCurrency } = useCurrency();
  const [selectedCurrency, setSelectedCurrency] = useState(currency.code);

  // Sample amounts for demonstration
  const sampleAmounts = [100, 1500.50, 25000, 100000];

  const handleCurrencyChange = (currencyCode: string) => {
    setSelectedCurrency(currencyCode);
    setCurrency(currencyCode);
  };

  if (compact) {
    return (
      <div className={`inline-flex items-center gap-2 ${className}`}>
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Currency:
        </label>
        <select
          value={selectedCurrency}
          onChange={(e) => handleCurrencyChange(e.target.value)}
          className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        >
          {Object.entries(AVAILABLE_CURRENCIES).map(([code, config]) => (
            <option key={code} value={code}>
              {config.name} ({config.symbol})
            </option>
          ))}
        </select>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          🎯 Currency System Demo
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          This demonstrates the centralized currency system. In production, change
          <code className="mx-1 px-1 bg-gray-100 dark:bg-gray-700 rounded">DEFAULT_CURRENCY_CODE</code>
          in <code className="mx-1 px-1 bg-gray-100 dark:bg-gray-700 rounded">currencyConfig.ts</code>
        </p>
      </div>

      {/* Current Currency Display */}
      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
        <div className="flex items-center gap-3 mb-2">
          <CurrencyIcon context="default" size={24} />
          <div>
            <div className="font-semibold text-blue-900 dark:text-blue-100">
              Current: {currency.name}
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300">
              Code: {currency.code} | Symbol: {currency.symbol}
            </div>
          </div>
        </div>

        {/* Sample Amounts */}
        <div className="grid grid-cols-2 gap-2 mt-3">
          {sampleAmounts.map((amount) => (
            <div key={amount} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {amount.toLocaleString()}:
              </span>
              <CurrencyDisplay
                amount={amount}
                context="default"
                size={14}
                amountClassName="font-medium"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Currency Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Select Currency:
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {Object.entries(AVAILABLE_CURRENCIES).map(([code, config]) => (
            <button
              key={code}
              onClick={() => handleCurrencyChange(code)}
              className={`p-3 text-left border rounded-lg transition-colors ${selectedCurrency === code
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100'
                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-800'
                }`}
            >
              <div className="flex items-center gap-2 mb-1">
                {config.hasCustomIcon ? (
                  <CurrencyIcon context="default" size={16} />
                ) : (
                  <span className="text-sm font-semibold">{config.symbol}</span>
                )}
                <span className="font-medium">{config.name}</span>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {code} • {config.hasCustomIcon ? 'Custom Icons' : 'Text Symbol'}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Context Examples */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Context Examples:
        </h4>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="p-2 bg-gray-900 rounded text-white">
            <div className="text-xs text-gray-400 mb-1">Header Context:</div>
            <CurrencyDisplay amount={5000} context="header" size={14} amountClassName="text-white" />
          </div>
          <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded">
            <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Table Context:</div>
            <CurrencyDisplay amount={1500} context="table" size={14} />
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
        <div className="text-xs text-yellow-800 dark:text-yellow-200">
          <strong>💡 Production Usage:</strong> To permanently change the currency,
          edit <code>DEFAULT_CURRENCY_CODE</code> in <code>shared/config/currencyConfig.ts</code>
        </div>
      </div>
    </div>
  );
};

export default CurrencySelector;
