// shared/UI/components/CopyToClipboard.tsx
import React, { useCallback, useState } from 'react';
import { Icon } from './icons';

interface CopyToClipboardProps {
  text: string;
  className?: string;
  iconSize?: number;
  showTooltip?: boolean;
  tooltipText?: string;
  successText?: string;
  onCopy?: (text: string) => void;
}

/**
 * CopyToClipboard Component
 * 
 * A reusable component that provides copy-to-clipboard functionality with visual feedback.
 * Designed for use in table ID fields and other text elements that need copying capability.
 * 
 * Features:
 * - Click to copy text to clipboard
 * - Visual feedback with icon change and tooltip
 * - Customizable styling and text
 * - Error handling for clipboard API
 * - Accessibility support
 */
const CopyToClipboard: React.FC<CopyToClipboardProps> = ({
  text,
  className = '',
  iconSize = 16,
  showTooltip = true,
  tooltipText = 'Copy to clipboard',
  successText = 'Copied!',
  onCopy
}) => {
  const [isCopied, setIsCopied] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleCopy = useCallback(async () => {
    try {
      // Use the modern Clipboard API if available
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }

      // Show success feedback
      setIsCopied(true);

      // Call optional callback
      if (onCopy) {
        onCopy(text);
      }

      // Reset success state after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);

    } catch (error) {
      //eslint-disable-next-line no-console
      console.error('Failed to copy text to clipboard:', error);
      // Could add error toast notification here if needed
    }
  }, [text, onCopy]);

  return (
    <div className="relative inline-flex items-center">
      <button
        onClick={handleCopy}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={`
          inline-flex items-center justify-center
          p-1 rounded transition-colors duration-200
          hover:bg-surface focus:outline-none focus:ring-2 focus:ring-primary/20
          ${className}
        `}
        aria-label={isCopied ? successText : tooltipText}
        title={isCopied ? successText : tooltipText}
      >
        <Icon
          type="FONT_ICON"
          iconClass={isCopied ? "check-line" : "clipboard-line"}
          library="remix"
          size={iconSize}
          className={`
            transition-colors duration-200
            ${isCopied
              ? 'text-green-500'
              : isHovered
                ? 'text-primary'
                : 'text-text-muted hover:text-white'
            }
          `}
        />
      </button>

      {/* Tooltip */}
      {showTooltip && (isHovered || isCopied) && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50">
          <div className="bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
            {isCopied ? successText : tooltipText}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CopyToClipboard;
