import React, { Fragment } from 'react';

export interface SpkLegendIndicatorProps {
  /** The color variant of the indicator */
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' | 
           'gray' | 'red' | 'yellow' | 'green' | 'blue' | 'indigo' | 'purple' | 'pink' | 'black';
  /** Custom color class for the indicator dot */
  color?: string;
  /** The label text to display next to the indicator */
  label: string;
  /** Size of the indicator dot */
  size?: 'sm' | 'md' | 'lg';
  /** Additional CSS classes */
  className?: string;
  /** Click handler for interactive indicators */
  onClick?: () => void;
  /** Whether the indicator is disabled */
  disabled?: boolean;
}

const SpkLegendIndicator: React.FC<SpkLegendIndicatorProps> = ({
  variant = 'gray',
  color,
  label,
  size = 'md',
  className = '',
  onClick,
  disabled = false
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'w-1.5 h-1.5';
      case 'lg':
        return 'w-3 h-3';
      default:
        return 'w-2 h-2';
    }
  };

  const getVariantClass = () => {
    if (color) {
      return color;
    }

    switch (variant) {
      case 'primary':
        return 'bg-primary';
      case 'secondary':
        return 'bg-secondary';
      case 'success':
        return 'bg-success';
      case 'danger':
        return 'bg-danger';
      case 'warning':
        return 'bg-warning';
      case 'info':
        return 'bg-info';
      case 'light':
        return 'bg-light';
      case 'dark':
        return 'bg-black/20 dark:bg-white';
      case 'gray':
        return 'bg-gray-500';
      case 'red':
        return 'bg-red-600';
      case 'yellow':
        return 'bg-yellow-600';
      case 'green':
        return 'bg-green-600';
      case 'blue':
        return 'bg-blue-600';
      case 'indigo':
        return 'bg-indigo-600';
      case 'purple':
        return 'bg-purple-600';
      case 'pink':
        return 'bg-pink-600';
      case 'black':
        return 'bg-black/20 dark:bg-white/80';
      default:
        return 'bg-gray-500 dark:bg-white/80';
    }
  };

  const indicatorClasses = [
    getSizeClass(),
    'inline-block',
    'rounded-full',
    'me-2',
    getVariantClass(),
    disabled ? 'opacity-50' : ''
  ].filter(Boolean).join(' ');

  const containerClasses = [
    'inline-flex',
    'items-center',
    onClick && !disabled ? 'cursor-pointer hover:opacity-80 transition-opacity' : '',
    disabled ? 'opacity-50 cursor-not-allowed' : '',
    className
  ].filter(Boolean).join(' ');

  const labelClasses = [
    'text-defaulttextcolor',
    'dark:text-defaulttextcolor/80',
    disabled ? 'opacity-50' : ''
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div 
        className={containerClasses}
        onClick={onClick && !disabled ? onClick : undefined}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick && !disabled ? 0 : undefined}
        onKeyDown={onClick && !disabled ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        } : undefined}
      >
        <span className={indicatorClasses}></span>
        <span className={labelClasses}>{label}</span>
      </div>
    </Fragment>
  );
};

export default SpkLegendIndicator;
