"use client";

import React from 'react';

// User Management Action Types
export type UserManagementActionType = 'edit' | 'add' | 'deactivate' | 'activate';

// Theme configuration for each action type
export interface UserManagementIconTheme {
  backgroundColor: string;
  backgroundOpacity: string;
  iconColor: string;
}

// Theme definitions matching the original design
const THEMES: Record<UserManagementActionType, UserManagementIconTheme> = {
  edit: {
    backgroundColor: '#9E5CF7',
    backgroundOpacity: '0.2',
    iconColor: '#9E5CF7'
  },
  add: {
    backgroundColor: '#FF8E6F',
    backgroundOpacity: '0.2',
    iconColor: '#FF8E6F'
  },
  deactivate: {
    backgroundColor: '#FB4242',
    backgroundOpacity: '0.2',
    iconColor: '#FB4242'
  },
  activate: {
    backgroundColor: '#4CAF50',
    backgroundOpacity: '0.2',
    iconColor: '#4CAF50'
  }
};

// SVG path definitions for each action type
const SVG_PATHS: Record<UserManagementActionType, string> = {
  edit: `M13.2793 13.8077C13.2793 12.9557 13.5319 12.1229 14.0053 11.4145C14.4787 10.7061 15.1515 10.1539 15.9387 9.82791C16.7259 9.50187 17.5921 9.41656 18.4278 9.58277C19.2635 9.74899 20.0311 10.1593 20.6336 10.7617C21.2361 11.3641 21.6464 12.1317 21.8126 12.9673C21.9788 13.8029 21.8935 14.669 21.5675 15.4562C21.2414 16.2433 20.6892 16.9161 19.9808 17.3894C19.2723 17.8627 18.4394 18.1154 17.5873 18.1154C16.4448 18.1154 15.349 17.6615 14.5411 16.8537C13.7331 16.0458 13.2793 14.9502 13.2793 13.8077ZM24.1011 22.0785C24.0871 21.979 24.0387 21.8876 23.9643 21.82C23.3991 21.2906 22.733 20.8805 22.0058 20.6141C21.2786 20.3476 20.5052 20.2304 19.7317 20.2692H15.4236C11.0509 20.2692 9.5 23.4677 9.5 26.2138C9.5 28.6692 10.8032 29.9615 13.2696 29.9615H18.87C18.9125 29.9628 18.9548 29.9547 18.9938 29.938C19.0329 29.9212 19.0678 29.8962 19.0962 29.8646C19.1561 29.8021 19.1907 29.7196 19.1932 29.6331V27.8077C19.1935 27.3794 19.364 26.9688 19.667 26.6662L24.0074 22.3046C24.0663 22.2439 24.0998 22.163 24.1011 22.0785ZM20.8087 27.8077V30.5H23.5012L28.3898 25.6334L25.6768 22.9206L20.8087 27.8077ZM30.1873 22.3272L28.9811 21.1211C28.7793 20.9205 28.5063 20.8079 28.2218 20.8079C27.9373 20.8079 27.6643 20.9205 27.4625 21.1211L26.4296 22.1657L29.1426 24.8785L30.1873 23.8457C30.3876 23.644 30.5 23.3712 30.5 23.087C30.5 22.8028 30.3876 22.529 30.1873 22.3272Z`,

  add: `M24.375 14.75C24.375 17.1662 22.4162 19.125 20 19.125C17.5838 19.125 15.625 17.1662 15.625 14.75C15.625 12.3338 17.5838 10.375 20 10.375C22.4162 10.375 24.375 12.3338 24.375 14.75ZM18.25 20.875C14.8673 20.875 12.125 23.6173 12.125 27C12.125 28.4498 13.3003 29.625 14.75 29.625H21.2331C21.6113 29.625 21.8261 29.1631 21.6303 28.8395C21.1509 28.0475 20.875 27.1185 20.875 26.125C20.875 24.3452 21.7607 22.7722 23.1153 21.8227C23.4333 21.5999 23.4062 21.0887 23.0264 21.0082C22.6147 20.9209 22.1877 20.875 21.75 20.875H18.25ZM26.125 22.625C26.6083 22.625 27 23.0167 27 23.5V25.25H28.75C29.2333 25.25 29.625 25.6417 29.625 26.125C29.625 26.6083 29.2333 27 28.75 27H27V28.75C27 29.2332 26.6083 29.625 26.125 29.625C25.6417 29.625 25.25 29.2332 25.25 28.75V27H23.5C23.0167 27 22.625 26.6083 22.625 26.125C22.625 25.6417 23.0167 25.25 23.5 25.25H25.25V23.5C25.25 23.0167 25.6417 22.625 26.125 22.625Z`,

  deactivate: `M19.3437 25.25C19.3455 23.9481 19.8303 22.6931 20.7043 21.7281C21.5783 20.763 22.7792 20.1566 24.0747 20.0263C23.7437 19.6077 23.322 19.2696 22.8413 19.0377C22.3607 18.8058 21.8337 18.6861 21.3 18.6875C21.1982 18.6875 21.0977 18.7113 21.0065 18.7568C20.9154 18.8024 20.8362 18.8685 20.775 18.95C19.6935 20.3787 16.3709 20.3787 15.2894 18.95C15.2281 18.8688 15.1488 18.8028 15.0577 18.7572C14.9666 18.7117 14.8663 18.6878 14.7644 18.6875C13.8786 18.6831 13.0235 19.0121 12.3691 19.6092C11.7147 20.2063 11.309 21.0278 11.2325 21.9104L10.8125 26.5625C10.8125 27.2587 11.0891 27.9264 11.5813 28.4187C12.0736 28.911 12.7413 29.1875 13.4375 29.1875H21.1327C20.5712 28.6966 20.1211 28.0913 19.8125 27.4122C19.504 26.7331 19.3442 25.9959 19.3437 25.25Z M18.0312 10.8125C12.7812 10.8125 12.7812 18.6875 18.0312 18.6875C23.2812 18.6875 23.2812 10.8125 18.0312 10.8125Z M24.5938 21.3125C23.815 21.3125 23.0537 21.5434 22.4062 21.9761C21.7587 22.4087 21.254 23.0237 20.956 23.7432C20.658 24.4627 20.58 25.2544 20.7319 26.0182C20.8838 26.782 21.2588 27.4836 21.8095 28.0342C22.3602 28.5849 23.0618 28.9599 23.8256 29.1118C24.5894 29.2638 25.3811 29.1858 26.1006 28.8878C26.8201 28.5898 27.435 28.0851 27.8677 27.4376C28.3003 26.79 28.5313 26.0288 28.5313 25.25C28.53 24.2061 28.1148 23.2053 27.3766 22.4671C26.6385 21.7289 25.6377 21.3137 24.5938 21.3125ZM25.9063 25.9062H23.2813C23.1072 25.9062 22.9403 25.8371 22.8172 25.714C22.6941 25.591 22.625 25.424 22.625 25.25C22.625 25.076 22.6941 24.909 22.8172 24.786C22.9403 24.6629 23.1072 24.5938 23.2813 24.5938H25.9063C26.0803 24.5938 26.2472 24.6629 26.3703 24.786C26.4934 24.909 26.5625 25.076 26.5625 25.25C26.5625 25.424 26.4934 25.591 26.3703 25.714C26.2472 25.8371 26.0803 25.9062 25.9063 25.9062Z`,

  activate: `M19.3437 25.25C19.3455 23.9481 19.8303 22.6931 20.7043 21.7281C21.5783 20.763 22.7792 20.1566 24.0747 20.0263C23.7437 19.6077 23.322 19.2696 22.8413 19.0377C22.3607 18.8058 21.8337 18.6861 21.3 18.6875C21.1982 18.6875 21.0977 18.7113 21.0065 18.7568C20.9154 18.8024 20.8362 18.8685 20.775 18.95C19.6935 20.3787 16.3709 20.3787 15.2894 18.95C15.2281 18.8688 15.1488 18.8028 15.0577 18.7572C14.9666 18.7117 14.8663 18.6878 14.7644 18.6875C13.8786 18.6831 13.0235 19.0121 12.3691 19.6092C11.7147 20.2063 11.309 21.0278 11.2325 21.9104L10.8125 26.5625C10.8125 27.2587 11.0891 27.9264 11.5813 28.4187C12.0736 28.911 12.7413 29.1875 13.4375 29.1875H21.1327C20.5712 28.6966 20.1211 28.0913 19.8125 27.4122C19.504 26.7331 19.3442 25.9959 19.3437 25.25Z M18.0312 10.8125C12.7812 10.8125 12.7812 18.6875 18.0312 18.6875C23.2812 18.6875 23.2812 10.8125 18.0312 10.8125Z M24.5938 21.3125C23.815 21.3125 23.0537 21.5434 22.4062 21.9761C21.7587 22.4087 21.254 23.0237 20.956 23.7432C20.658 24.4627 20.58 25.2544 20.7319 26.0182C20.8838 26.782 21.2588 27.4836 21.8095 28.0342C22.3602 28.5849 23.0618 28.9599 23.8256 29.1118C24.5894 29.2638 25.3811 29.1858 26.1006 28.8878C26.8201 28.5898 27.435 28.0851 27.8677 27.4376C28.3003 26.79 28.5313 26.0288 28.5313 25.25C28.53 24.2061 28.1148 23.2053 27.3766 22.4671C26.6385 21.7289 25.6377 21.3137 24.5938 21.3125ZM26.5625 25.25C26.5625 25.424 26.4934 25.591 26.3703 25.714C26.2472 25.8371 26.0803 25.9062 25.9063 25.9062H25.25V26.5625C25.25 26.7365 25.1809 26.9034 25.0578 27.0265C24.9347 27.1496 24.7678 27.2187 24.5938 27.2187C24.4197 27.2187 24.2528 27.1496 24.1297 27.0265C24.0066 26.9034 23.9375 26.7365 23.9375 26.5625V25.9062H23.2813C23.1072 25.9062 22.9403 25.8371 22.8172 25.714C22.6941 25.591 22.625 25.424 22.625 25.25C22.625 25.076 22.6941 24.909 22.8172 24.786C22.9403 24.6629 23.1072 24.5938 23.2813 24.5938H23.9375V23.9375C23.9375 23.7635 24.0066 23.5966 24.1297 23.4735C24.2528 23.3504 24.4197 23.2813 24.5938 23.2813C24.7678 23.2813 24.9347 23.3504 25.0578 23.4735C25.1809 23.5966 25.25 23.7635 25.25 23.9375V24.5938H25.9063C26.0803 24.5938 26.2472 24.6629 26.3703 24.786C26.4934 24.909 26.5625 25.076 26.5625 25.25Z`
};

// Props interface
export interface UserManagementIconProps {
  /** The type of user management action */
  action: UserManagementActionType;
  /** Custom CSS classes */
  className?: string;
  /** Icon size (width and height) */
  size?: number;
  /** Custom theme override */
  theme?: Partial<UserManagementIconTheme>;
  /** Accessibility label */
  ariaLabel?: string;
  /** Whether the icon is decorative (hidden from screen readers) */
  ariaHidden?: boolean;
  /** Tooltip title */
  title?: string;
}

/**
 * UserManagementIcon Component
 * 
 * A specialized icon component for user management actions that provides
 * themed SVG icons for edit, add, and deactivate operations.
 * 
 * Features:
 * - Three predefined action types with consistent theming
 * - Purple theme for edit actions
 * - Orange theme for add actions  
 * - Red theme for deactivate actions
 * - Customizable size and styling
 * - Full accessibility support
 * - TypeScript type safety
 * 
 * @example
 * ```tsx
 * // Edit user icon
 * <UserManagementIcon action="edit" size={40} />
 * 
 * // Add user icon with custom class
 * <UserManagementIcon action="add" className="mr-2" />
 * 
 * // Deactivate user icon with accessibility
 * <UserManagementIcon 
 *   action="deactivate" 
 *   ariaLabel="Deactivate user account"
 * />
 * ```
 */
const UserManagementIcon: React.FC<UserManagementIconProps> = ({
  action,
  className = '',
  size = 40,
  theme,
  ariaLabel,
  ariaHidden = false,
  title
}) => {
  // Get the theme for the action type
  const actionTheme = THEMES[action];
  const finalTheme = theme ? { ...actionTheme, ...theme } : actionTheme;

  // Get the SVG path for the action type
  const svgPath = SVG_PATHS[action];

  // Generate accessibility attributes
  const accessibilityProps = {
    'aria-label': ariaLabel,
    'aria-hidden': ariaHidden,
    title,
    role: ariaHidden ? undefined : 'img'
  };

  return (
    <div
      className={`inline-flex items-center justify-center ${className}`}
      style={{ width: size, height: size }}
      {...accessibilityProps}
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background rectangle with theme color */}
        <rect
          width="40"
          height="40"
          rx="4.57143"
          fill={finalTheme.backgroundColor}
          fillOpacity={finalTheme.backgroundOpacity}
        />

        {/* Icon path with theme color */}
        <path
          d={svgPath}
          fill={finalTheme.iconColor}
          fillRule="evenodd"
          clipRule="evenodd"
        />
      </svg>
    </div>
  );
};

export default UserManagementIcon;
