"use client";

import React from 'react';
import Icon from './Icon';
import { getSVGCacheInfo } from './SVGLoader';

/**
 * IconShowcase Component
 *
 * Demonstrates the usage of the optimized Icon system
 * and provides performance comparison examples.
 */
const IconShowcase: React.FC = () => {

  const handleCacheInfo = () => {
    getSVGCacheInfo();
  };

  return (
    <div className="p-8 space-y-8 bg-background text-foreground">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Optimized Icon System Showcase</h1>

        {/* Performance Comparison */}
        <div className="bg-card p-6 rounded-lg mb-8">
          <h2 className="text-xl font-semibold mb-4">Performance Comparison</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Optimized Local SVG */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-green-600">✅ Optimized (Recommended)</h3>
              <div className="flex items-center gap-2">
                <Icon type="SVG_LOCAL" name="plus" size={24} />
                <span>Local SVG with caching</span>
              </div>
              <div className="text-sm text-muted-foreground">
                • Cached after first load<br/>
                • No network requests<br/>
                • Better performance
              </div>
            </div>

            {/* Legacy External SVG */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-orange-600">⚠️ Legacy (Not Recommended)</h3>
              <div className="flex items-center gap-2">
                <Icon
                  type="SVG"
                  url="/assets/icon-fonts/tabler-icons/icons/plus.svg"
                  size={24}
                />
                <span>External SVG URL</span>
              </div>
              <div className="text-sm text-muted-foreground">
                • Network request each time<br/>
                • Slower loading<br/>
                • Use only for external icons
              </div>
            </div>
          </div>
        </div>

        {/* Icon Types Demo */}
        <div className="bg-card p-6 rounded-lg mb-8">
          <h2 className="text-xl font-semibold mb-4">Icon Types</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Font Icons */}
            <div className="space-y-2">
              <h3 className="font-medium">Font Icons</h3>
              <div className="flex items-center gap-2">
                <Icon type="FONT_ICON" iconClass="user-line" library="remix" size={20} />
                <span className="text-sm">Remix Icons</span>
              </div>
            </div>

            {/* Local SVG */}
            <div className="space-y-2">
              <h3 className="font-medium">Local SVG</h3>
              <div className="flex items-center gap-2">
                <Icon type="SVG_LOCAL" name="edit" size={20} />
                <span className="text-sm">Cached SVG</span>
              </div>
            </div>

            {/* Images */}
            <div className="space-y-2">
              <h3 className="font-medium">Images</h3>
              <div className="flex items-center gap-2">
                <Icon
                  type="IMAGE"
                  src="/assets/icon-fonts/tabler-icons/icons/search.svg"
                  alt="Search"
                  width={20}
                  height={20}
                />
                <span className="text-sm">Image Icons</span>
              </div>
            </div>

            {/* External SVG */}
            <div className="space-y-2">
              <h3 className="font-medium">External SVG</h3>
              <div className="flex items-center gap-2">
                <Icon
                  type="SVG"
                  url="/assets/icon-fonts/tabler-icons/icons/filter.svg"
                  size={20}
                />
                <span className="text-sm">External SVG</span>
              </div>
            </div>
          </div>
        </div>

        {/* Cache Management */}
        <div className="bg-card p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Cache Management</h2>
          <div className="space-y-4">
            <button
              onClick={handleCacheInfo}
              className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Log Cache Info
            </button>
            <p className="text-sm text-muted-foreground">
              Check browser console for SVG cache information
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IconShowcase;
