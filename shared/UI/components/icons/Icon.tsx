"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import SV<PERSON><PERSON>oader, { type SVGIconName } from './SVGLoader';

// Icon type definitions
export type IconType = 'SVG' | 'SVG_LOCAL' | 'IMAGE' | 'ICON_COMPONENT' | 'FONT_ICON';

export interface BaseIconProps {
  className?: string;
  size?: string | number;
  color?: string;
  ariaLabel?: string;
  ariaHidden?: boolean;
  title?: string;
}

export interface SVGIconProps extends BaseIconProps {
  type: 'SVG';
  url: string;
  sanitize?: boolean;
}

export interface SVGLocalIconProps extends BaseIconProps {
  type: 'SVG_LOCAL';
  name: SVGIconName;
  fallback?: React.ReactNode;
}

export interface ImageIconProps extends BaseIconProps {
  type: 'IMAGE';
  src: string;
  alt: string;
  width?: number;
  height?: number;
}

export interface ComponentIconProps extends BaseIconProps {
  type: 'ICON_COMPONENT';
  component: React.ComponentType<any>;
  props?: Record<string, any>;
}

export interface FontIconProps extends BaseIconProps {
  type: 'FONT_ICON';
  iconClass: string;
  library?: 'remix' | 'bootstrap' | 'tabler' | 'feather' | 'lineawesome' | 'boxicons';
}

export type IconProps = SVGIconProps | SVGLocalIconProps | ImageIconProps | ComponentIconProps | FontIconProps;

// Basic SVG sanitization
const sanitizeSVG = (svgString: string): string => {
  return svgString
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/on\w+='[^']*'/gi, '')
    .replace(/javascript:/gi, '');
};

// SVG fetching hook
const useSVGContent = (url: string, sanitize: boolean = true) => {
  const [svgContent, setSvgContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!url) return;

    const fetchSVG = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        const content = await response.text();
        const processedContent = sanitize ? sanitizeSVG(content) : content;
        setSvgContent(processedContent);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load SVG');
      } finally {
        setLoading(false);
      }
    };

    fetchSVG();
  }, [url, sanitize]);

  return { svgContent, loading, error };
};

// Separate component for SVG icons to avoid conditional hook usage
const SVGIcon: React.FC<SVGIconProps & { className?: string; style?: React.CSSProperties; 'aria-label'?: string; 'aria-hidden'?: boolean; title?: string }> = (props) => {
  const { url, sanitize = true, className, style, 'aria-label': ariaLabel, 'aria-hidden': ariaHidden, title } = props;
  const { svgContent, loading, error } = useSVGContent(url, sanitize);

  const commonProps = {
    className,
    style,
    'aria-label': ariaLabel,
    'aria-hidden': ariaHidden,
    title,
  };

  if (loading) {
    return (
      <div {...commonProps} className={`inline-block animate-pulse bg-gray-300 rounded ${className}`}>
        <span className="sr-only">Loading icon...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div {...commonProps} className={`inline-block text-red-500 ${className}`}>
        <span className="sr-only">Failed to load icon</span>
        ⚠️
      </div>
    );
  }

  if (!svgContent) {
    return null;
  }

  return (
    <div
      {...commonProps}
      dangerouslySetInnerHTML={{ __html: svgContent }}
    />
  );
};

/**
 * Flexible Icon Component
 *
 * Supports multiple icon types:
 * - SVG: External SVG URLs (legacy, not recommended for performance)
 * - SVG_LOCAL: Local SVG files (recommended)
 * - IMAGE: Regular images
 * - ICON_COMPONENT: React components
 * - FONT_ICON: Icon fonts (Remix, Bootstrap, etc.)
 */
const Icon: React.FC<IconProps> = (props) => {
  const { className = '', size, color, ariaLabel, ariaHidden, title } = props;

  const commonStyles: React.CSSProperties = {
    ...(size && { width: size, height: size }),
    ...(color && { color }),
  };

  const commonProps = {
    className,
    style: commonStyles,
    'aria-label': ariaLabel,
    'aria-hidden': ariaHidden,
    title,
  };

  // SVG Icon Type (External URLs - Legacy)
  if (props.type === 'SVG') {
    return <SVGIcon {...props} {...commonProps} />;
  }

  // Local SVG Icon Type (Optimized)
  if (props.type === 'SVG_LOCAL') {
    const { name, fallback, ...restProps } = props;

    return (
      <SVGLoader
        name={name}
        fallback={fallback}
        {...restProps}
        {...commonProps}
      />
    );
  }

  // Image Icon Type
  if (props.type === 'IMAGE') {
    const { src, alt, width = 24, height = 24 } = props;

    return (
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        {...commonProps}
      />
    );
  }

  // Component Icon Type
  if (props.type === 'ICON_COMPONENT') {
    const { component: IconComponent, props: iconProps = {} } = props;

    return (
      <IconComponent
        {...iconProps}
        {...commonProps}
      />
    );
  }

  // Font Icon Type
  if (props.type === 'FONT_ICON') {
    const { iconClass, library = 'remix' } = props;

    let finalIconClass = iconClass;

    // Add library prefixes if not already present
    if (library === 'remix' && !iconClass.startsWith('ri-')) {
      finalIconClass = `ri-${iconClass}`;
    } else if (library === 'bootstrap' && !iconClass.startsWith('bi-')) {
      finalIconClass = `bi bi-${iconClass}`;
    } else if (library === 'tabler' && !iconClass.startsWith('ti-')) {
      finalIconClass = `ti ti-${iconClass}`;
    }

    return (
      <i
        className={`${finalIconClass} ${className}`}
        style={commonStyles}
        aria-label={ariaLabel}
        aria-hidden={ariaHidden}
        title={title}
      />
    );
  }

  return null;
};

export default Icon;
