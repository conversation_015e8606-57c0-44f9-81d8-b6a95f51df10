"use client";

import React from 'react';

export type LKRCurrencyVariant = 'black' | 'red' | 'white' | 'inherit';

export interface LKRCurrencyIconProps {
  /** Color variant of the LKR currency icon */
  variant?: LKRCurrencyVariant;
  /** Size of the icon in pixels */
  size?: number | string;
  /** Additional CSS classes */
  className?: string;
  /** Accessibility label */
  'aria-label'?: string;
  /** Whether the icon is decorative (hidden from screen readers) */
  'aria-hidden'?: boolean;
}

/**
 * LKR Currency Icon Component
 *
 * A sharp, crisp currency icon component that uses inline SVG for optimal rendering
 * and supports dynamic color inheritance from parent text elements.
 *
 * Features:
 * - Sharp/crisp rendering with inline SVG
 * - Dynamic color inheritance with 'inherit' variant
 * - Three fixed color variants: black, red, white
 * - Configurable size
 * - Accessibility support
 * - Consistent styling with existing icon system
 *
 * Usage:
 * ```tsx
 * // Dynamic color inheritance (recommended)
 * <span className="text-green-500">
 *   <LKRCurrencyIcon variant="inherit" size={20} />
 * </span>
 *
 * // Fixed color variants
 * <LKRCurrencyIcon variant="black" size={20} />
 * <LKRCurrencyIcon variant="red" size={24} />
 * ```
 */
const LKRCurrencyIcon: React.FC<LKRCurrencyIconProps> = ({
  variant = 'inherit',
  size = 20,
  className = '',
  'aria-label': ariaLabel = 'Sri Lankan Rupee',
  'aria-hidden': ariaHidden = false,
}) => {
  // Convert size to pixels if it's a number
  const sizeValue = typeof size === 'number' ? size : parseInt(size as string) || 20;

  // Get the fill color based on variant
  const getFillColor = (variant: LKRCurrencyVariant): string => {
    switch (variant) {
      case 'black':
        return '#000000';
      case 'red':
        return '#CE0041';
      case 'white':
        return '#FFFFFF';
      case 'inherit':
      default:
        return 'currentColor';
    }
  };

  const fillColor = getFillColor(variant);

  return (
    <svg
      width={sizeValue}
      height={sizeValue}
      viewBox="0 0 800 800"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`inline-block ${className}`}
      style={{
        minWidth: sizeValue,
        minHeight: sizeValue
      }}
      aria-label={ariaHidden ? undefined : ariaLabel}
      aria-hidden={ariaHidden}
      role={ariaHidden ? 'presentation' : 'img'}
    >
      <path
        d="M400 8.03174C616.474 8.03174 791.968 183.526 791.968 400C791.968 616.474 616.474 791.969 400 791.969C183.526 791.969 8.03125 616.474 8.03125 400C8.03125 183.526 183.526 8.03174 400 8.03174ZM683.627 380.394H752.207C747.681 297.754 714.705 222.682 662.915 164.817L614.414 213.328C653.91 258.654 679.281 316.642 683.627 380.394ZM752.207 419.607H683.627C679.281 483.368 653.92 541.346 614.414 586.682L662.915 635.183C714.705 577.318 747.681 502.256 752.207 419.607ZM635.183 662.916L586.681 614.414C541.346 653.92 483.367 679.272 419.606 683.618V752.208C502.256 747.682 577.318 714.714 635.183 662.916ZM380.393 752.208V683.628C316.642 679.281 258.654 653.911 213.327 614.414L164.817 662.916C222.682 714.705 297.753 747.682 380.393 752.208ZM137.084 635.193L185.595 586.682C146.089 541.346 120.728 483.368 116.382 419.607H47.7922C52.3182 502.256 85.2948 577.328 137.084 635.193ZM154.933 400C152.571 534.382 267.328 645.076 400 645.076C535.355 645.076 645.076 535.356 645.076 400C645.076 264.692 535.412 154.99 400.104 154.924C267.375 154.924 152.571 265.533 154.933 400ZM47.7922 380.394H116.382C120.719 316.642 146.089 258.654 185.595 213.318L137.094 164.817C85.2948 222.682 52.3182 297.754 47.7922 380.394ZM380.393 116.382V47.7927C297.753 52.3187 222.682 85.2953 164.817 137.094L213.318 185.595C258.654 146.09 316.642 120.719 380.393 116.382ZM419.606 47.7927V116.382C483.367 120.719 541.346 146.09 586.681 185.595L635.183 137.094C577.318 85.2953 502.256 52.3187 419.606 47.7927ZM380.393 241.769C380.393 215.983 419.606 215.983 419.606 241.769V262.944H450.844C476.63 262.944 476.63 302.166 450.844 302.166H419.606V380.394H438.334C470.696 380.394 497.097 406.794 497.097 439.147V478.303C497.097 510.609 470.611 537.056 438.334 537.056H419.606V558.231C419.606 584.027 380.393 584.027 380.393 558.231V537.056H349.165C323.369 537.056 323.369 497.843 349.165 497.843H380.393V419.607H361.666C329.369 419.607 302.912 393.178 302.912 360.853V321.707C302.912 289.344 329.313 262.944 361.666 262.944H380.393V241.769ZM419.606 419.607V497.843H438.334C449.096 497.843 457.874 489.047 457.874 478.303V439.147C457.874 428.451 449.039 419.607 438.334 419.607H419.606ZM380.393 380.394V302.166H361.666C350.96 302.166 342.125 311.001 342.125 321.707V360.853C342.125 371.606 350.932 380.394 361.666 380.394H380.393Z"
        fill={fillColor}
      />
    </svg>
  );
};

export default LKRCurrencyIcon;
