// shared/UI/components/TabNavigation.tsx - Reusable tab navigation component
"use client";

import React from "react";

export interface TabNavigationItem<T extends string = string> {
	/** Unique identifier for the tab */
	id: T;
	/** The text label for the tab */
	label: string;
	/** Optional icon class name */
	icon?: string;
	/** Whether this tab is disabled */
	disabled?: boolean;
}

export interface TabNavigationProps<T extends string = string> {
	/** Array of tab items */
	tabs: TabNavigationItem<T>[];
	/** Currently active tab ID */
	activeTab: T;
	/** Callback when tab changes */
	onTabChange: (tab: T) => void;
	/** Additional CSS classes for the container */
	className?: string;
	/** Tab button radius */
	radius?: string;
	/** Tab style variant */
	variant?: 'default' | 'golden' | 'underline' | 'pills';
}

/**
 * Reusable tab navigation component extracted from UserDetailsPageClient
 * 
 * Features:
 * - Generic TypeScript support for tab IDs
 * - Multiple style variants
 * - Icon support
 * - Disabled state support
 * - Responsive design
 * - Dark theme compatible
 * - Accessibility features (ARIA labels, keyboard navigation)
 * 
 * @param tabs - Array of tab items with id, label, and optional icon
 * @param activeTab - Currently active tab ID
 * @param onTabChange - Callback function when tab changes
 * @param className - Additional CSS classes for customization
 * @param variant - Style variant for different use cases
 */
export const TabNavigation = <T extends string = string>({
	tabs,
	activeTab,
	onTabChange,
	className = "",
	radius = "",
	variant = 'golden'
}: TabNavigationProps<T>) => {
	const getVariantClasses = () => {
		switch (variant) {
			case 'golden':
				return {
					container: "h-[67px] w-full max-w-[1768px] bg-elevated2 rounded-[16px] p-[8px] flex gap-2",
					button: (isActive: boolean) => `
						flex-1 h-full rounded-[${radius || "xl"}] px-3 py-4 font-rubik font-normal text-base text-center transition-background
						${isActive
							? 'bg-gradient-to-b from-[rgba(225,182,73,0)] to-[rgba(225,182,73,0.42)] border border-[#FFFFFF1A] text-white'
							: 'text-gray-400 hover:text-white hover:bg-gray-700/20'
						}
					`,
					buttonStyle: {
						textTransform: 'capitalize' as const,
						fontSize: '16px',
						lineHeight: '100%',
						fontWeight: 400,
					}
				};
			case 'underline':
				return {
					container: "border-b border-gray-200 dark:border-white/10",
					button: (isActive: boolean) => `
						py-4 px-1 inline-flex items-center gap-2 border-b-2 text-sm whitespace-nowrap transition-background
						${isActive
							? 'border-primary text-primary font-semibold'
							: 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
						}
					`,
					buttonStyle: {
					}
				};
			case 'pills':
				return {
					container: "flex space-x-2",
					button: (isActive: boolean) => `
						px-4 py-2 rounded-full text-sm font-medium transition-background
						${isActive
							? 'bg-primary text-white'
							: 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700'
						}
					`,
					buttonStyle: {
					}
				};
			default:
				return {
					container: "flex space-x-1 bg-gray-100 rounded-lg p-1 dark:bg-gray-800",
					button: (isActive: boolean) => `
						px-3 py-2 rounded-md text-sm font-medium transition-background
						${isActive
							? 'bg-white text-gray-900 shadow-sm dark:bg-gray-700 dark:text-white'
							: 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
						}
					`,
					buttonStyle: {
					}
				};
		}
	};

	const variantClasses = getVariantClasses();

	const handleKeyDown = (event: React.KeyboardEvent, tab: TabNavigationItem<T>) => {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			if (!tab.disabled) {
				onTabChange(tab.id);
			}
		}
	};

	return (
		<div className={`${variantClasses.container} ${className}`} role="tablist">
			{tabs.map((tab) => {
				const isActive = activeTab === tab.id;
				return (
					<button
						key={tab.id}
						type="button"
						role="tab"
						aria-selected={isActive}
						aria-controls={`panel-${tab.id}`}
						id={`tab-${tab.id}`}
						disabled={tab.disabled}
						className={variantClasses.button(isActive)}
						style={variantClasses.buttonStyle}
						onClick={() => !tab.disabled && onTabChange(tab.id)}
						onKeyDown={(e) => handleKeyDown(e, tab)}
					>
						{tab.icon && <i className={`${tab.icon} mr-2`} aria-hidden="true" />}
						<span>{tab.label}</span>
					</button>
				);
			})}
		</div>
	);
};

export default TabNavigation;
