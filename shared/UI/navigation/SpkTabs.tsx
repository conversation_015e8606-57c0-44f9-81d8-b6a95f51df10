"use client";

import React, { Fragment, useState, useEffect } from 'react';

export interface SpkTabItem {
  /** Unique identifier for the tab */
  id: string;
  /** The text label for the tab */
  label: string;
  /** The content to display in the tab panel */
  content: React.ReactNode;
  /** Whether this tab is disabled */
  disabled?: boolean;
  /** Optional icon element to display */
  icon?: React.ReactNode;
  /** Optional badge element to display */
  badge?: React.ReactNode;
}

export interface SpkTabsProps {
  /** Array of tab items */
  tabs: SpkTabItem[];
  /** The ID of the initially active tab */
  defaultActiveTab?: string;
  /** Controlled active tab ID */
  activeTab?: string;
  /** Callback when tab changes */
  onTabChange?: (tabId: string) => void;
  /** Tab style variant */
  variant?: 'basic' | 'underline' | 'pills' | 'card' | 'segment';
  /** Orientation of the tabs */
  orientation?: 'horizontal' | 'vertical';
  /** Whether tabs should be responsive (show select on mobile) */
  responsive?: boolean;
  /** Additional CSS classes for the container */
  className?: string;
  /** Additional CSS classes for tab buttons */
  tabClassName?: string;
  /** Additional CSS classes for tab panels */
  panelClassName?: string;
}

const SpkTabs: React.FC<SpkTabsProps> = ({
  tabs,
  defaultActiveTab,
  activeTab: controlledActiveTab,
  onTabChange,
  variant = 'basic',
  orientation = 'horizontal',
  responsive = false,
  className = '',
  tabClassName = '',
  panelClassName = ''
}) => {
  const [internalActiveTab, setInternalActiveTab] = useState<string>(
    controlledActiveTab || defaultActiveTab || tabs[0]?.id || ''
  );

  const activeTab = controlledActiveTab || internalActiveTab;

  useEffect(() => {
    if (controlledActiveTab) {
      setInternalActiveTab(controlledActiveTab);
    }
  }, [controlledActiveTab]);

  const handleTabChange = (tabId: string) => {
    if (!controlledActiveTab) {
      setInternalActiveTab(tabId);
    }
    onTabChange?.(tabId);
  };

  const getTabListClasses = () => {
    const baseClasses = orientation === 'vertical' ? 'flex flex-col' : 'flex';
    const spacingClasses = orientation === 'vertical' ? 'space-y-2 whitespace-nowrap' : 'space-x-2 rtl:space-x-reverse';

    let variantClasses = '';
    switch (variant) {
      case 'underline':
        variantClasses = orientation === 'vertical'
          ? 'border-e border-gray-200 dark:border-white/10'
          : 'border-b-0 border-gray-200 dark:border-white/10';
        break;
      case 'card':
        variantClasses = orientation === 'vertical'
          ? 'border-e border-gray-200 dark:border-white/10'
          : 'border-b-0 border-gray-200 dark:border-white/10';
        break;
      case 'segment':
        variantClasses = 'bg-gray-100 hover:bg-gray-200 rounded-sm transition p-1 dark:bg-black/20 dark:hover:bg-black/20';
        break;
    }

    return [baseClasses, spacingClasses, variantClasses].filter(Boolean).join(' ');
  };

  const getTabButtonClasses = (tab: SpkTabItem, isActive: boolean) => {
    const baseClasses = 'inline-flex items-center gap-2 text-sm font-medium text-center transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2';

    let variantClasses = '';
    let stateClasses = '';

    if (tab.disabled) {
      stateClasses = 'opacity-50 cursor-not-allowed pointer-events-none';
    } else if (isActive) {
      switch (variant) {
        case 'basic':
          variantClasses = 'py-2 px-3';
          stateClasses = 'text-primary font-semibold';
          break;
        case 'underline':
          variantClasses = orientation === 'vertical'
            ? '-me-px py-2 px-3 border-e-transparent border text-primary'
            : '-mb-px py-2 px-3 border-b-transparent border text-primary';
          stateClasses = 'bg-white dark:bg-transparent';
          break;
        case 'pills':
          variantClasses = 'py-2 px-3 rounded-md';
          stateClasses = 'bg-primary text-white';
          break;
        case 'card':
          variantClasses = orientation === 'vertical'
            ? '-me-px py-2 px-3 border rounded-s-sm'
            : '-mb-px py-2 px-3 border rounded-t-sm';
          stateClasses = 'bg-white border-b-transparent text-primary dark:bg-transparent dark:border-b-white/10';
          break;
        case 'segment':
          variantClasses = 'py-2 px-3 rounded-sm';
          stateClasses = 'bg-white text-gray-700 dark:bg-light dark:text-defaulttextcolor/70';
          break;
      }
    } else {
      switch (variant) {
        case 'basic':
          variantClasses = 'py-2 px-3';
          stateClasses = 'text-defaulttextcolor hover:text-primary dark:text-white/50';
          break;
        case 'underline':
          variantClasses = orientation === 'vertical'
            ? '-me-px py-2 px-3 border rounded-s-sm'
            : '-mb-px py-2 px-3 border rounded-t-sm';
          stateClasses = 'bg-gray-50 text-defaulttextcolor hover:text-primary dark:bg-black/20 dark:border-white/10 dark:text-white/50';
          break;
        case 'pills':
          variantClasses = 'py-2 px-3 rounded-md';
          stateClasses = 'text-defaulttextcolor hover:bg-gray-100 dark:text-white/50 dark:hover:bg-black/20';
          break;
        case 'card':
          variantClasses = orientation === 'vertical'
            ? '-me-px py-2 px-3 border rounded-s-sm'
            : '-mb-px py-2 px-3 border rounded-t-sm';
          stateClasses = 'bg-gray-50 text-defaulttextcolor hover:text-primary dark:bg-black/20 dark:border-white/10 dark:text-white/50';
          break;
        case 'segment':
          variantClasses = 'py-2 px-3 rounded-sm';
          stateClasses = 'bg-transparent text-defaulttextcolor hover:text-gray-700 dark:text-white/50 dark:hover:text-white';
          break;
      }
    }

    return [baseClasses, variantClasses, stateClasses, tabClassName].filter(Boolean).join(' ');
  };

  const getTabPanelClasses = () => {
    let variantClasses = '';
    switch (variant) {
      case 'underline':
        variantClasses = 'mt-3';
        break;
      case 'card':
        variantClasses = orientation === 'vertical'
          ? 'border border-s-0 rounded-sm rounded-ss-none dark:border-white/10 border-gray-200'
          : 'border rounded-sm rounded-ss-none dark:border-white/10 border-gray-200';
        break;
      case 'segment':
        variantClasses = 'mt-3';
        break;
      default:
        variantClasses = '';
    }

    return [variantClasses, panelClassName].filter(Boolean).join(' ');
  };

  const _activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <Fragment>
      <div className={className}>
        {/* Responsive select for mobile */}
        {responsive && (
          <select
            className="mb-5 sm:hidden py-2 px-3 pe-9 block w-full border-gray-200 rounded-sm text-sm focus:border-primary focus:ring-primary dark:bg-bgdark dark:border-white/10 dark:text-white/50"
            value={activeTab}
            onChange={(e) => handleTabChange(e.target.value)}
            aria-label="Tabs"
          >
            {tabs.map((tab) => (
              <option key={tab.id} value={tab.id} disabled={tab.disabled}>
                {tab.label}
              </option>
            ))}
          </select>
        )}

        <div className={orientation === 'vertical' ? 'flex' : ''}>
          {/* Tab List */}
          <div className={responsive ? 'hidden sm:block' : ''}>
            <nav className={getTabListClasses()} role="tablist">
              {tabs.map((tab) => {
                const isActive = tab.id === activeTab;
                return (
                  <button
                    key={tab.id}
                    type="button"
                    className={getTabButtonClasses(tab, isActive)}
                    role="tab"
                    aria-selected={isActive}
                    aria-controls={`panel-${tab.id}`}
                    id={`tab-${tab.id}`}
                    disabled={tab.disabled}
                    onClick={() => !tab.disabled && handleTabChange(tab.id)}
                  >
                    {tab.icon && <span className="flex-shrink-0">{tab.icon}</span>}
                    <span>{tab.label}</span>
                    {tab.badge && <span className="flex-shrink-0">{tab.badge}</span>}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Panels */}
          <div className={getTabPanelClasses()}>
            {tabs.map((tab) => (
              <div
                key={tab.id}
                id={`panel-${tab.id}`}
                role="tabpanel"
                aria-labelledby={`tab-${tab.id}`}
                className={tab.id === activeTab ? '' : 'hidden'}
              >
                {tab.content}
              </div>
            ))}
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default SpkTabs;
