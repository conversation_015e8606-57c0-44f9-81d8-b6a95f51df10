import React, { Fragment } from 'react';
import Link from 'next/link';

export interface SpkNavItem {
  /** Unique identifier for the nav item */
  id: string;
  /** The text label for the nav item */
  label: string;
  /** The href/link for the nav item */
  href: string;
  /** Whether this nav item is currently active */
  active?: boolean;
  /** Whether this nav item is disabled */
  disabled?: boolean;
  /** Optional icon element to display */
  icon?: React.ReactNode;
  /** Optional badge element to display */
  badge?: React.ReactNode;
  /** Click handler for the nav item */
  onClick?: (id: string) => void;
}

export interface SpkNavProps {
  /** Array of navigation items */
  items: SpkNavItem[];
  /** Navigation style variant */
  variant?: 'basic' | 'underline' | 'pills' | 'segment';
  /** Orientation of the navigation */
  orientation?: 'horizontal' | 'vertical';
  /** Whether to render as a list */
  asList?: boolean;
  /** Additional CSS classes for the container */
  className?: string;
  /** Additional CSS classes for nav items */
  itemClassName?: string;
  /** Spacing between nav items */
  spacing?: 'sm' | 'md' | 'lg';
}

const SpkNav: React.FC<SpkNavProps> = ({
  items,
  variant = 'basic',
  orientation = 'horizontal',
  asList = false,
  className = '',
  itemClassName = '',
  spacing = 'md'
}) => {
  const getSpacingClass = () => {
    if (orientation === 'vertical') {
      switch (spacing) {
        case 'sm':
          return 'space-y-1';
        case 'lg':
          return 'space-y-4';
        default:
          return 'space-y-2';
      }
    } else {
      switch (spacing) {
        case 'sm':
          return 'space-x-2 rtl:space-x-reverse';
        case 'lg':
          return 'space-x-8 rtl:space-x-reverse';
        default:
          return 'space-x-6 rtl:space-x-reverse';
      }
    }
  };

  const getContainerClasses = () => {
    const baseClasses = orientation === 'vertical' ? 'flex flex-col' : 'sm:flex';
    const spacingClasses = getSpacingClass();

    let variantClasses = '';
    if (variant === 'underline') {
      variantClasses = orientation === 'vertical'
        ? 'border-e-2 border-border-secondary dark:border-white/10'
        : 'border-b-2 border-border-secondary dark:border-white/10';
    } else if (variant === 'segment') {
      variantClasses = 'bg-section hover:bg-elevated rounded-sm transition p-1 dark:bg-black/20 dark:hover:bg-black/20';
    }

    return [baseClasses, spacingClasses, variantClasses, className].filter(Boolean).join(' ');
  };

  const getItemClasses = (item: SpkNavItem) => {
    const baseClasses = 'px-1 inline-flex items-center gap-2 text-sm whitespace-nowrap transition-colors';

    let stateClasses = '';
    if (item.disabled) {
      stateClasses = 'text-text-disabled pointer-events-none dark:text-white/50';
    } else if (item.active) {
      switch (variant) {
        case 'underline':
          stateClasses = 'font-semibold border-primary text-primary py-4 px-1 border-b-[3px]';
          break;
        case 'pills':
          stateClasses = 'bg-primary text-white rounded-md px-3 py-2';
          break;
        case 'segment':
          stateClasses = 'bg-white text-gray-700 dark:bg-light dark:text-defaulttextcolor/70 py-2 px-3 rounded-sm font-medium';
          break;
        default:
          stateClasses = 'font-medium text-primary';
      }
    } else {
      switch (variant) {
        case 'underline':
          stateClasses = 'border-b-[3px] border-transparent text-defaulttextcolor dark:text-[#8c9097] dark:text-white/50 hover:text-primary py-4 px-1';
          break;
        case 'pills':
          stateClasses = 'text-defaulttextcolor hover:bg-gray-100 dark:text-white/50 dark:hover:bg-black/20 rounded-md px-3 py-2';
          break;
        case 'segment':
          stateClasses = 'bg-transparent text-defaulttextcolor hover:text-gray-700 font-medium py-2 px-3 rounded-sm hover:text-primary dark:text-[#8c9097] dark:text-white/50 dark:hover:text-white';
          break;
        default:
          stateClasses = 'text-primary hover:text-primary';
      }
    }

    return [baseClasses, stateClasses, itemClassName].filter(Boolean).join(' ');
  };

  const handleItemClick = (item: SpkNavItem) => {
    if (!item.disabled && item.onClick) {
      item.onClick(item.id);
    }
  };

  const renderNavItem = (item: SpkNavItem) => (
    <Link
      key={item.id}
      href={item.href}
      className={getItemClasses(item)}
      onClick={() => handleItemClick(item)}
      aria-current={item.active ? 'page' : undefined}
      scroll={false}
    >
      {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
      <span>{item.label}</span>
      {item.badge && <span className="flex-shrink-0">{item.badge}</span>}
    </Link>
  );

  if (asList) {
    return (
      <Fragment>
        <ul className={getContainerClasses()}>
          {items.map((item) => (
            <li key={item.id} className={orientation === 'horizontal' ? 'inline-block' : ''}>
              {renderNavItem(item)}
            </li>
          ))}
        </ul>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <nav className={getContainerClasses()} role="navigation">
        {items.map(renderNavItem)}
      </nav>
    </Fragment>
  );
};

export default SpkNav;
