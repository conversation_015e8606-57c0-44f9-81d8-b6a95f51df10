// shared/UI/forms/validation/layout.tsx - Server-side layout for showcase with SEO
import React from "react";
import type { Metadata } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Form Validation - Form Component Showcase",
	"Comprehensive showcase of form validation components and patterns for the Xintra platform.",
	"/shared/UI/forms/validation",
	{
		keywords: ["form validation","input validation","form errors","validation patterns","form components"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for showcase page
 * Provides SEO optimization and structured data
 */
export default function Layout({ children }: LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
    {
        "name": "Home",
        "url": "/"
    },
    {
        "name": "UI Components",
        "url": "/ui"
    },
    {
        "name": "Forms",
        "url": "/ui/forms"
    },
    {
        "name": "Form Validation"
    }
]}
			/>
			<SoftwareApplicationStructuredData
				name="Form Validation Showcase"
				description="Comprehensive showcase of form validation components and patterns for the Xintra platform."
				url="/shared/UI/forms/validation"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
