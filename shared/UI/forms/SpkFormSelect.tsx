import React, { Fragment } from 'react';

export interface SpkFormSelectOption {
  value: string | number | null;
  label: string;
  disabled?: boolean;
}

export interface SpkFormSelectProps {
  id?: string;
  name?: string;
  value?: string | number;
  disabled?: boolean;
  required?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  options: SpkFormSelectOption[];
  placeholder?: string;
  className?: string;
  label?: string;
  helpText?: string;
  size?: 'sm' | 'md' | 'lg';
  error?: string;
  success?: boolean;
  multiple?: boolean;
  'aria-label'?: string;
}

const SpkFormSelect: React.FC<SpkFormSelectProps> = ({
  id,
  name,
  value,
  disabled = false,
  required = false,
  onChange,
  onBlur,
  onFocus,
  options,
  placeholder,
  className = '',
  label,
  helpText,
  size = 'md',
  error,
  success,
  multiple = false,
  'aria-label': ariaLabel
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'form-select-sm';
      case 'lg':
        return 'form-select-lg';
      default:
        return '';
    }
  };

  const getStatusClass = () => {
    if (error) {
      return 'border-red-500 focus:border-red-500 focus:ring-red-500';
    }
    if (success) {
      return 'border-green-500 focus:border-green-500 focus:ring-green-500';
    }
    return 'border-secondary focus:border-accent focus:ring-accent/50';
  };

  const selectClasses = [
    'form-select',
    getSizeClass(),
    getStatusClass(),
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium font-rubik text-secondary mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      <select
        id={id}
        name={name}
        value={value}
        disabled={disabled}
        required={required}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        className={`${selectClasses} bg-form-input text-white font-rubik placeholder-tertiary`}
        multiple={multiple}
        aria-label={ariaLabel || label}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option, index) => (
          <option
            key={index}
            value={option.value ?? ''}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>

      {error && (
        <div className="text-sm text-red-500 font-rubik mt-1">
          {error}
        </div>
      )}

      {helpText && !error && (
        <div className="text-sm text-tertiary font-rubik mt-1">
          {helpText}
        </div>
      )}
    </Fragment>
  );
};

export default SpkFormSelect;
