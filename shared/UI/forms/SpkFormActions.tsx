import React from "react";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";
import PrimaryButton from "../buttons/PrimaryButton";

export interface SpkFormActionsProps {
	isLoading?: boolean;
	onCancel?: () => void;
	submitText?: string;
	loadingText?: string;
	submitIcon?: string;
	cancelText?: string;
	cancelIcon?: string;
	_submitVariant?: string;  // Kept for backward compatibility but not used with PrimaryButton
	cancelVariant?: string;
	disabled?: boolean;
	showCancel?: boolean;
	className?: string;
	layout?: "horizontal" | "vertical";
	alignment?: "left" | "center" | "right";
}

const SpkFormActions: React.FC<SpkFormActionsProps> = ({
	isLoading = false,
	onCancel,
	submitText = "Submit",
	loadingText,
	submitIcon = "ri-check-line",
	cancelText = "Cancel",
	cancelIcon = "ri-close-line",
	_submitVariant = "ti-btn-primary",  // Kept for backward compatibility but not used with PrimaryButton
	cancelVariant = "ti-btn-secondary",
	disabled = false,
	showCancel = true,
	className = "",
	layout = "horizontal",
	alignment = "right"
}) => {
	const getLayoutClasses = () => {
		const baseClasses = "flex gap-3";
		const layoutClass = layout === "vertical" ? "flex-col" : "flex-col sm:flex-row";
		
		let alignmentClass = "";
		switch (alignment) {
			case "left":
				alignmentClass = "justify-start";
				break;
			case "center":
				alignmentClass = "justify-center";
				break;
			case "right":
			default:
				alignmentClass = "justify-end";
				break;
		}

		return `${baseClasses} ${layoutClass} ${alignmentClass}`;
	};

	const getLoadingText = () => {
		if (loadingText) return loadingText;
		if (submitText.toLowerCase().includes("create")) return "Creating...";
		if (submitText.toLowerCase().includes("update")) return "Updating...";
		if (submitText.toLowerCase().includes("save")) return "Saving...";
		return "Processing...";
	};

	return (
		<div className={`box ${className}`}>
			<div className="box-body">
				<div className={getLayoutClasses()}>
					{showCancel && onCancel && (
						<SpkButton
							type="button"
							variant="secondary"
							customClass={`ti-btn ${cancelVariant}`}
							onclickfunc={onCancel}
							disabled={isLoading || disabled}
						>
							<i className={`${cancelIcon} me-2`}></i>
							{cancelText}
						</SpkButton>
					)}
					
					<PrimaryButton
						type="submit"
						disabled={isLoading || disabled}
						loading={isLoading}
						loadingText={getLoadingText()}
					>
						<i className={`${submitIcon} me-2`}></i>
						{submitText}
					</PrimaryButton>
				</div>
			</div>
		</div>
	);
};

export default SpkFormActions;
