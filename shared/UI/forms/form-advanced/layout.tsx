// shared/UI/forms/form-advanced/layout.tsx - Server-side layout for advanced forms showcase with SEO
import React from "react";
import type { Metadata } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface FormAdvancedLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for advanced forms showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Advanced Forms - Form Component Showcase",
	"Comprehensive showcase of advanced form components including phone inputs, country selectors, tags, and rich text editors for the Xintra platform.",
	"/ui/forms/form-advanced",
	{
		keywords: ["advanced forms", "form components", "phone input", "country selector", "tags input", "form elements"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for advanced forms showcase page
 * Provides SEO optimization and structured data for the advanced forms page
 */
export default function FormAdvancedLayout({ children }: FormAdvancedLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: 'Home', url: '/' },
					{ name: 'UI Components', url: '/ui' },
					{ name: 'Forms', url: '/ui/forms' },
					{ name: 'Advanced Forms' }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Advanced Forms Showcase"
				description="Comprehensive advanced form component library for modern web applications"
				url="/ui/forms/form-advanced"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
