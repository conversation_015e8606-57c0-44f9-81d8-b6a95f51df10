// shared/UI/forms/sun-editor/layout.tsx - Server-side layout for sun editor showcase with SEO
import React from "react";
import type { Metadata } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface SunEditorLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for sun editor showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Sun Editor - Rich Text Editor Showcase",
	"Comprehensive showcase of Sun Editor rich text editing capabilities with various configurations and features for the Xintra platform.",
	"/ui/forms/sun-editor",
	{
		keywords: ["sun editor", "rich text editor", "WYSIWYG editor", "text editing", "content editor"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for sun editor showcase page
 * Provides SEO optimization and structured data for the sun editor page
 */
export default function SunEditorLayout({ children }: SunEditorLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: 'Home', url: '/' },
					{ name: 'UI Components', url: '/ui' },
					{ name: 'Forms', url: '/ui/forms' },
					{ name: 'Sun Editor' }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Sun Editor Showcase"
				description="Comprehensive rich text editor component library for modern web applications"
				url="/ui/forms/sun-editor"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
