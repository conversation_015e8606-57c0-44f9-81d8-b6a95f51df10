// shared/UI/forms/form-elements/form-wizard/layout.tsx - Server-side layout for showcase with SEO
import React from "react";
import type { Metadata } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Form Wizard - Form Component Showcase",
	"Comprehensive showcase of form wizard components for multi-step forms in the Xintra platform.",
	"/shared/UI/forms/form-elements/form-wizard",
	{
		keywords: ["form wizard","multi-step forms","step forms","form elements","wizard components"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for showcase page
 * Provides SEO optimization and structured data
 */
export default function Layout({ children }: LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
    {
        "name": "Home",
        "url": "/"
    },
    {
        "name": "UI Components",
        "url": "/ui"
    },
    {
        "name": "Forms",
        "url": "/ui/forms"
    },
    {
        "name": "Form Elements",
        "url": "/ui/forms/form-elements"
    },
    {
        "name": "Form Wizard"
    }
]}
			/>
			<SoftwareApplicationStructuredData
				name="Form Wizard Showcase"
				description="Comprehensive showcase of form wizard components for multi-step forms in the Xintra platform."
				url="/shared/UI/forms/form-elements/form-wizard"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
