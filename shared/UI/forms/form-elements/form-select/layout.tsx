// shared/UI/forms/form-elements/form-select/layout.tsx - Server-side layout for showcase with SEO
import React from "react";
import type { Metadata } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Form Select - Form Component Showcase",
	"Comprehensive showcase of select dropdown components with various configurations for the Xintra platform.",
	"/shared/UI/forms/form-elements/form-select",
	{
		keywords: ["form select","dropdown","select components","form elements","option selection"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for showcase page
 * Provides SEO optimization and structured data
 */
export default function Layout({ children }: LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
    {
        "name": "Home",
        "url": "/"
    },
    {
        "name": "UI Components",
        "url": "/ui"
    },
    {
        "name": "Forms",
        "url": "/ui/forms"
    },
    {
        "name": "Form Elements",
        "url": "/ui/forms/form-elements"
    },
    {
        "name": "Form Select"
    }
]}
			/>
			<SoftwareApplicationStructuredData
				name="Form Select Showcase"
				description="Comprehensive showcase of select dropdown components with various configurations for the Xintra platform."
				url="/shared/UI/forms/form-elements/form-select"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
