// shared/UI/forms/form-elements/date-time-picker/layout.tsx - Server-side layout for showcase with SEO
import React from "react";
import type { Metadata } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Date & Time Picker - Form Component Showcase",
	"Comprehensive showcase of date and time picker components for the Xintra platform.",
	"/shared/UI/forms/form-elements/date-time-picker",
	{
		keywords: ["date picker","time picker","datetime picker","calendar","form elements"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for showcase page
 * Provides SEO optimization and structured data
 */
export default function Layout({ children }: LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
    {
        "name": "Home",
        "url": "/"
    },
    {
        "name": "UI Components",
        "url": "/ui"
    },
    {
        "name": "Forms",
        "url": "/ui/forms"
    },
    {
        "name": "Form Elements",
        "url": "/ui/forms/form-elements"
    },
    {
        "name": "Date & Time Picker"
    }
]}
			/>
			<SoftwareApplicationStructuredData
				name="Date & Time Picker Showcase"
				description="Comprehensive showcase of date and time picker components for the Xintra platform."
				url="/shared/UI/forms/form-elements/date-time-picker"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
