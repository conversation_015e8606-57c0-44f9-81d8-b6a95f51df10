// shared/UI/forms/select2/layout.tsx - Server-side layout for showcase with SEO
import React from "react";
import type { Metada<PERSON> } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Select2 - Advanced Select Component Showcase",
	"Comprehensive showcase of Select2 advanced select components for the Xintra platform.",
	"/shared/UI/forms/select2",
	{
		keywords: ["select2","advanced select","searchable select","form elements","dropdown components"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for showcase page
 * Provides SEO optimization and structured data
 */
export default function Layout({ children }: LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
    {
        "name": "Home",
        "url": "/"
    },
    {
        "name": "UI Components",
        "url": "/ui"
    },
    {
        "name": "Forms",
        "url": "/ui/forms"
    },
    {
        "name": "Select2"
    }
]}
			/>
			<SoftwareApplicationStructuredData
				name="Select2 Showcase"
				description="Comprehensive showcase of Select2 advanced select components for the Xintra platform."
				url="/shared/UI/forms/select2"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
