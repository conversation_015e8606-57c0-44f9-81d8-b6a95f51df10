// shared/UI/components/forms/WalletTransactionForm.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { UserSelectionDropdown, useToast, PrimaryButton } from '@/shared/UI/components';
import { useWalletTransactionMutation } from '@/shared/query/mutations/useWalletTransactionMutation';
import { useUserDetailsQuery } from '@/shared/query/useUserDetailsQuery';
import { WalletTransactionFormData, UserSelectionOption } from '@/shared/types/user-management-types';

export interface WalletTransactionFormProps {
  transactionType: "deposit" | "withdraw";
  preSelectedUser?: {
    id: string;
    userName: string;
    walletId: string;
  };
  onSuccess?: (_transactionId: number) => void;
  onCancel?: () => void;
  showUserSelection?: boolean;
}
const WalletTransactionForm: React.FC<WalletTransactionFormProps> = ({
  transactionType,
  preSelectedUser,
  onSuccess,
  onCancel: _onCancel,
  showUserSelection = true
}) => {
  const { showSuccess, showError } = useToast();
  const walletTransactionMutation = useWalletTransactionMutation();
  const [formData, setFormData] = useState<WalletTransactionFormData>({
    userId: preSelectedUser?.id || '',
    userName: preSelectedUser?.userName || '',
    walletId: preSelectedUser?.walletId || '',
    transactionType,
    amount: '',
    internalComment: '',
    utrNumber: '',
    transactionComments: ''
  });
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Fetch user details if we have a userId but no walletId
  const shouldFetchUserDetails = formData.userId && !formData.walletId;
  const { data: userDetailsResponse } = useUserDetailsQuery(
    shouldFetchUserDetails ? formData.userId : ''
  );
  // Update transaction type when prop changes
  useEffect(() => {
    setFormData(prev => ({ ...prev, transactionType }));
  }, [transactionType]);

  // Update user info when preSelectedUser changes
  useEffect(() => {
    if (preSelectedUser) {
      setFormData(prev => ({
        ...prev,
        userId: preSelectedUser.id,
        userName: preSelectedUser.userName,
        walletId: preSelectedUser.walletId
      }));
    }
  }, [preSelectedUser]);

  // Update walletId when user details are fetched
  useEffect(() => {
    if (userDetailsResponse?.data && formData.userId && !formData.walletId) {
      const walletId = userDetailsResponse.data.walletid || '';
      if (walletId) {
        setFormData(prev => ({
          ...prev,
          walletId
        }));
      }
    }
  }, [userDetailsResponse, formData.userId, formData.walletId]);

  const handleInputChange = (field: keyof WalletTransactionFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));

    // Clear validation errors when user starts typing
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  const handleUserSelection = (userInfo: UserSelectionOption | null) => {
    if (userInfo) {
      setFormData(prev => ({
        ...prev,
        userId: userInfo.value,
        userName: userInfo.label,
        walletId: userInfo.walletId || ''
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        userId: '',
        userName: '',
        walletId: ''
      }));
    }
  };

  const validateForm = (data: WalletTransactionFormData): string[] => {
    const errors: string[] = [];

    if (!data.userId || data.userId.trim() === '') {
      errors.push('User selection is required');
    }

    if (!data.transactionType) {
      errors.push('Transaction type is required');
    }

    if (!data.amount || data.amount.trim() === '') {
      errors.push('Transaction amount is required');
    } else {
      const amount = parseFloat(data.amount);
      if (isNaN(amount) || amount <= 0) {
        errors.push('Transaction amount must be a positive number');
      }
      if (amount > 1000000) {
        errors.push('Transaction amount cannot exceed 1,000,000');
      }
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const errors = validateForm(formData);

    if (errors.length > 0) {
      setValidationErrors(errors);
      showError('Validation Error', 'Please fix the validation errors');
      return;
    }

    try {
      const response = await walletTransactionMutation.mutateAsync(formData);

      if (response.success === 1) {
        showSuccess(
          'Transaction Successful',
          `${transactionType === 'deposit' ? 'Deposit' : 'Withdrawal'} completed successfully!`
        );

        // Reset form
        setFormData({
          userId: preSelectedUser?.id || '',
          userName: preSelectedUser?.userName || '',
          walletId: preSelectedUser?.walletId || '',
          transactionType,
          amount: '',
          internalComment: '',
          utrNumber: '',
          transactionComments: ''
        });

        // Call success callback
        if (onSuccess) {
          onSuccess(response.record.transaction_id);
        }
      } else {
        showError('Transaction Failed', response.message || 'Transaction failed');
      }
    } catch (error) {
      showError(
        'Transaction Error',
        error instanceof Error ? error.message : 'An unexpected error occurred'
      );
    }
  };

  const isLoading = walletTransactionMutation.isPending;

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      {/* User Selection - Only show if showUserSelection is true */}
      {showUserSelection && (
        <div className="space-y-2">
          <label className="block font-rubik font-semibold text-sm text-white capitalize">
            Select User
          </label>
          <UserSelectionDropdown
            value={formData.userId}
            onChange={handleUserSelection}
            label=""
            helpText=""
            required
            maxUsers={100}
          />
        </div>
      )}

      {/* Wallet ID Status Indicator */}
      {showUserSelection && formData.userId && (
        <div className={`p-3 rounded-md border ${formData.walletId
          ? 'bg-green-500/10 border-green-500/30'
          : 'bg-yellow-500/10 border-yellow-500/30'
          }`}>
          <div className="flex items-center gap-2 text-sm">
            <i className={`${formData.walletId
              ? 'ri-check-line text-green-400'
              : 'ri-alert-line text-yellow-400'
              }`}></i>
            <span className={`font-rubik ${formData.walletId
              ? 'text-green-300'
              : 'text-yellow-300'
              }`}>
              {formData.walletId
                ? `Wallet ID: ${formData.walletId}`
                : 'Wallet ID not available for selected user'
              }
            </span>
          </div>
        </div>
      )}

      {/* Pre-selected User Display - Only show if user is pre-selected */}
      {!showUserSelection && preSelectedUser && (
        <div className="p-3 bg-elevated rounded-[4px] border border-border-secondary">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-[4px] bg-elevated flex items-center justify-center">
              <i className="ri-user-line text-blue-400"></i>
            </div>
            <div>
              <div className="font-medium font-rubik text-blue-300">
                {preSelectedUser.userName}
              </div>
              <div className="text-sm font-rubik text-blue-400">
                User ID: {preSelectedUser.id}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Transaction Amount */}
      <div className="space-y-2">
        <label className="block font-rubik font-semibold text-sm text-white capitalize">
          Amount
        </label>
        <input
          type="number"
          value={formData.amount}
          onChange={handleInputChange('amount')}
          placeholder="Enter amount"
          required
          min={0.01}
          step={0.01}
          className="w-full px-3 py-2 bg-elevated border border-border-secondary rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        />
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="p-3 bg-red-500/10 border border-red-500/30 rounded-md">
          <div className="flex items-start">
            <i className="ri-error-warning-line text-red-400 mt-0.5 mr-2"></i>
            <div>
              <h4 className="text-sm font-medium font-rubik text-red-300">
                Please fix the following errors:
              </h4>
              <ul className="mt-1 text-sm font-rubik text-red-400 list-disc list-inside">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Form Actions - Single Process Button */}
      <div className="pt-4">
        <PrimaryButton
          type="submit"
          disabled={isLoading}
          loading={isLoading}
          loadingText="Processing..."
          fullWidth
        >
          Process
        </PrimaryButton>
      </div>
    </form>
  );
};

export default WalletTransactionForm;
