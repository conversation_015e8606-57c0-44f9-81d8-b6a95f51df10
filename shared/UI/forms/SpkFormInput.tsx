import React, { Fragment } from 'react';

export interface SpkFormInputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'date' | 'datetime-local' | 'time' | 'week' | 'month';
  id?: string;
  name?: string;
  value?: string | number;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  label?: string;
  helpText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'dotted' | 'dashed';
  radius?: 'none' | 'default' | 'full';
  error?: string;
  success?: boolean;
  autoComplete?: string;
  min?: number;
  max?: number;
  step?: number;
  pattern?: string;
  maxLength?: number;
  minLength?: number;
}

const SpkFormInput: React.FC<SpkFormInputProps> = ({
  type = 'text',
  id,
  name,
  value,
  placeholder,
  className = '',
  disabled = false,
  readOnly = false,
  required = false,
  onChange,
  onBlur,
  onFocus,
  label,
  helpText,
  size = 'md',
  variant = 'default',
  radius = 'default',
  error,
  success,
  autoComplete,
  min,
  max,
  step,
  pattern,
  maxLength,
  minLength
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'form-control-sm';
      case 'lg':
        return 'form-control-lg';
      default:
        return '';
    }
  };

  const getVariantClass = () => {
    switch (variant) {
      case 'dotted':
        return 'border border-defaultborder dark:border-defaultborder/10 border-dotted';
      case 'dashed':
        return 'border border-defaultborder dark:border-defaultborder/10 border-dashed';
      default:
        return '';
    }
  };

  const getRadiusClass = () => {
    switch (radius) {
      case 'none':
        return '!rounded-none';
      case 'full':
        return '!rounded-full';
      default:
        return '';
    }
  };

  const getStatusClass = () => {
    if (error) {
      return 'border-red-500 focus:border-red-500 focus:ring-red-500';
    }
    if (success) {
      return 'border-green-500 focus:border-green-500 focus:ring-green-500';
    }
    return 'border-secondary focus:border-accent focus:ring-accent/50';
  };

  const inputClasses = [
    'form-control',
    getSizeClass(),
    getVariantClass(),
    getRadiusClass(),
    getStatusClass(),
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium font-rubik text-secondary mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      <input
        type={type}
        id={id}
        name={name}
        value={value}
        placeholder={placeholder}
        className={`${inputClasses} bg-form-input text-white font-rubik placeholder-tertiary`}
        disabled={disabled}
        readOnly={readOnly}
        required={required}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        autoComplete={autoComplete}
        min={min}
        max={max}
        step={step}
        pattern={pattern}
        maxLength={maxLength}
        minLength={minLength}
      />

      {error && (
        <div className="text-sm text-red-500 font-rubik mt-1">
          {error}
        </div>
      )}

      {helpText && !error && (
        <div className="text-sm text-tertiary font-rubik mt-1">
          {helpText}
        </div>
      )}
    </Fragment>
  );
};

export default SpkFormInput;
