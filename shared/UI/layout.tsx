// shared/UI/layout.tsx - Server-side layout for UI showcase pages with SEO
import React from "react";
import type { Metada<PERSON> } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface UILayoutProps {
	children: React.ReactNode;
}

// Generate metadata for UI showcase pages (server-side)
export const metadata: Metadata = createPageMetadata(
	"UI Components - Design System Showcase",
	"Comprehensive showcase of UI components, design patterns, and interactive elements for the Xintra platform. Explore buttons, forms, tables, and more.",
	"/ui",
	{
		keywords: ["UI components", "design system", "component library", "interface elements", "web components"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for UI showcase pages
 * Provides SEO optimization and structured data for UI component pages
 */
export default function UILayout({ children }: UILayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: 'Home', url: '/' },
					{ name: 'UI Components' }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="UI Component Library"
				description="Comprehensive design system and component library for modern web applications"
				url="/ui"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
