// shared/UI/user-management/UserProfileHeader.tsx - Reusable user profile header component
"use client";

import { UserDetailsData } from "@/shared/types/user-management-types";
import { buildImageUrl, isExternalImage } from "@/shared/utils/imageOptimization";
import { CurrencyDisplay } from "@/shared/UI/components";
import Image from "next/image";
import React from "react";

export interface UserProfileHeaderProps {
	userData: UserDetailsData;
	userTypeLabel: string;
	showWalletBalance?: boolean;
	className?: string;
}

/**
 * Reusable user profile header component extracted from UserDetailsPageClient
 * 
 * Features:
 * - User avatar with fallback handling
 * - User information display (username, type, phone, join date)
 * - Optional wallet balance display
 * - Responsive design (mobile/desktop layouts)
 * - Dark theme compatible
 * - Consistent styling with design system
 * 
 * @param userData - User data object containing profile information
 * @param userTypeLabel - Formatted user type label (e.g., "Player", "Agent")
 * @param showWalletBalance - Whether to display wallet balance section
 * @param className - Additional CSS classes for customization
 */
export const UserProfileHeader: React.FC<UserProfileHeaderProps> = ({
	userData,
	userTypeLabel,
	showWalletBalance = true,
	className = ""
}) => {
	return (
		<div className={`h-auto lg:h-[144px] bg-profile-gradient rounded-[1rem] px-3 py-4 flex flex-col lg:flex-row justify-between gap-4 ${className}`}>
			{/* Header with gradient background */}
			<div
				className="flex items-center gap-4"
			>
				{/* Profile Image */}
				<div className="w-20 h-20 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden">
					{(() => {
						const avatarUrl = buildImageUrl(userData.avatarImage);
						const isDefaultFallback = avatarUrl === '/images/profile.png';

						return !isDefaultFallback ? (
							<div className="relative w-full h-full">
								<Image
									src={avatarUrl}
									alt="User Avatar"
									fill
									className="object-cover"
									unoptimized={isExternalImage(avatarUrl)}
									onError={(e) => {
										const target = e.target as HTMLImageElement;
										target.style.display = 'none';
										(e.currentTarget.parentNode?.nextSibling as HTMLElement)?.classList.remove('hidden');
									}}
								/>
							</div>
						) : null;
					})()}
					<i className={`ri-user-line text-2xl text-gray-600 ${buildImageUrl(userData.avatarImage) === '/images/profile.png' ? '' : 'hidden'}`}></i>
				</div>

				{/* User Details */}
				<div className="flex flex-col gap-2">
					{/* Username */}
					<h2 className="text-xl font-semibold text-white font-rubik">
						{userData.userName || 'Unknown User'}
					</h2>

					{/* User Metadata */}
					<div className="flex flex-col lg:flex-row lg:items-center gap-2 lg:gap-4">
						{/* User Type */}
						<div className="flex items-center gap-1">
							<i className="ri-user-line text-[#E354D4]"></i>
							<span className="text-sm text-[#E354D4] font-rubik">{userTypeLabel}</span>
						</div>

						{/* Phone Number */}
						<div className="flex items-center gap-1">
							<i className="ri-phone-line text-[#FF5D9F]"></i>
							<span className="text-sm text-[#FF5D9F] font-rubik">
								{userData.phone ? `${userData.phoneCode || ''}${userData.phone}` : 'N/A'}
							</span>
						</div>

						{/* Join Date */}
						<div className="flex items-center gap-1">
							<i className="ri-calendar-line text-[#FF8E6F]"></i>
							<span className="text-sm text-[#FF8E6F] font-rubik">
								{userData.createdAt ? new Date(userData.createdAt).toLocaleDateString() : 'N/A'}
							</span>
						</div>
					</div>
				</div>
			</div>

			{showWalletBalance && (
				<div className="flex flex-col bg-golden-button justify-center items-center p-[1px] h-full w-[250px] border-none rounded-lg overflow-hidden relative min-h-[80px]">
					<div className="flex flex-col  justify-center items-center  text-center m-[1px] h-full w-full rounded-[12px] bg-secondary-btn">
						<div className="text-2xl font-semibold text-white font-rubik">
							<CurrencyDisplay
								amount={userData.amount || 0}
								context="header"
								size={20}
								amountClassName="text-2xl font-semibold font-rubik"
								gap="sm"
							/>
						</div>
						<div className="text-sm text-gray-300 font-rubik">
							Wallet Balance
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default UserProfileHeader;
