// shared/UI/user-management/FinancialSummaryCard.tsx - Reusable financial summary card component
"use client";

import React from "react";
import { SVGLoader, type SVGIconName } from "@/shared/UI/components/icons";
import { CurrencyDisplay } from "@/shared/UI/components";

export interface FinancialSummaryCardProps {
	/** The SVG icon name from the financialSummary directory */
	svgName: SVGIconName;
	/** The title/label for the card */
	title: string;
	/** The main value to display */
	value: string | number;
	/** Background color for the icon container */
	iconColor?: 'blue' | 'green' | 'purple' | 'red' | 'orange' | 'yellow';
	/** Whether to show currency (uses centralized system) */
	showCurrency?: boolean;
	/** Whether to format the value with commas */
	formatValue?: boolean;
	/** Additional CSS classes for customization */
	className?: string;
}

/**
 * Reusable financial summary card component extracted from UserDetailsPageClient
 * Updated to match UserStatisticsCard styling specifications
 *
 * Features:
 * - Consistent 188px height and styling matching UserStatisticsCard
 * - Same padding as UserStatisticsCard (px-[24px] py-[28px])
 * - Center-aligned layout with 28px gap between icon and text
 * - 12px gap between title and value (gap-3)
 * - bg-card-financial-texture class with texture-background-v1.png and exclusion blend mode
 * - 80x80px icon container (matching UserStatisticsCard)
 * - Typography matching UserStatisticsCard (#AEAEAE title, text-base, text-2xl value)
 * - Configurable icon and color themes
 * - Automatic value formatting with commas
 * - Currency support
 * - Dark theme compatible
 * - Responsive design
 * - Follows established design system patterns
 *
 * @param svgName - SVG icon name from the financialSummary directory
 * @param title - Title/label text
 * @param value - Main value to display
 * @param iconColor - Color theme for icon background
 * @param currency - Currency symbol (default: '$')
 * @param formatValue - Whether to format numbers with commas (default: true)
 * @param className - Additional CSS classes
 */
export const FinancialSummaryCard: React.FC<FinancialSummaryCardProps> = ({
	svgName,
	title,
	value,
	showCurrency = false,
	formatValue = true,
	className = ""
}) => {

	// Format the value for display
	const formatDisplayValue = (val: string | number): string => {
		if (typeof val === 'number') {
			if (formatValue) {
				return val.toLocaleString();
			}
			return val.toString();
		}
		return val;
	};

	// Determine if value should have currency prefix
	const shouldShowCurrencyForValue = (val: string | number): boolean => {
		if (typeof val === 'number') {
			return true;
		}
		// Check if string represents a number
		const numericValue = parseFloat(val.replace(/[^0-9.-]/g, ''));
		return !isNaN(numericValue);
	};

	const displayValue = formatDisplayValue(value);
	const shouldDisplayCurrency = showCurrency && shouldShowCurrencyForValue(value);

	return (
		<div className={`w-full lg:w-[427px] h-[188px] bg-card-financial-texture rounded-[12px] px-[24px] py-[28px] flex items-center justify-start gap-[28px] ${className}`}>
			{/* Icon Container */}
			<div className={`w-[100px] h-[100px]  rounded-lg flex items-center justify-center`}>
				<SVGLoader
					name={svgName}
					size={80}
					className="w-[100px] h-[100px]"
					fallback={<div className="w-[100px] h-[100px] bg-gray-500 rounded-lg flex items-center justify-center text-white text-xs">Icon</div>}
				/>
			</div>

			{/* Content Container - Updated spacing to match UserStatisticsCard */}
			<div className="flex flex-col gap-3">
				{/* Title */}
				<span className="text-[#AEAEAE] font-rubik font-light text-base">
					{title}
				</span>

				{/* Value */}
				<div className="text-white font-rubik font-semibold text-2xl">
					{shouldDisplayCurrency && typeof value === 'number' ? (
						<CurrencyDisplay
							amount={value}
							context="card"
							size={20}
							amountClassName="font-rubik font-semibold text-2xl"
							gap="sm"
						/>
					) : (
						displayValue
					)}
				</div>
			</div>
		</div>
	);
};

export default FinancialSummaryCard;
