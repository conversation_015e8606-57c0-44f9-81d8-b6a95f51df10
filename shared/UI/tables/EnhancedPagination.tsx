import React from 'react';

export interface EnhancedPaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  totalPages?: number;
  showItemsPerPageSelector?: boolean;
  itemsPerPageOptions?: number[];
  className?: string;
}

/**
 * Enhanced Pagination Component
 *
 * Layout:
 * - Main container: width: 100%; height: 34px; display: flex; justify-content: space-between;
 * - Left side: Page navigation controls (<< Previous 1 2 3 4 Next >>)
 * - Right side: Total count display, with optional items-per-page selector
 * - Background: transparent
 *
 * Features:
 * - Fixed conditional rendering bug (no longer returns null when showItemsPerPageSelector = false)
 * - Page navigation with Previous/Next buttons and page numbers
 * - Items per page selector (optional)
 * - Total count display with pill-shaped design
 * - Custom styling following design specifications
 */
const EnhancedPagination: React.FC<EnhancedPaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
  totalPages,
  showItemsPerPageSelector = true,
  itemsPerPageOptions = [10, 25, 50, 100],
  className = ''
}) => {
  // Calculate total pages if not provided - with safety checks to prevent infinity
  const calculatedTotalPages = totalPages || (itemsPerPage > 0 ? Math.ceil(totalItems / itemsPerPage) : 1);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;

    if (calculatedTotalPages <= maxPagesToShow) {
      for (let i = 1; i <= calculatedTotalPages; i++) {
        pages.push(i);
      }
    } else {
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(calculatedTotalPages, currentPage + 2);

      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      if (endPage < calculatedTotalPages) {
        if (endPage < calculatedTotalPages - 1) {
          pages.push('...');
        }
        pages.push(calculatedTotalPages);
      }
    }

    return pages;
  };

  const handlePageClick = (page: number | string) => {
    if (typeof page === 'number' && page !== currentPage) {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < calculatedTotalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    if (onItemsPerPageChange) {
      onItemsPerPageChange(newItemsPerPage);
    }
  };

  return (
    <div
      className={`w-full h-[34px] flex justify-between items-center ${className}`}
      style={{ background: 'transparent' }}
    >
      {/* Left side - Page navigation controls */}
      <div className="flex items-center">
        {calculatedTotalPages > 1 && (
          <nav className="flex items-center">
            {/* Previous button */}
            <button
              onClick={handlePrevious}
              disabled={currentPage === 1}
              className="disabled:opacity-50 disabled:pointer-events-none transition-colors"
              style={{
                fontFamily: 'Rubik',
                fontWeight: 400,
                fontSize: '16px',
                lineHeight: '100%',
                color: '#616161',
                textTransform: 'capitalize',
                background: 'none',
                border: 'none',
                cursor: currentPage === 1 ? 'default' : 'pointer',
                marginRight: '8px'
              }}
            >
              &lt;&lt; Previous
            </button>

            {/* Page numbers */}
            <div className="flex items-center" style={{ gap: '4px' }}>
              {getPageNumbers().map((page, index) => (
                <button
                  key={index}
                  onClick={() => handlePageClick(page)}
                  disabled={page === '...'}
                  className={`flex justify-center items-center transition-colors ${page === '...' ? 'cursor-default' : 'cursor-pointer'
                    }`}
                  style={{
                    width: '26px',
                    height: '26px',
                    borderRadius: '4px',
                    padding: '4px',
                    fontFamily: 'Rubik',
                    fontWeight: 500,
                    fontSize: '16px',
                    lineHeight: '100%',
                    textAlign: 'center',
                    textTransform: 'capitalize',
                    color: page === '...' ? '#616161' : '#FF8E6F',
                    backgroundColor: (page === '...' || page !== currentPage) ? 'transparent' : '#523B37',
                    border: 'none'
                  }}
                >
                  {page}
                </button>
              ))}
            </div>

            {/* Next button */}
            <button
              onClick={handleNext}
              disabled={currentPage === calculatedTotalPages}
              className="disabled:opacity-50 disabled:pointer-events-none transition-colors"
              style={{
                fontFamily: 'Rubik',
                fontWeight: 400,
                fontSize: '16px',
                lineHeight: '100%',
                color: '#616161',
                textTransform: 'capitalize',
                background: 'none',
                border: 'none',
                cursor: currentPage === calculatedTotalPages ? 'default' : 'pointer',
                marginLeft: '8px'
              }}
            >
              Next &gt;&gt;
            </button>
          </nav>
        )}
      </div>

      {/* Right side - Items per page selector and total count */}
      <div className="flex items-center" style={{ gap: '10px' }}>
        {/* Items per page selector */}
        {showItemsPerPageSelector && onItemsPerPageChange && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-text-secondary">Show:</span>
            <select
              value={itemsPerPage}
              onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
              className="px-3 py-1 text-sm bg-surface border border-border-primary rounded-md text-text-muted focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent"
            >
              {itemsPerPageOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
            <span className="text-sm text-text-secondary">per page</span>
          </div>
        )}

        {/* Total count display */}
        <div
          className="flex items-center"
          style={{
            borderRadius: '16px',
            backgroundColor: '#494C72',
            padding: '4px 8px',
            fontFamily: 'Rubik',
            fontWeight: 500,
            fontSize: '12px',
            lineHeight: '100%',
            color: '#FFFFFF',
            verticalAlign: 'middle'
          }}
        >
          {totalItems} total
        </div>
      </div>
    </div>
  );
};

export default EnhancedPagination;
