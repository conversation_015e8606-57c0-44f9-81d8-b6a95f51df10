import React, { Fragment } from 'react';
import Link from 'next/link';
import SpkButton from '@/shared/@spk-reusable-components/uielements/spk-button';

export interface SpkPaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  totalPages?: number;
  style?: 'default' | 'style-1' | 'style-2' | 'style-3' | 'style-4' | 'style-5';
  size?: 'sm' | 'md' | 'lg';
  showInfo?: boolean;
  showGoToPage?: boolean;
  className?: string;
}

const SpkPagination: React.FC<SpkPaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange,
  totalPages,
  style = 'default',
  size = 'md',
  showInfo = true,
  showGoToPage = false,
  className = ''
}) => {
  // Calculate total pages if not provided
  const calculatedTotalPages = totalPages || Math.ceil(totalItems / itemsPerPage);

  // Calculate the range of items being displayed
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;

    if (calculatedTotalPages <= maxPagesToShow) {
      for (let i = 1; i <= calculatedTotalPages; i++) {
        pages.push(i);
      }
    } else {
      let startPage = Math.max(1, currentPage - 2);
      let endPage = Math.min(calculatedTotalPages, currentPage + 2);

      if (currentPage <= 3) {
        endPage = Math.min(maxPagesToShow, calculatedTotalPages);
      } else if (currentPage >= calculatedTotalPages - 2) {
        startPage = Math.max(1, calculatedTotalPages - maxPagesToShow + 1);
      }

      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      if (endPage < calculatedTotalPages) {
        if (endPage < calculatedTotalPages - 1) {
          pages.push('...');
        }
        pages.push(calculatedTotalPages);
      }
    }

    return pages;
  };

  const handlePageClick = (page: number | string) => {
    if (typeof page === 'number' && page !== currentPage) {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < calculatedTotalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'pagination-sm';
      case 'lg':
        return 'pagination-lg';
      default:
        return '';
    }
  };

  const getStyleClass = () => {
    switch (style) {
      case 'style-1':
        return 'pagination-style-1';
      case 'style-2':
        return 'pagination-style-2';
      case 'style-3':
        return 'pagination-style-3';
      case 'style-4':
        return 'pagination-style-4';
      case 'style-5':
        return 'pagination-style-5';
      default:
        return '';
    }
  };

  if (calculatedTotalPages <= 1) {
    return null;
  }

  // Style 5 uses SpkButton components
  if (style === 'style-5') {
    return (
      <Fragment>
        <div className={`flex items-center justify-between ${className}`}>
          {showInfo && (
            <div className="text-sm text-gray-700 dark:text-gray-300">
              Showing <span className="font-medium">{startItem}</span> to{' '}
              <span className="font-medium">{endItem}</span> of{' '}
              <span className="font-medium">{totalItems}</span> results
            </div>
          )}

          <nav className="flex items-center gap-x-1">
            <SpkButton
              type="button"
              customClass="min-h-[38px] min-w-[38px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-md text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-white/10 dark:focus:bg-white/10"
              disabled={currentPage === 1}
              onclickfunc={handlePrevious}
            >
              <i className="ri-arrow-left-s-line align-middle rtl:rotate-180"></i>
            </SpkButton>

            <div className="flex items-center gap-x-1">
              {getPageNumbers().map((page, index) => (
                <SpkButton
                  key={index}
                  type="button"
                  customClass={`min-h-[38px] min-w-[38px] flex justify-center items-center py-2 px-3 text-sm rounded-md focus:outline-none disabled:opacity-50 disabled:pointer-events-none ${page === currentPage
                      ? 'bg-primary text-white dark:bg-primary dark:text-white'
                      : page === '...'
                        ? 'text-gray-400 cursor-default'
                        : 'text-gray-800 hover:bg-gray-100 dark:text-white dark:hover:bg-white/10'
                    }`}
                  disabled={page === '...'}
                  onclickfunc={() => handlePageClick(page)}
                >
                  {page}
                </SpkButton>
              ))}
            </div>

            <SpkButton
              type="button"
              customClass="min-h-[38px] min-w-[38px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-md text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-white/10 dark:focus:bg-white/10"
              disabled={currentPage === calculatedTotalPages}
              onclickfunc={handleNext}
            >
              <i className="ri-arrow-right-s-line align-middle rtl:rotate-180"></i>
            </SpkButton>
          </nav>

          {showGoToPage && (
            <div className="flex justify-center sm:justify-start items-center gap-x-2">
              <span className="text-sm text-gray-800 whitespace-nowrap dark:text-white">
                Go to
              </span>
              <input
                type="number"
                className="min-h-[38px] py-2 px-2.5 block w-12 border-gray-200 rounded-md text-sm text-center focus:border-primary focus:ring-primary disabled:opacity-50 disabled:pointer-events-none dark:bg-bodybg dark:border-white/10 dark:text-gray-400 dark:focus:ring-gray-600"
                min="1"
                max={calculatedTotalPages}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    const page = parseInt((e.target as HTMLInputElement).value);
                    if (page >= 1 && page <= calculatedTotalPages) {
                      onPageChange(page);
                    }
                  }
                }}
              />
              <span className="text-sm text-gray-800 whitespace-nowrap dark:text-white">
                page
              </span>
            </div>
          )}
        </div>
      </Fragment>
    );
  }

  // Default pagination style
  return (
    <Fragment>
      <div className={`flex items-center justify-between ${className}`}>
        {showInfo && (
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{startItem}</span> to{' '}
            <span className="font-medium">{endItem}</span> of{' '}
            <span className="font-medium">{totalItems}</span> results
          </div>
        )}

        <nav aria-label="Page navigation" className={getStyleClass()}>
          <ul className={`ti-pagination mb-0 ${getSizeClass()}`}>
            <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
              <Link
                href="#!"
                scroll={false}
                className="page-link px-3 py-[0.375rem]"
                onClick={(e) => {
                  e.preventDefault();
                  handlePrevious();
                }}
              >
                Previous
              </Link>
            </li>

            {getPageNumbers().map((page, index) => (
              <li
                key={index}
                className={`page-item ${page === currentPage ? 'active' : ''} ${page === '...' ? 'disabled' : ''}`}
              >
                {page === '...' ? (
                  <span className="page-link px-3 py-[0.375rem]">...</span>
                ) : (
                  <Link
                    href="#!"
                    scroll={false}
                    className="page-link px-3 py-[0.375rem]"
                    onClick={(e) => {
                      e.preventDefault();
                      handlePageClick(page);
                    }}
                  >
                    {page}
                  </Link>
                )}
              </li>
            ))}

            <li className={`page-item ${currentPage === calculatedTotalPages ? 'disabled' : ''}`}>
              <Link
                href="#!"
                scroll={false}
                className="page-link px-3 py-[0.375rem]"
                onClick={(e) => {
                  e.preventDefault();
                  handleNext();
                }}
              >
                Next
              </Link>
            </li>
          </ul>
        </nav>
      </div>
    </Fragment>
  );
};

export default SpkPagination;
