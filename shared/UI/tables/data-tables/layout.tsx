// shared/UI/tables/data-tables/layout.tsx - Server-side layout for showcase with SEO
import React from "react";
import type { Metadata } from "next";
import { createPageMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

interface LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for showcase page (server-side)
export const metadata: Metadata = createPageMetadata(
	"Data Tables - Table Component Showcase",
	"Comprehensive showcase of data table components with sorting, filtering, and pagination for the Xintra platform.",
	"/shared/UI/tables/data-tables",
	{
		keywords: ["data tables","table components","sortable tables","filterable tables","pagination"],
		noindex: true // UI showcase pages typically shouldn't be indexed
	}
);

/**
 * Server-side layout component for showcase page
 * Provides SEO optimization and structured data
 */
export default function Layout({ children }: LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
    {
        "name": "Home",
        "url": "/"
    },
    {
        "name": "UI Components",
        "url": "/ui"
    },
    {
        "name": "Tables",
        "url": "/ui/tables"
    },
    {
        "name": "Data Tables"
    }
]}
			/>
			<SoftwareApplicationStructuredData
				name="Data Tables Showcase"
				description="Comprehensive showcase of data table components with sorting, filtering, and pagination for the Xintra platform."
				url="/shared/UI/tables/data-tables"
				applicationCategory="DeveloperApplication"
				operatingSystem="Web Browser"
			/>
			
			{children}
		</>
	);
}
