import React, { Fragment } from 'react';

/**
 * SpkTable Column Definition
 *
 * Defines the structure and behavior of a table column
 */
export interface SpkTableColumn {
  key: string;                                                  // Unique identifier for the column, used for data mapping
  title: string;                                                // Display title for the column header
  className?: string;                                           // Optional CSS class for the column
  sortable?: boolean;                                           // Whether the column is sortable
  render?: (value: any, record: any, index: number) => React.ReactNode; // Custom render function for cell content
  width?: string | number;                                      // Width of the column (CSS value or number of pixels)
  align?: 'left' | 'center' | 'right';                          // Text alignment within the column
  hidden?: boolean;                                             // Whether the column should be hidden
  fixed?: 'left' | 'right';                                     // Whether the column should be fixed to left or right (for horizontal scrolling)
  ellipsis?: boolean;                                           // Whether to show ellipsis for overflowed content
}

/**
 * SpkTable Props Interface
 *
 * The standardized table component for the application
 * Follows our design system with dark theme compatibility and consistent styling
 */
export interface SpkTableProps {
  // Data props
  columns: SpkTableColumn[];                                    // Column definitions
  data: any[];                                                  // Table data
  loading?: boolean;                                            // Whether the table is in loading state

  // Styling props
  className?: string;                                           // CSS class for the table container
  tableClass?: string;                                          // CSS class for the table element
  headerClass?: string;                                         // CSS class for the table header
  bodyClass?: string;                                           // CSS class for the table body
  rowClass?: string | ((record: any, index: number) => string); // CSS class for table rows (string or function)
  bordered?: boolean;                                           // Whether to show borders
  striped?: boolean;                                            // Whether to use striped rows
  hover?: boolean;                                              // Whether to show hover effect on rows
  size?: 'sm' | 'md' | 'lg';                                    // Size variant of the table
  responsive?: boolean;                                         // Whether the table should be responsive
  scrollableBody?: boolean;                                     // Whether to make only the table body scrollable
  maxBodyHeight?: string;                                       // Maximum height for scrollable body

  // Content props
  emptyText?: string;                                           // Text to show when there's no data
  loadingText?: string;                                         // Text to show when loading

  // Interaction props
  onRowClick?: (record: any, index: number) => void;            // Callback when a row is clicked

  // Sorting props
  sortBy?: string;                                              // Current sort column key
  sortOrder?: 'asc' | 'desc';                                   // Current sort order
  onSort?: (column: string, order: 'asc' | 'desc') => void;     // Callback when sort changes
}

/**
 * SpkTable Component
 *
 * The standardized table component for the application.
 * Features:
 * - TypeScript interfaces for type safety
 * - Sorting functionality with visual indicators
 * - Custom cell rendering
 * - Loading and empty states
 * - Responsive design
 * - Dark theme compatibility
 * - Consistent styling with 8px border-radius on thead elements
 * - Hover effects and row click handling
 */
const SpkTable: React.FC<SpkTableProps> = ({
  columns,
  data,
  loading = false,
  className = '',
  tableClass = '',
  headerClass = '',
  bodyClass = '',
  rowClass = '',
  bordered = false,
  striped = false,
  hover = false,
  size = 'md',
  responsive = true,
  scrollableBody = false,
  maxBodyHeight = '400px',
  emptyText = 'No data available',
  loadingText = 'Loading...',
  onRowClick,
  sortBy,
  sortOrder,
  onSort
}) => {
  // Ensure data is always an array to prevent map errors
  const safeData = Array.isArray(data) ? data : [];

  // Filter out hidden columns
  const visibleColumns = columns.filter(column => !column.hidden);

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'table-sm';
      case 'lg':
        return 'table-lg';
      default:
        return '';
    }
  };

  const getTableClasses = () => {
    return [
      'table',
      'whitespace-nowrap',
      'min-w-full',
      getSizeClass(),
      bordered ? 'table-bordered' : '',
      striped ? 'table-striped' : '',
      hover ? 'table-hover' : '',
      tableClass
    ].filter(Boolean).join(' ');
  };

  const getRowClass = (record: any, index: number) => {
    const baseClass = 'border-b border-tertiary';
    const clickableClass = onRowClick ? 'cursor-pointer hover:bg-elevated/50' : '';
    const customClass = typeof rowClass === 'function' ? rowClass(record, index) : rowClass;

    return [baseClass, clickableClass, customClass].filter(Boolean).join(' ');
  };

  const handleSort = (column: SpkTableColumn) => {
    if (!column.sortable || !onSort) return;

    const newOrder = sortBy === column.key && sortOrder === 'asc' ? 'desc' : 'asc';
    onSort(column.key, newOrder);
  };

  // const renderSortIcon = (column: SpkTableColumn) => {
  //   if (!column.sortable) return null;

  //   if (sortBy === column.key) {
  //     return (
  //       <i className={`ri-arrow-${sortOrder === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
  //     );
  //   }

  //   return <i className="ri-expand-up-down-line ml-1 opacity-50"></i>;
  // };

  const renderCell = (column: SpkTableColumn, record: any, index: number) => {
    const value = record[column.key];

    if (column.render) {
      return column.render(value, record, index);
    }

    return value;
  };

  const renderEmptyState = () => (
    <tr>
      <td colSpan={visibleColumns.length} className="text-center py-8">
        <div className="text-secondary text-6xl mb-4">
          <i className="ri-inbox-line"></i>
        </div>
        <h3 className="text-lg font-semibold font-rubik text-secondary mb-2">No Data</h3>
        <p className="text-tertiary font-rubik">{emptyText}</p>
      </td>
    </tr>
  );

  const renderLoadingState = () => (
    <tr>
      <td colSpan={visibleColumns.length} className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p className="mt-2 text-tertiary font-rubik">{loadingText}</p>
      </td>
    </tr>
  );

  const tableContent = scrollableBody ? (
    // Scrollable body version - separate header and body tables
    <div className="w-full">
      {/* Fixed Header Table */}
      <table className={getTableClasses()}>
        <thead className={`rounded-lg overflow-hidden ${headerClass}`}>
          <tr className="rounded-lg overflow-hidden">
            {visibleColumns.map((column, index) => {
              // Determine if this is the first or last column for special border radius
              const isFirstColumn = index === 0;
              const isLastColumn = index === visibleColumns.length - 1;

              return (
                <th
                  key={index}
                  scope="col"
                  className={`text-start font-rubik font-semibold text-base text-white leading-none ${column.className || ''} ${column.sortable ? 'cursor-pointer select-none' : ''} ${isFirstColumn ? 'rounded-tl-lg' : ''} ${isLastColumn ? 'rounded-tr-lg' : ''
                    }`}
                  style={{ width: column.width }}
                  onClick={() => handleSort(column)}
                >
                  <div className={`flex items-center ${column.align === 'center' ? 'justify-center' : column.align === 'right' ? 'justify-end' : 'justify-start'}`}>
                    {column.title}
                    {/* {renderSortIcon(column)} */}
                  </div>
                </th>
              );
            })}
          </tr>
        </thead>
      </table>

      {/* Scrollable Body Container */}
      <div
        className="overflow-y-auto overflow-x-hidden"
        style={{ maxHeight: maxBodyHeight }}
      >
        <table className={getTableClasses()}>
          <tbody className={`text-muted font-rubik font-normal text-base ${bodyClass}`}>
            {loading ? (
              renderLoadingState()
            ) : safeData.length === 0 ? (
              renderEmptyState()
            ) : (
              safeData.map((record, index) => (
                <tr
                  key={index}
                  className={getRowClass(record, index)}
                  onClick={() => onRowClick?.(record, index)}
                >
                  {visibleColumns.map((column, colIndex) => (
                    <td
                      key={colIndex}
                      className={`${column.className || ''} ${column.ellipsis ? 'truncate' : ''}`}
                      style={{ width: column.width }}
                    >
                      <div className={`${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'} ${column.ellipsis ? 'truncate' : ''}`}>
                        {renderCell(column, record, index)}
                      </div>
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  ) : (
    // Standard version - single table
    <table className={getTableClasses()}>
      <thead className={`rounded-lg overflow-hidden ${headerClass}`}>
        <tr className="rounded-lg overflow-hidden">
          {visibleColumns.map((column, index) => {
            // Determine if this is the first or last column for special border radius
            const isFirstColumn = index === 0;
            const isLastColumn = index === visibleColumns.length - 1;

            return (
              <th
                key={index}
                scope="col"
                className={`text-start font-rubik font-semibold text-base text-white leading-none ${column.className || ''} ${column.sortable ? 'cursor-pointer select-none' : ''} ${isFirstColumn ? 'rounded-tl-lg' : ''} ${isLastColumn ? 'rounded-tr-lg' : ''
                  }`}
                style={{ width: column.width }}
                onClick={() => handleSort(column)}
              >
                <div className={`flex items-center ${column.align === 'center' ? 'justify-center' : column.align === 'right' ? 'justify-end' : 'justify-start'}`}>
                  {column.title}
                  {/* {renderSortIcon(column)} */}
                </div>
              </th>
            );
          })}
        </tr>
      </thead>
      <tbody className={`text-muted font-rubik font-normal text-base ${bodyClass}`}>
        {loading ? (
          renderLoadingState()
        ) : safeData.length === 0 ? (
          renderEmptyState()
        ) : (
          safeData.map((record, index) => (
            <tr
              key={index}
              className={getRowClass(record, index)}
              onClick={() => onRowClick?.(record, index)}
            >
              {visibleColumns.map((column, colIndex) => (
                <td
                  key={colIndex}
                  className={`${column.className || ''} ${column.ellipsis ? 'truncate' : ''}`}
                  style={{ width: column.width }}
                >
                  <div className={`${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'} ${column.ellipsis ? 'truncate' : ''}`}>
                    {renderCell(column, record, index)}
                  </div>
                </td>
              ))}
            </tr>
          ))
        )}
      </tbody>
    </table>
  );

  if (responsive) {
    return (
      <Fragment>
        <div className={`table-responsive ${className}`}>
          {tableContent}
        </div>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <div className={className}>
        {tableContent}
      </div>
    </Fragment>
  );
};

export default SpkTable;
