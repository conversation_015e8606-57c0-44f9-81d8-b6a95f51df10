import React from 'react';

// Status variant types
export type StatusVariant =
  | 'success' | 'win' | 'active' | 'completed' | 'won'
  | 'failed' | 'loss' | 'inactive' | 'lost' | 'cancelled' | 'void'
  | 'pending' | 'processing' | 'refunded';

export interface StatusBadgeProps {
  /** The status value to display */
  status: string;
  /** Optional custom variant override */
  variant?: StatusVariant;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show the status dot */
  showDot?: boolean;
  /** Custom dot color override */
  dotColor?: string;
  /** Custom background color override */
  backgroundColor?: string;
  /** Custom text color override */
  textColor?: string;
}

/**
 * StatusBadge Component
 * 
 * A reusable status badge component with dot indicator following the dark theme design system.
 * Supports various status types with predefined styling and custom overrides.
 */
const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  variant,
  className = '',
  showDot = true,
  dotColor,
  backgroundColor,
  textColor
}) => {
  // Determine the variant based on status if not explicitly provided
  const getVariant = (): StatusVariant => {
    if (variant) return variant;

    const normalizedStatus = status?.toLowerCase();

    // Success/Win/Active states
    if (['success', 'win', 'active', 'completed', 'won'].includes(normalizedStatus)) {
      return 'success';
    }

    // Failed/Loss/Inactive states
    if (['failed', 'loss', 'inactive', 'lost', 'cancelled', 'void'].includes(normalizedStatus)) {
      return 'failed';
    }

    // Default to pending for unknown statuses
    return 'pending';
  };

  // Get styling based on variant
  const getStatusStyling = (statusVariant: StatusVariant) => {
    switch (statusVariant) {
      case 'success':
      case 'win':
      case 'active':
      case 'completed':
      case 'won':
        return {
          backgroundColor: backgroundColor || 'var(--status-success-bg, rgba(65, 136, 118, 0.2))',
          textColor: textColor || 'var(--status-success-text, #21CE9E)',
          dotColor: dotColor || 'var(--status-success-text, #21CE9E)'
        };

      case 'failed':
      case 'loss':
      case 'inactive':
      case 'lost':
      case 'cancelled':
      case 'void':
        return {
          backgroundColor: backgroundColor || 'var(--status-failed-bg, #5B2424)',
          textColor: textColor || 'var(--status-failed-text, #FB3D32)',
          dotColor: dotColor || 'var(--status-failed-text, #FB3D32)'
        };

      case 'pending':
      case 'processing':
      case 'refunded':
      default:
        return {
          backgroundColor: backgroundColor || 'var(--action-type-bg, rgba(255, 165, 0, 0.2))',
          textColor: textColor || 'var(--action-type-text, #FFA500)',
          dotColor: dotColor || 'var(--action-type-text, #FFA500)'
        };
    }
  };

  const currentVariant = getVariant();
  const styling = getStatusStyling(currentVariant);

  const containerClasses = [
    'inline-flex',
    'items-center',
    'gap-1',
    'px-3',
    'py-0.5',
    'rounded-[4px]',
    'font-rubik',
    'font-normal',
    'text-sm',
    'leading-none',
    className
  ].filter(Boolean).join(' ');

  const dotClasses = [
    'w-1',
    'h-1',
    'rounded-full',
    'flex-shrink-0'
  ].join(' ');

  return (
    <div
      className={containerClasses}
      style={{
        backgroundColor: styling.backgroundColor,
        color: styling.textColor
      }}
    >
      {showDot && (
        <div
          className={dotClasses}
          style={{ backgroundColor: styling.dotColor }}
        />
      )}
      <span className="capitalize truncate">
        {status}
      </span>
    </div>
  );
};

export default StatusBadge;
