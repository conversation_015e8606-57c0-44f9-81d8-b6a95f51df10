import React, { Fragment } from 'react';

export interface SpkBadgeProps {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' | string | undefined;
  children: React.ReactNode;
  className?: string;
  id?: string;
  onClick?: () => void;
  pill?: boolean;
  size?: 'sm' | 'md' | 'lg';
  style?: 'default' | 'outline' | 'gradient';
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  dot?: boolean;
}

const SpkBadge: React.FC<SpkBadgeProps> = ({
  variant = 'primary',
  children,
  className = '',
  id,
  onClick,
  pill = false,
  size = 'md',
  style = 'default',
  position,
  dot = false
}) => {
  const getSizeClass = () => {
    if (dot) {
      switch (size) {
        case 'sm':
          return 'w-2 h-2';
        case 'lg':
          return 'w-4 h-4';
        default:
          return 'w-3 h-3';
      }
    }

    switch (size) {
      case 'sm':
        return 'text-xs px-2 py-1';
      case 'lg':
        return 'text-base px-3 py-2';
      default:
        return 'text-sm px-2.5 py-1.5';
    }
  };

  const getStyleClass = () => {
    switch (style) {
      case 'outline':
        return `border border-${variant} text-${variant} bg-transparent`;
      case 'gradient':
        return `bg-gradient-to-r from-${variant} to-${variant}/80 text-white`;
      default:
        return `bg-${variant} text-white`;
    }
  };

  const getPositionClass = () => {
    if (!position) return '';

    const basePosition = 'absolute transform';
    switch (position) {
      case 'top-right':
        return `${basePosition} -top-1 -right-1 translate-x-1/2 -translate-y-1/2`;
      case 'top-left':
        return `${basePosition} -top-1 -left-1 -translate-x-1/2 -translate-y-1/2`;
      case 'bottom-right':
        return `${basePosition} -bottom-1 -right-1 translate-x-1/2 translate-y-1/2`;
      case 'bottom-left':
        return `${basePosition} -bottom-1 -left-1 -translate-x-1/2 translate-y-1/2`;
      default:
        return '';
    }
  };

  const badgeClasses = [
    'badge',
    getStyleClass(),
    pill || dot ? 'rounded-full' : 'rounded',
    getSizeClass(),
    getPositionClass(),
    dot ? 'inline-block' : 'inline-flex items-center justify-center',
    onClick ? 'cursor-pointer hover:opacity-80' : '',
    className
  ].filter(Boolean).join(' ');

  if (dot) {
    return (
      <Fragment>
        <span
          className={badgeClasses}
          id={id}
          onClick={onClick}
          role={onClick ? 'button' : undefined}
          tabIndex={onClick ? 0 : undefined}
        />
      </Fragment>
    );
  }

  return (
    <Fragment>
      <span
        className={badgeClasses}
        id={id}
        onClick={onClick}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick ? 0 : undefined}
      >
        {children}
      </span>
    </Fragment>
  );
};

export default SpkBadge;
