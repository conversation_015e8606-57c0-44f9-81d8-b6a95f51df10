import React from 'react';

export interface ActionTypeBadgeProps {
  /** The action type value to display */
  actionType: string;
  /** Additional CSS classes */
  className?: string;
  /** Custom background color override */
  backgroundColor?: string;
  /** Custom text color override */
  textColor?: string;
}

/**
 * Map technical transaction type names to user-friendly display labels
 */
const getDisplayLabel = (actionType: string): string => {
  const labelMap: { [key: string]: string } = {
    // New financial report action types
    'DEBIT': 'debit',
    'CREDIT': 'credit',
    'ROLLBACK': 'rollback',
    'DEPOSIT': 'deposit',
    'WITHDRAW': 'withdraw',
    'WITHDRAW_CANCEL': 'withdraw cancel',

    // Legacy mappings for backward compatibility
    'bet': 'bet',
    'win': 'win',
    'deposit': 'deposit',
    'withdrawal': 'withdraw',
    'transfer': 'transfer',

    // Fallback patterns for type_* format
    'type_21': 'withdraw', // place bet cash debit
    'type_22': 'deposit',  // place bet cash credit (win)
    'type_1': 'deposit',
    'type_2': 'withdraw',
    'type_3': 'transfer',
  };

  return labelMap[actionType] || actionType?.replace('type_', '') || actionType;
};

/**
 * Get color scheme based on action type
 */
const getActionTypeColors = (actionType: string): { backgroundColor: string; textColor: string } => {
  const displayLabel = getDisplayLabel(actionType);

  // Green variant for deposit and credit actions (similar to success status)
  if (displayLabel === 'deposit' || displayLabel === 'credit') {
    return {
      backgroundColor: 'var(--status-success-bg, rgba(65, 136, 118, 0.2))',
      textColor: 'var(--status-success-text, #21CE9E)'
    };
  }

  // Red variant for withdraw, debit, and withdraw cancel actions
  if (displayLabel === 'withdraw' || displayLabel === 'debit' || displayLabel === 'withdraw cancel') {
    return {
      backgroundColor: 'var(--status-error-bg, rgba(239, 68, 68, 0.2))',
      textColor: 'var(--status-error-text, #EF4444)'
    };
  }

  // Yellow variant for rollback actions
  if (displayLabel === 'rollback') {
    return {
      backgroundColor: 'var(--status-warning-bg, rgba(245, 158, 11, 0.2))',
      textColor: 'var(--status-warning-text, #F59E0B)'
    };
  }

  // Orange variant for other actions (default)
  return {
    backgroundColor: 'var(--action-type-bg, rgba(255, 165, 0, 0.2))',
    textColor: 'var(--action-type-text, #FFA500)'
  };
};

/**
 * ActionTypeBadge Component
 *
 * A reusable action type badge component following the dark theme design system.
 * Uses color-coded styling: green for deposits, orange for withdrawals and other actions.
 * Automatically converts technical transaction type names to user-friendly labels.
 */
const ActionTypeBadge: React.FC<ActionTypeBadgeProps> = ({
  actionType,
  className = '',
  backgroundColor,
  textColor
}) => {
  const containerClasses = [
    'inline-flex',
    'items-center',
    'px-3',
    'py-0.5',
    'rounded-[4px]',
    'font-rubik',
    'font-normal',
    'text-sm',
    'leading-none',
    className
  ].filter(Boolean).join(' ');

  // Get user-friendly display label
  const displayLabel = getDisplayLabel(actionType);

  // Get color scheme based on action type (unless overridden by props)
  const colors = getActionTypeColors(actionType);

  return (
    <div
      className={containerClasses}
      style={{
        backgroundColor: backgroundColor || colors.backgroundColor,
        color: textColor || colors.textColor
      }}
    >
      <span className="capitalize truncate">
        {displayLabel}
      </span>
    </div>
  );
};

export default ActionTypeBadge;
