import React, { Fragment } from 'react';
import Image from 'next/image';
import SpkOverlay from '@/shared/@spk-reusable-components/uielements/spk-overlay';

export interface SpkContentPopoverProps {
  children: React.ReactNode;
  title?: string;
  image?: string;
  imageAlt?: string;
  description?: string;
  details?: Array<{
    label: string;
    value: string;
  }>;
  placement?: 'top' | 'right' | 'bottom' | 'left' | 'auto';
  trigger?: 'click' | 'hover' | 'focus';
  disabled?: boolean;
  className?: string;
  contentClassName?: string;
  maxWidth?: string;
  role?: string;
  id?: string;
}

const SpkContentPopover: React.FC<SpkContentPopoverProps> = ({
  children,
  title,
  image,
  imageAlt = 'Image Description',
  description,
  details = [],
  placement = 'top',
  trigger = 'click',
  disabled = false,
  className = '',
  contentClassName = '',
  maxWidth = '320px',
  role = 'tooltip',
  id
}) => {
  const getPlacementClass = () => {
    switch (placement) {
      case 'right':
        return '[--placement:right] rtl:[--placement:left]';
      case 'bottom':
        return '[--placement:bottom]';
      case 'left':
        return '[--placement:left]';
      case 'auto':
        return '[--placement:auto]';
      default:
        return ''; // Default is top
    }
  };

  const getTriggerClass = () => {
    return `[--trigger:${trigger}]`;
  };

  const overlayClasses = [
    getTriggerClass(),
    getPlacementClass(),
    className
  ].filter(Boolean).join(' ');

  const contentClasses = [
    'hs-tooltip-content',
    'ti-main-tooltip-content',
    'border',
    'border-defaultborder',
    'dark:border-defaultborder/10',
    'dark:bg-bodybg',
    'bg-white',
    '!p-0',
    'max-w-xs',
    'border-gray-200',
    'text-start',
    'rounded-sm',
    'hidden',
    contentClassName
  ].filter(Boolean).join(' ');

  const maxWidthStyle = maxWidth ? { maxWidth } : { maxWidth: '320px' };

  if (disabled) {
    return (
      <Fragment>
        <div className="opacity-60">
          {children}
        </div>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <SpkOverlay customClass={overlayClasses}>
        <div className="hs-tooltip-toggle ti-main-tooltip-toggle">
          {children}
          <div 
            className={contentClasses}
            style={maxWidthStyle}
            role={role}
            id={id}
          >
            {title && (
              <span className="pt-3 px-4 block text-lg font-bold text-gray-800 dark:text-white">
                {title}
              </span>
            )}
            <div className="py-2 px-3 text-sm text-gray-600 dark:text-white/70">
              {image && (
                <Image
                  className="rounded-sm mb-3"
                  src={image}
                  alt={imageAlt || 'Content image'}
                  width={300}
                  height={200}
                />
              )}
              {description && (
                <p className="mb-3">{description}</p>
              )}
              {details.length > 0 && (
                <dl className="mt-3">
                  {details.map((detail, index) => (
                    <Fragment key={index}>
                      <dt className="font-bold pt-3 first:pt-0 dark:text-white">
                        {detail.label}:
                      </dt>
                      <dd className="m-0 text-gray-600 dark:text-white/70">
                        {detail.value}
                      </dd>
                    </Fragment>
                  ))}
                </dl>
              )}
            </div>
          </div>
        </div>
      </SpkOverlay>
    </Fragment>
  );
};

export default SpkContentPopover;
