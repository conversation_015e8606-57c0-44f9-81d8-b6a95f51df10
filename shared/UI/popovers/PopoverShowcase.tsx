'use client';

import React from 'react';
import {
  Spk<PERSON><PERSON><PERSON>,
  SpkPopover<PERSON>ith<PERSON>eader,
  SpkColoredPopover,
  SpkLightPopover,
  SpkIconPopover,
  SpkContentPopover,
  SpkReviewPopover,
  SpkUserPopover
} from './index';
import SpkButton from '@/shared/@spk-reusable-components/uielements/spk-button';

const PopoverShowcase: React.FC = () => {
  const sampleUser = {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/assets/images/faces/1.jpg',
    role: 'Storyteller',
    badge: 'PRO',
    company: 'Pixeel Ltd.',
    phone: '(*************'
  };

  const sampleReviewData = {
    overallRating: 5.0,
    totalReviews: 4,
    ratings: [
      { stars: 5, percentage: 78 },
      { stars: 4, percentage: 20 },
      { stars: 3, percentage: 6 },
      { stars: 2, percentage: 2 },
      { stars: 1, percentage: 0 }
    ]
  };

  const sampleContentDetails = [
    { label: 'Assigned to', value: '<PERSON>' },
    { label: 'Due', value: 'December 21, 2021' }
  ];

  return (
    <div className="space-y-8 p-6">
      <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">
        Popover Components Showcase
      </h1>

      {/* Basic Popover */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Basic Popovers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkPopover
            title="Popover Right"
            content="And here's some amazing content. It's very engaging. Right?"
            placement="right"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-outline-primary">
              Popover Right
            </SpkButton>
          </SpkPopover>

          <SpkPopover
            title="Popover Top"
            content="And here's some amazing content. It's very engaging. Right?"
            placement="top"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-outline-primary">
              Popover Top
            </SpkButton>
          </SpkPopover>

          <SpkPopover
            title="Popover Bottom"
            content="And here's some amazing content. It's very engaging. Right?"
            placement="bottom"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-outline-primary">
              Popover Bottom
            </SpkButton>
          </SpkPopover>

          <SpkPopover
            title="Popover Left"
            content="And here's some amazing content. It's very engaging. Right?"
            placement="left"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-outline-primary">
              Popover Left
            </SpkButton>
          </SpkPopover>
        </div>
      </section>

      {/* Colored Header Popovers */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Colored Header Popovers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkPopoverWithHeader
            title="Color Header"
            content="Popover With Primary Header"
            headerColor="primary"
            placement="right"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-outline-primary">
              Header Primary
            </SpkButton>
          </SpkPopoverWithHeader>

          <SpkPopoverWithHeader
            title="Color Header"
            content="Popover With Secondary Header"
            headerColor="secondary"
            placement="top"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-outline-secondary">
              Header Secondary
            </SpkButton>
          </SpkPopoverWithHeader>

          <SpkPopoverWithHeader
            title="Color Header"
            content="Popover With Info Header"
            headerColor="info"
            placement="bottom"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-outline-info">
              Header Info
            </SpkButton>
          </SpkPopoverWithHeader>
        </div>
      </section>

      {/* Colored Background Popovers */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Colored Background Popovers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkColoredPopover
            title="Primary Color background"
            content="Popover With primary background"
            color="primary"
            placement="top"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-primary">
              Primary
            </SpkButton>
          </SpkColoredPopover>

          <SpkColoredPopover
            title="Secondary Color background"
            content="Popover With secondary background"
            color="secondary"
            placement="right"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-secondary">
              Secondary
            </SpkButton>
          </SpkColoredPopover>

          <SpkColoredPopover
            title="Success Color background"
            content="Popover With success background"
            color="success"
            placement="top"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-success">
              Success
            </SpkButton>
          </SpkColoredPopover>
        </div>
      </section>

      {/* Light Popovers */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Light Popovers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkLightPopover
            title="Color background"
            content="Popover With primary background"
            color="primary"
            placement="top"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-soft-primary">
              Primary
            </SpkButton>
          </SpkLightPopover>

          <SpkLightPopover
            title="Color background"
            content="Popover With warning background"
            color="warning"
            placement="right"
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-soft-warning">
              Warning
            </SpkButton>
          </SpkLightPopover>
        </div>
      </section>

      {/* Icon Popovers */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Icon Popovers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkIconPopover
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" className="fill-primary" height="24px" viewBox="0 0 24 24" width="24px">
                <path d="M0 0h24v24H0V0z" fill="none" />
                <path d="M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z" />
              </svg>
            }
            content={<p>The Icon Popover</p>}
            color="primary"
            placement="top"
            ariaLabel="Information popover"
          />

          <SpkIconPopover
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" className="fill-secondary" height="24px" viewBox="0 0 24 24" width="24px">
                <path d="M0 0h24v24H0V0z" fill="none" />
                <path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
              </svg>
            }
            content={<p>The Icon Popover</p>}
            color="secondary"
            placement="left"
            ariaLabel="Information popover"
          />
        </div>
      </section>

      {/* Content Popovers */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Content Popovers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkContentPopover
            title="Overview"
            image="/assets/images/media/media-28.jpg"
            imageAlt="Sample Image"
            description="This is a popover body with supporting text below as a natural lead-in to additional content."
            details={sampleContentDetails}
            placement="top"
          >
            <SpkButton customClass="w-10 h-10 ti-btn btn-wave p-0 transition-none focus:outline-none bg-gray-50 border-gray-200 text-gray-600 hover:bg-primary/30 hover:border-primary hover:text-primary dark:bg-bodybg dark:border-white/10 dark:text-white/70 dark:hover:bg-white/[.05] dark:hover:border-white/[.1] dark:hover:text-white">
              <i className="ri-arrow-up-s-line" />
            </SpkButton>
          </SpkContentPopover>
        </div>
      </section>

      {/* Review Popovers */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Review Popovers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkReviewPopover
            overallRating={sampleReviewData.overallRating}
            totalReviews={sampleReviewData.totalReviews}
            ratings={sampleReviewData.ratings}
            seeAllLink="#"
            helpLink="#"
            placement="top"
          >
            <SpkButton customClass="inline-flex items-center gap-x-2 text-sm font-semibold rounded-lg border border-transparent text-primary hover:text-primary/80 disabled:opacity-50 disabled:pointer-events-none dark:text-primary dark:hover:text-primary/80 dark:focus:outline-none dark:focus:ring-0 dark:shadow-none dark:focus:ring-primary">
              Preline review
              <i className="ri-arrow-up-s-line" />
            </SpkButton>
          </SpkReviewPopover>
        </div>
      </section>

      {/* User Popovers */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">User Popovers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkUserPopover
            user={sampleUser}
            followAction={{
              label: 'Follow',
              onClick: () => { /* console.log('Follow clicked') */ }
            }}
            flagAction={{
              href: '#'
            }}
            trigger="hover"
            placement="right"
          />
        </div>
      </section>

      {/* Disabled Popover */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Disabled Popover</h2>
        <div className="flex flex-wrap gap-4">
          <SpkPopover
            title="Disabled Popover"
            content="This popover is disabled"
            disabled={true}
          >
            <SpkButton customClass="ti-btn btn-wave ti-btn-primary opacity-60">
              Disabled button
            </SpkButton>
          </SpkPopover>
        </div>
      </section>

      {/* Different Triggers */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Different Triggers</h2>
        <div className="flex flex-wrap gap-4">
          <SpkPopover
            content="Click me"
            trigger="click"
            placement="top"
          >
            <SpkButton customClass="w-10 h-10 ti-btn btn-wave p-0 transition-none focus:outline-none bg-gray-50 border-gray-200 text-gray-600 hover:bg-primary/30 hover:border-primary hover:text-primary dark:bg-bodybg dark:border-white/10 dark:text-white/70 dark:hover:bg-white/[.05] dark:hover:border-white/[.1] dark:hover:text-white">
              <i className="ri-arrow-up-s-line" />
            </SpkButton>
          </SpkPopover>

          <SpkPopover
            content="Hover me"
            trigger="hover"
            placement="top"
          >
            <SpkButton customClass="w-10 h-10 ti-btn btn-wave p-0 transition-none focus:outline-none bg-gray-50 border-gray-200 text-gray-600 hover:bg-primary/30 hover:border-primary hover:text-primary dark:bg-bodybg dark:border-white/10 dark:text-white/70 dark:hover:bg-white/[.05] dark:hover:border-white/[.1] dark:hover:text-white">
              <i className="ri-arrow-up-s-line" />
            </SpkButton>
          </SpkPopover>

          <SpkPopover
            content="Focus me"
            trigger="focus"
            placement="top"
          >
            <SpkButton customClass="w-10 h-10 ti-btn btn-wave p-0 transition-none focus:outline-none bg-gray-50 border-gray-200 text-gray-600 hover:bg-primary/30 hover:border-primary hover:text-primary dark:bg-bodybg dark:border-white/10 dark:text-white/70 dark:hover:bg-white/[.05] dark:hover:border-white/[.1] dark:hover:text-white">
              <i className="ri-arrow-up-s-line" />
            </SpkButton>
          </SpkPopover>
        </div>
      </section>
    </div>
  );
};

export default PopoverShowcase;
