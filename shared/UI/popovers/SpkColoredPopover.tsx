import React, { Fragment } from 'react';
import SpkOverlay from '@/shared/@spk-reusable-components/uielements/spk-overlay';

export interface SpkColoredPopoverProps {
  children: React.ReactNode;
  content: React.ReactNode;
  title: string;
  color?: 'primary' | 'secondary' | 'info' | 'warning' | 'success' | 'danger';
  placement?: 'top' | 'right' | 'bottom' | 'left' | 'auto';
  trigger?: 'click' | 'hover' | 'focus';
  disabled?: boolean;
  className?: string;
  contentClassName?: string;
  maxWidth?: string;
  role?: string;
  id?: string;
}

const SpkColoredPopover: React.FC<SpkColoredPopoverProps> = ({
  children,
  content,
  title,
  color = 'primary',
  placement = 'top',
  trigger = 'click',
  disabled = false,
  className = '',
  contentClassName = '',
  maxWidth = '276px',
  role = 'tooltip',
  id
}) => {
  const getPlacementClass = () => {
    switch (placement) {
      case 'right':
        return '[--placement:right] rtl:[--placement:left]';
      case 'bottom':
        return '[--placement:bottom]';
      case 'left':
        return '[--placement:left]';
      case 'auto':
        return '[--placement:auto]';
      default:
        return ''; // Default is top
    }
  };

  const getTriggerClass = () => {
    return `[--trigger:${trigger}]`;
  };

  const overlayClasses = [
    getTriggerClass(),
    getPlacementClass(),
    className
  ].filter(Boolean).join(' ');

  const contentClasses = [
    'hs-tooltip-content',
    'ti-main-tooltip-content',
    'border',
    'border-defaultborder',
    'dark:border-defaultborder/10',
    `!bg-${color}`,
    '!z-[1000]',
    '!p-0',
    contentClassName
  ].filter(Boolean).join(' ');

  const maxWidthStyle = maxWidth ? { maxWidth } : { maxWidth: '276px' };

  if (disabled) {
    return (
      <Fragment>
        <div className="opacity-60">
          {children}
        </div>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <SpkOverlay customClass={overlayClasses}>
        <div className="hs-tooltip-toggle ti-main-tooltip-toggle">
          {children}
          <div 
            className={contentClasses}
            style={maxWidthStyle}
            role={role}
            id={id}
          >
            <div className="!border-b !border-solid dark:border-white/10 !py-2 !px-4 text-white border-white/10 !rounded-t-md text-start w-full text-[1rem]">
              <h6 className="!text-white">{title}</h6>
            </div>
            <div className="!text-white !text-[0.8rem] !py-4 !px-4 text-start">
              {content}
            </div>
          </div>
        </div>
      </SpkOverlay>
    </Fragment>
  );
};

export default SpkColoredPopover;
