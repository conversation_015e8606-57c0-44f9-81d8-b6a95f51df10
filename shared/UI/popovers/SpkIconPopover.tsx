import React, { Fragment } from 'react';
import SpkOverlay from '@/shared/@spk-reusable-components/uielements/spk-overlay';

export interface SpkIconPopoverProps {
  icon: React.ReactNode;
  content: React.ReactNode;
  color?: 'primary' | 'secondary' | 'info' | 'warning' | 'success' | 'danger';
  placement?: 'top' | 'right' | 'bottom' | 'left' | 'auto';
  trigger?: 'click' | 'hover' | 'focus';
  disabled?: boolean;
  className?: string;
  contentClassName?: string;
  iconClassName?: string;
  maxWidth?: string;
  role?: string;
  id?: string;
  ariaLabel?: string;
}

const SpkIconPopover: React.FC<SpkIconPopoverProps> = ({
  icon,
  content,
  color = 'primary',
  placement = 'top',
  trigger = 'click',
  disabled = false,
  className = '',
  contentClassName = '',
  iconClassName = '',
  maxWidth = '276px',
  role = 'tooltip',
  id,
  ariaLabel
}) => {
  const getPlacementClass = () => {
    switch (placement) {
      case 'right':
        return '[--placement:right] rtl:[--placement:left]';
      case 'bottom':
        return '[--placement:bottom]';
      case 'left':
        return '[--placement:left]';
      case 'auto':
        return '[--placement:auto]';
      default:
        return ''; // Default is top
    }
  };

  const getTriggerClass = () => {
    return `[--trigger:${trigger}]`;
  };

  const overlayClasses = [
    getTriggerClass(),
    getPlacementClass(),
    className
  ].filter(Boolean).join(' ');

  const contentClasses = [
    'hs-tooltip-content',
    'ti-main-tooltip-content',
    'border',
    'border-defaultborder',
    'dark:border-defaultborder/10',
    `!bg-${color}`,
    '!text-white',
    '!py-4',
    '!px-4',
    contentClassName
  ].filter(Boolean).join(' ');

  const iconClasses = [
    'hs-tooltip-toggle',
    'ti-main-tooltip-toggle',
    iconClassName
  ].filter(Boolean).join(' ');

  const maxWidthStyle = maxWidth ? { maxWidth } : { maxWidth: '276px' };

  if (disabled) {
    return (
      <Fragment>
        <div className="opacity-60">
          {icon}
        </div>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <SpkOverlay customClass={overlayClasses}>
        <div 
          className={iconClasses}
          aria-label={ariaLabel}
          role="button"
          tabIndex={0}
        >
          {icon}
          <div 
            className={contentClasses}
            style={maxWidthStyle}
            role={role}
            id={id}
          >
            {content}
          </div>
        </div>
      </SpkOverlay>
    </Fragment>
  );
};

export default SpkIconPopover;
