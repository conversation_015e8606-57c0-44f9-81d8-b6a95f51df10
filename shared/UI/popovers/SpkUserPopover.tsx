import React, { Fragment } from 'react';
import Image from 'next/image';
import SpkButton from '@/shared/@spk-reusable-components/uielements/spk-button';
import Link from 'next/link';

export interface UserInfo {
  name: string;
  email: string;
  avatar: string;
  role?: string;
  badge?: string;
  company?: string;
  phone?: string;
}

export interface UserAction {
  icon: string;
  label: string;
  href?: string;
  onClick?: () => void;
}

export interface SpkUserPopoverProps {
  user: UserInfo;
  actions?: UserAction[];
  followAction?: {
    label: string;
    onClick: () => void;
  };
  flagAction?: {
    href: string;
  };
  trigger?: 'hover' | 'click' | 'focus';
  placement?: 'top' | 'right' | 'bottom' | 'left' | 'auto';
  disabled?: boolean;
  className?: string;
  contentClassName?: string;
  maxWidth?: string;
  role?: string;
  id?: string;
}

const SpkUserPopover: React.FC<SpkUserPopoverProps> = ({
  user,
  actions = [],
  followAction,
  flagAction,
  trigger = 'hover',
  placement = 'top',
  disabled = false,
  className = '',
  contentClassName = '',
  maxWidth = '320px',
  role = 'tooltip',
  id
}) => {
  const getPlacementClass = () => {
    switch (placement) {
      case 'right':
        return '[--placement:right]';
      case 'bottom':
        return '[--placement:bottom]';
      case 'left':
        return '[--placement:left]';
      case 'auto':
        return '[--placement:auto]';
      default:
        return '[--placement:top]'; // Default is top
    }
  };

  const getTriggerClass = () => {
    return `[--trigger:${trigger}]`;
  };

  const overlayClasses = [
    'hs-tooltip',
    'inline-block',
    getTriggerClass(),
    getPlacementClass(),
    'sm:![--placement:right]',
    className
  ].filter(Boolean).join(' ');

  const contentClasses = [
    'hs-tooltip-content',
    'overflow-hidden',
    'ti-main-tooltip-content',
    'border',
    'border-defaultborder',
    'dark:border-defaultborder/10',
    'dark:bg-bodybg',
    'bg-white',
    '!p-0',
    'max-w-xs',
    'w-full',
    'after:absolute',
    'after:top-0',
    'after:-start-4',
    'after:w-4',
    'after:h-full',
    'hidden',
    contentClassName
  ].filter(Boolean).join(' ');

  const maxWidthStyle = maxWidth ? { maxWidth } : { maxWidth: '320px' };

  if (disabled) {
    return (
      <Fragment>
        <div className="opacity-60">
          <div className="max-w-xs p-3 flex items-center gap-x-3 bg-white border border-gray-200 rounded-xl shadow-sm dark:bg-bodybg dark:border-white/10">
            <Image className="inline-block size-9 rounded-full" src={user.avatar} alt="User Avatar" width={36} height={36} />
            <div className="grow">
              <h4 className="font-semibold text-sm text-gray-800 dark:text-white">
                {user.name}
              </h4>
              <p className="text-sm text-gray-800 md:text-gray-500 dark:text-white md:dark:text-white/70">
                {user.email}
              </p>
            </div>
          </div>
        </div>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <div className={overlayClasses}>
        <div className="hs-tooltip-toggle max-w-xs p-3 flex items-center gap-x-3 bg-white border border-gray-200 rounded-xl shadow-sm dark:bg-bodybg dark:border-white/10">
          <Image className="inline-block size-9 rounded-full" src={user.avatar} alt="User Avatar" width={36} height={36} />
          <div className="grow">
            <h4 className="font-semibold text-sm text-gray-800 dark:text-white">
              {user.name}
            </h4>
            <p className="text-sm text-gray-800 md:text-gray-500 dark:text-white md:dark:text-white/70">
              {user.email}
            </p>
          </div>

          {/* Popover Content */}
          <div
            className={contentClasses}
            style={maxWidthStyle}
            role={role}
            id={id}
          >
            {/* Header */}
            <div className="py-3 px-4 border-b border-gray-200 dark:border-white/10">
              <div className="flex items-center gap-x-3">
                <Image className="flex-shrink-0 inline-block size-10 rounded-full ring-2 ring-white dark:ring-bodybg" src={user.avatar} alt="User Avatar" width={40} height={40} />
                <div className="grow">
                  <h6 className="font-semibold text-gray-800 dark:text-white">
                    {user.name}
                    {user.badge && (
                      <span className="ms-0.5 inline-flex items-center align-middle gap-x-1.5 py-0.5 px-1.5 mt-1 rounded-md text-[11px] font-medium bg-gray-800 text-white dark:bg-bodybg dark:text-white">
                        {user.badge}
                      </span>
                    )}
                  </h6>
                  {user.role && (
                    <p className="text-sm text-gray-500 dark:text-white/70">
                      {user.role}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Contact Info List */}
            <ul className="py-3 px-4 space-y-1">
              {user.company && (
                <li>
                  <div className="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-white">
                    <i className="ri-building-line text-gray-600 dark:text-white/70" />
                    {user.company}
                  </div>
                </li>
              )}
              {user.phone && (
                <li>
                  <div className="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-white">
                    <i className="ri-smartphone-line text-gray-600 dark:text-white/70" />
                    {user.phone}
                  </div>
                </li>
              )}
              <li>
                <div className="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-white">
                  <i className="ri-mail-line text-gray-600 dark:text-white/70" />
                  {user.email}
                </div>
              </li>
              {actions.map((action, index) => (
                <li key={index}>
                  <div className="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-white">
                    <i className={`${action.icon} text-gray-600 dark:text-white/70`} />
                    {action.href ? (
                      <Link href={action.href} className="hover:text-primary">
                        {action.label}
                      </Link>
                    ) : (
                      <button onClick={action.onClick} className="hover:text-primary">
                        {action.label}
                      </button>
                    )}
                  </div>
                </li>
              ))}
            </ul>

            {/* Footer */}
            <div className="py-2 px-4 flex justify-between items-center bg-gray-100 dark:bg-bodybg">
              {flagAction && (
                <Link
                  scroll={false}
                  className="inline-flex items-center gap-x-1.5 text-xs text-gray-500 hover:text-primary disabled:opacity-50 disabled:pointer-events-none dark:text-white/70 dark:hover:text-white dark:focus:outline-none"
                  href={flagAction.href}
                >
                  <i className="ri-flag-line" />
                  Flag
                </Link>
              )}
              {followAction && (
                <SpkButton
                  type="button"
                  customClass="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-full border border-transparent bg-primary text-white hover:bg-primary/80 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-primary"
                  onclickfunc={followAction.onClick}
                >
                  {followAction.label}
                </SpkButton>
              )}
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default SpkUserPopover;
