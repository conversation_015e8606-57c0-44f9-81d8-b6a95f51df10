import React, { Fragment } from 'react';
import SpkOverlay from '@/shared/@spk-reusable-components/uielements/spk-overlay';

import Link from 'next/link';

export interface ReviewRating {
  stars: number;
  percentage: number;
}

export interface SpkReviewPopoverProps {
  children: React.ReactNode;
  overallRating: number;
  totalReviews: number;
  ratings: ReviewRating[];
  seeAllLink?: string;
  helpLink?: string;
  placement?: 'top' | 'right' | 'bottom' | 'left' | 'auto';
  trigger?: 'click' | 'hover' | 'focus';
  disabled?: boolean;
  className?: string;
  contentClassName?: string;
  maxWidth?: string;
  role?: string;
  id?: string;
}

const SpkReviewPopover: React.FC<SpkReviewPopoverProps> = ({
  children,
  overallRating,
  totalReviews,
  ratings,
  seeAllLink = '#',
  helpLink = '#',
  placement = 'top',
  trigger = 'click',
  disabled = false,
  className = '',
  contentClassName = '',
  maxWidth = '400px',
  role = 'tooltip',
  id
}) => {
  const getPlacementClass = () => {
    switch (placement) {
      case 'right':
        return '[--placement:right] rtl:[--placement:left]';
      case 'bottom':
        return '[--placement:bottom]';
      case 'left':
        return '[--placement:left]';
      case 'auto':
        return '[--placement:auto]';
      default:
        return ''; // Default is top
    }
  };

  const getTriggerClass = () => {
    return `[--trigger:${trigger}]`;
  };

  const overlayClasses = [
    getTriggerClass(),
    getPlacementClass(),
    className
  ].filter(Boolean).join(' ');

  const contentClasses = [
    'hs-tooltip-content',
    'ti-main-tooltip-content',
    'border',
    'border-defaultborder',
    'dark:border-defaultborder/10',
    'dark:bg-bodybg',
    'bg-white',
    'max-w-xs',
    'w-full',
    'hidden',
    '!z-[105]',
    contentClassName
  ].filter(Boolean).join(' ');

  const maxWidthStyle = maxWidth ? { maxWidth } : { maxWidth: '400px' };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <i
        key={index}
        className={`ri-star-fill text-lg ${index < rating ? 'text-warning dark:text-warning' : 'text-[#404040] dark:text-[#333333]'}`}
      />
    ));
  };

  if (disabled) {
    return (
      <Fragment>
        <div className="opacity-60">
          {children}
        </div>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <SpkOverlay customClass={overlayClasses}>
        <div className="hs-tooltip-toggle ti-main-tooltip-toggle">
          {children}
          <div
            className={contentClasses}
            style={maxWidthStyle}
            role={role}
            id={id}
          >
            <div className="p-4">
              {/* Header */}
              <div className="mb-3 flex justify-between items-center gap-x-3">
                <div className="flex items-center gap-x-2">
                  <h4 className="font-semibold text-gray-800 dark:text-white">
                    {overallRating.toFixed(1)}
                  </h4>
                  {/* Rating */}
                  <div className="flex">
                    {renderStars(Math.round(overallRating))}
                  </div>
                </div>
                <Link
                  scroll={false}
                  className="inline-flex items-center gap-x-1 text-xs text-primary decoration-2 hover:underline font-medium dark:focus:outline-none dark:focus:ring-0 dark:focus:shadow-none dark:focus:ring-primary"
                  href={seeAllLink}
                >
                  See all ({totalReviews})
                </Link>
              </div>

              {/* Progress Bars */}
              <div className="mb-3">
                {ratings.map((rating, index) => (
                  <div key={index} className="flex items-center gap-x-3 whitespace-nowrap">
                    <div className="w-10 text-end">
                      <span className="text-sm text-gray-800 dark:text-white">{rating.stars} star</span>
                    </div>
                    <div className="flex w-full h-2 bg-gray-200 rounded-full overflow-hidden dark:bg-bodybg2" role="progressbar" aria-valuenow={rating.percentage} aria-valuemin={0} aria-valuemax={100}>
                      <div
                        className="flex flex-col justify-center rounded-full overflow-hidden bg-warning text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-yellow-600"
                        style={{ width: `${rating.percentage}%` }}
                      />
                    </div>
                    <div className="w-10 text-end">
                      <span className="text-sm text-gray-800 dark:text-white">{rating.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Help Link */}
              <Link
                scroll={false}
                className="inline-flex items-center gap-x-1 text-xs text-gray-600 decoration-2 hover:underline dark:text-white/70 dark:focus:outline-none dark:focus:ring-0 dark:shadow-none dark:focus:ring-gray-400"
                href={helpLink}
              >
                How reviews and ratings work
                <i className="ri-arrow-right-s-line rtl:rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </SpkOverlay>
    </Fragment>
  );
};

export default SpkReviewPopover;
