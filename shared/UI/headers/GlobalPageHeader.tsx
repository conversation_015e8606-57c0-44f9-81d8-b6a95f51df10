import React from 'react';
import Image from 'next/image';
import { PrimaryButton } from '@/shared/UI/components';

export interface GlobalPageHeaderAction {
  label: string;
  icon?: string;
  onClick: () => void;
  href?: string;
  className?: string;
}

export interface GlobalPageHeaderProps {
  title: string;
  bgIcon?: string;
  actions?: GlobalPageHeaderAction[];
  className?: string;
  showIcon?: boolean;
}

/**
 * Global Page Header Component
 *
 * Reusable header component for admin pages with:
 * - Dark background matching the main page background
 * - Customizable title with proper styling
 * - Support for multiple action buttons with different variants
 * - Golden button styling for primary actions with multi-layered shadows
 * - Dark theme compatibility
 */
const GlobalPageHeader: React.FC<GlobalPageHeaderProps> = ({
  title,
  bgIcon: _bgIcon,
  actions = [],
  className = "",
  showIcon = true
}) => {
  return (
    <div
      className={`
        relative rounded-lg mb-6 overflow-hidden ${className} h-[80px]
        bg-purple-header-gradient
      `}
    >
      {/* Header Icon on the left side */}
      {/* {showIcon && ( */}
      <div className="absolute left-[-5px] top-1/2 transform -translate-y-1/2">
        <Image src="/assets/images/header-icon.png" alt="header icon" className="w-[80px] h-[80px] rotate-[8deg]" width={80} height={80} />
      </div>
      {/* )} */}

      {/* Content */}
      <div className="relative z-10 flex items-center justify-between h-full px-6">
        <div className={`flex-1 ${showIcon ? 'pl-16' : ''}`}>
          {/* Page Title */}
          <h1 className="text-white text-2xl font-medium">
            {title}
          </h1>
        </div>

        {/* Action Buttons */}
        {actions.length > 0 && (
          <div className="flex items-center gap-3">
            {actions.map((action, index) => (
              action.href ? (
                <PrimaryButton
                  key={index}
                  size={'lg'}
                  as={'link'}
                  href={action.href}
                  onClick={action.onClick}
                  icon={action.icon ? {
                    type: 'FONT_ICON',
                    iconClass: action.icon,
                    library: 'remix'
                  } : undefined}
                  iconPosition="left"
                >
                  {action.label}
                </PrimaryButton>
              ) : (
                <PrimaryButton
                  key={index}
                  size={'lg'}
                  as={'button'}
                  onClick={action.onClick}
                  icon={action.icon ? {
                    type: 'FONT_ICON',
                    iconClass: action.icon,
                    library: 'remix'
                  } : undefined}
                  iconPosition="left"
                >
                  {action.label}
                </PrimaryButton>
              )
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default GlobalPageHeader;
