// shared/types/env.d.ts
declare namespace NodeJS {
  interface ProcessEnv {
    // Next.js built-in environment variables
    NODE_ENV: 'development' | 'production' | 'test';

    // Custom environment variables used in the project
    NEXT_PUBLIC_API_BASE_URL?: string;
    NEXT_PUBLIC_ADMIN_BACKEND_URL?: string;
    NEXT_PUBLIC_CASHIER_API_URL?: string;
    NEXT_PUBLIC_WEBSOCKET_URL?: string;

    // Add other environment variables as needed
    // Example:
    // NEXT_PUBLIC_APP_NAME?: string;
    // DATABASE_URL?: string;
    // JWT_SECRET?: string;
  }
}
