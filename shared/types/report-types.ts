// shared/types/report-types.ts - Type definitions for report pages

// Cashier Report Types
export interface CashierTransaction {
  id?: string;
  transactionId: string;
  userName: string;
  actionType: string;
  marketId?: string; // Market ID
  marketName?: string; // Market Name
  amount: number;
  currency: string | number; // Can be ISO code (string) or currency ID (number)
  balance?: number;
  status: string;
  createdAt: string; // Transaction Timestamp
  description?: string;
  roundId?: string;
  gameProvider?: string;
  gameType?: string;
}

export interface CashierReportFilters {
  size: number;
  page: number;
  search?: string;
  transactionId?: string;
  debitTransactionId?: string;
  roundId?: string;
  tenantId?: string;
  order?: 'asc' | 'desc';
  sortBy?: string;
  actionType?: string;
  timePeriod?: string;
  userId?: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  agentId?: string;
  timeZone?: string;
  timeZoneName?: string;
  dateTime?: string;
  username?: string; // For username filter display
}

export interface CashierReportResponse {
  data: CashierTransaction[];
  success: number;
  message: string;
  errors: string[];
  count?: number;
  totalPages?: number;
  currentPage?: number;
}

// Bet Report Types
export interface BetReportData {
  id?: string;
  betId: string; // Bet ID
  transactionId?: string;
  userName: string; // Username
  playerName?: string; // Alternative field name for player
  betAmount: number; // Stake Amount
  winAmount: number; // Win Amount
  status: string; // Status Settlement
  payoutStatus?: string | number; // Payout settlement status: 'settled', 'rejected', 'pending' or numeric (1=settled, 2=unsettled, 3=rejected)
  createdAt: string; // Date & Time
  gameProvider?: string;
  gameType?: string;
  roundId?: string;
  marketId?: string; // Market ID
  marketName?: string; // Market Name
  betType?: string; // Bet Type
  provider?: string; // Provider field (alternative to gameProvider)
  odds?: number; // Odds
  currency: string | number; // Can be ISO code (string) or currency ID (number)
}

export interface BetReportFilters {
  page: number;
  limit: number;
  startDate: string;
  endDate: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  transactionId?: string;
  playerId?: string;
  marketId?: string;
  status?: string;
  payoutStatus?: string;
  username?: string; // For username filter display
}

export interface BetReportResponse {
  data: BetReportData[];
  success: number;
  message: string;
  errors: string[];
  totalBetAmount?: number;
  totalWinAmount?: number;
  ggr?: number;
  count?: number;
  totalPages?: number;
  currentPage?: number;
}

// Financial Report Types
export interface FinancialTransaction {
  id?: string;
  transactionId: string;
  userName: string;
  playerName?: string; // Alternative field name for player
  actionType: string;
  actionCategory: string;
  amount: number;
  currency: string | number; // Can be ISO code (string) or currency ID (number)
  balance?: number;
  totalBalance: number; // Total Balance after transaction
  status: string;
  createdAt: string; // Timestamp
  description?: string;
  gameProvider?: string;
  gameType?: string;
  utrNumber?: string; // UTR number field
}

export interface FinancialReportFilters {
  size: number;
  page: number;
  gameProvider?: string;
  gameType?: string;
  search?: string;
  roundId?: string;
  amount?: string;
  transactionId?: string;
  debitTransactionId?: string;
  utrNumber?: string;
  tenantId?: string;
  currencyId?: string;
  order?: 'asc' | 'desc';
  sortBy?: string;
  startDate?: string;
  endDate?: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  actionType?: string;
  actionCategory?: string;
  timePeriod?: string;
  timeType?: string;
  timeZone?: string;
  timeZoneName?: string;
  lifetimeRecords?: boolean;
  playerId?: string;
  dateTime?: string;
  username?: string; // For username filter display
  totals?: boolean; // For fetching summary totals
}

export interface FinancialReportResponse {
  data: FinancialTransaction[];
  success: number;
  message: string;
  errors: string[];
  count?: number;
  totalPages?: number;
  currentPage?: number;
  totalAmount?: number;
  totalDeposits?: number;
  totalWithdrawals?: number;
  totals?: {
    totalDeposit: number;
    totalWithdraw: number;
    totalBet: number;
  };
}

// Default filter values
export const DEFAULT_CASHIER_REPORT_FILTERS: CashierReportFilters = {
  size: 25,
  page: 1,
  order: 'desc',
  sortBy: 'created_at',
  timeZone: 'UTC +00:00',
  timeZoneName: 'UTC +00:00',
  userId: '', // Initialize userId for user search functionality
  username: '' // Initialize username for display in filter
};

export const DEFAULT_BET_REPORT_FILTERS: BetReportFilters = {
  page: 1,
  limit: 25,
  startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 30 days ago
  endDate: new Date().toISOString().slice(0, 16), // Now
  dateRange: {
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16),
    endDate: new Date().toISOString().slice(0, 16)
  },
  status: "",
  payoutStatus: "",
  playerId: "", // Initialize playerId for user search functionality
  username: "" // Initialize username for display in filter
};

export const DEFAULT_FINANCIAL_REPORT_FILTERS: FinancialReportFilters = {
  size: 25,
  page: 1,
  order: 'desc',
  sortBy: 'created_at',
  actionCategory: 'financial',
  timeType: 'today',
  timeZone: 'UTC +00:00',
  timeZoneName: 'UTC +00:00',
  lifetimeRecords: false,
  playerId: '', // Initialize playerId for user search functionality
  username: '' // Initialize username for display in filter
};
