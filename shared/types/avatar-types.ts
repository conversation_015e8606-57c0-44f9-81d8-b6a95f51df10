// Basic avatar interface
export interface AvatarType {
  id: number;
  class: string;
  src: string;
}

// Avatar with icon interface
export interface AvatarIconType extends AvatarType {
  icon: string;
  color: string;
}

// Avatar with number badge interface
export interface AvatarNumberType extends AvatarType {
  color: string;
  number: string;
}

// Avatar stack interface
export interface AvatarStackType {
  id: number;
  src: string;
}

// Avatar with initials interface
export interface AvatarInitialType {
  id: number;
  class: string;
  color: string;
  data1: string;
}

// Avatar component props interface
export interface AvatarProps {
  src?: string;
  alt?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  shape?: 'rounded' | 'circle' | 'square';
  status?: 'online' | 'offline';
  badge?: {
    content: string | number;
    color: string;
    icon?: string;
  };
  initials?: string;
  backgroundColor?: string;
  className?: string;
}
