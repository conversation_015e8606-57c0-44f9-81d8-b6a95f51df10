// shared/types/global.d.ts
import React from 'react';
// Common interfaces used across the application

export interface BaseComponent {
  className?: string;
  children?: React.ReactNode;
}

export interface ApiResponse<T = any> {
  success: number;
  message: string;
  data?: T;
  count?: number;
}

// Comprehensive User interface that consolidates all user properties used across the application
export interface User {
  // Core identification
  id: number;
  firstName: string;
  lastName?: string;
  email: string;
  userName?: string; // Added for sportsbook and user management compatibility
  internalPlayerID?: string; // Added for WebSocket authentication and bet notifications

  // Contact information
  phone?: string;
  phoneCode?: string;
  phoneVerified?: boolean;

  // Status and permissions
  active?: boolean;
  isAuthenticated?: boolean;

  // Timestamps
  createdAt?: string;
  updatedAt?: string;

  // User preferences
  timezone?: string; // User's timezone preference (e.g., "America/New_York", "+05:30", etc.)

  // Admin/Agent specific fields (optional for regular users)
  tenantId?: number;
  agentName?: string;
  parentType?: string;
  parentId?: number;

  // Authentication related
  affiliateToken?: string;
  roles?: string[];

  // Additional optional fields for compatibility
  [key: string]: any;
}

export interface MenuItem {
  id: number;
  title: string;
  path?: string;
  icon?: React.ReactNode;
  type: 'link' | 'sub' | 'empty';
  menutitle?: string;
  selected?: boolean;
  active?: boolean;
  badgetxt?: string;
  class?: string;
  children?: MenuItem[];
  dirchange?: boolean;
  [key: string]: any;
}

export interface ThemeConfig {
  lang: string;
  dir: 'ltr' | 'rtl';
  class: 'light' | 'dark';
  dataMenuStyles: string;
  dataNavLayout: 'vertical' | 'horizontal';
  dataHeaderStyles: string;
  dataVerticalStyle: string;
  toggled: string;
  dataNavStyle: string;
  dataPageStyle: string;
  dataWidth: string;
  dataMenuPosition: string;
  dataHeaderPosition: string;
  loader: string;
  iconOverlay: string;
  colorPrimaryRgb: string;
  PrimaryRgb: string;
  bodyBg: string;
  darkBg: string;
  inputBorder: string;
  lightRgb: string;
  gray: string;
  bgImg: string;
  iconText: string;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
