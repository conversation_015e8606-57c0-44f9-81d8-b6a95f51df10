import { ChangeEvent, HTMLInputTypeAttribute } from 'react';

// Input component props interface
export interface InputProps {
  type?: HTMLInputTypeAttribute;
  id?: string;
  name?: string;
  value?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  helpText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'dotted' | 'dashed';
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'full';
}

// Checkbox/Radio props interface
export interface CheckboxRadioProps {
  id?: string;
  name?: string;
  value?: string;
  checked?: boolean;
  disabled?: boolean;
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  inline?: boolean;
  reverse?: boolean;
}

// Switch props interface
export interface SwitchProps {
  id?: string;
  name?: string;
  checked?: boolean;
  disabled?: boolean;
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  labelPosition?: 'before' | 'after';
  size?: 'sm' | 'md' | 'lg';
}

// Select props interface
export interface SelectProps {
  id?: string;
  name?: string;
  value?: string;
  disabled?: boolean;
  onChange?: (e: ChangeEvent<HTMLSelectElement>) => void;
  options: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
  placeholder?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

// Textarea props interface
export interface TextareaProps {
  id?: string;
  name?: string;
  value?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  rows?: number;
  cols?: number;
  onChange?: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  label?: string;
  helpText?: string;
}
