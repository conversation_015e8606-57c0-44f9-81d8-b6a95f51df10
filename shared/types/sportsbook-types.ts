// shared/types/sportsbook-types.ts
// TypeScript interfaces for sportsbook-related data structures

/**
 * Individual sport discipline/category
 */
export interface SportDiscipline {
  id: string;
  name: string;
}

/**
 * Response from TurboStars marketing event list API
 */
export interface SportsCategoriesResponse {
  message: string;
  data: {
    availableDisciplines: SportDiscipline[];
  };
}

/**
 * Props for sports category card component
 */
export interface SportsCategoryCardProps {
  discipline: SportDiscipline;
  onClick: (discipline: SportDiscipline) => void;
  className?: string;
}

/**
 * Props for sports category selection page
 */
export interface SportsCategorySelectionProps {
  onCategorySelect: (discipline: SportDiscipline) => void;
}

export default SportsCategoriesResponse;
