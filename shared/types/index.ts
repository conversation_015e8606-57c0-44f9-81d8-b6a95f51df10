// shared/types/index.ts
// Central export file for all type definitions

export * from './global';
// env.d.ts is a declaration file, not a module to export

// Re-export commonly used types from stores
export type { UIState } from '../stores/uiStore';

// Re-export types from data files
export type { LanguageOption } from '../data/languageOptions';
export type { DividerProfileMenuItem, LinkProfileMenuItem, ProfileMenuItem } from '../data/profileMenuItems';

// Export component type definitions
export * from './accordion-types';
export * from './avatar-types';
export * from './form-types';
export * from './table-types';
export * from './ui-element-types';
export * from './user-management-types';
export * from './export-types';

// Re-export financial report types for easier access
export type {
    FinancialReportData, FinancialReportError, FinancialReportFilters, FinancialReportResponse, FinancialSummaryData,
    FinancialTransactionData
} from './user-management-types';

// Re-export login history types for easier access
export type {
    DEFAULT_LOGIN_HISTORY_FILTERS, LoginHistoryApiResponse, LoginHistoryData, LoginHistoryFilters, LoginHistoryRecord, LoginHistoryResponse
} from './user-management-types';

// Re-export report types
export type {
    BetReportData,
    BetReportFilters,
    BetReportResponse, CashierReportFilters,
    CashierReportResponse, CashierTransaction, DEFAULT_BET_REPORT_FILTERS, DEFAULT_CASHIER_REPORT_FILTERS, DEFAULT_FINANCIAL_REPORT_FILTERS, FinancialTransaction,
    FinancialReportFilters as NewFinancialReportFilters,
    FinancialReportResponse as NewFinancialReportResponse
} from './report-types';

