// Basic table data interface
import React from 'react';
export interface BasicTableData {
  id: number;
  name: string;
  date: string;
  number: string;
  status: string;
  color: string;
}

// User table data interface
export interface UserTableData {
  id: number;
  src: string;
  name: string;
  mail: string;
  color: string;
  class: string;
  text: string;
  class1: string;
  color1: string;
}

// Order table data interface
export interface OrderTableData {
  id: number;
  src: string;
  order: string;
  date: string;
  name: string;
}

// Transaction table data interface
export interface TransactionTableData {
  id: number;
  name: string;
  date: string;
  text: string;
  transactionid?: string;
  status?: string;
  color?: string;
}

// Group table data interface
export interface GroupTableData {
  id: number;
  product: string;
  seller: string;
  percent: string;
  sold: string;
  icon: string;
  color: string;
}

// Table header interface
export interface TableHeader {
  title: string;
  className?: string;
}

// Table component props interface
export interface TableProps {
  tableClass?: string;
  tableRowclass?: string;
  header: TableHeader[];
  children: React.ReactNode;
}
