import { ReactNode } from 'react';

// Basic accordion item interface
export interface AccordionItem {
  Id: string;
  title: string;
  Customclass: string;
  content: ReactNode;
  Mainid: string;
  Custombodyclass: string;
  Svgcontent1: ReactNode;
  Svgcontent2: ReactNode;
}

// Accordion component props interface
export interface AccordionProps {
  items: AccordionItem[];
  SvgIcon?: boolean;
  Titletext?: 'before' | 'after';
  Toggleclass?: string;
  Groupclass?: string;
  Alwaysopen?: boolean;
}

// Nested accordion data interface
export interface NestedAccordionItem extends AccordionItem {
  nestedItems?: AccordionItem[];
}
