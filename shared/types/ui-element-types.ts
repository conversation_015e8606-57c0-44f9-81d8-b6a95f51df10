import { ReactNode } from 'react';

// Alert component props interface
export interface AlertProps {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
  role?: string;
  id?: string;
  children: ReactNode;
  icon?: ReactNode;
  title?: string;
  actions?: ReactNode;
  bordered?: boolean;
  borderPosition?: 'top' | 'bottom' | 'left' | 'right';
}

// Badge component props interface
export interface BadgeProps {
  variant?: string;
  children: ReactNode;
  className?: string;
  id?: string;
  onClick?: () => void;
  pill?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

// Spinner component props interface
export interface SpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'border' | 'grow';
  color?: string;
  className?: string;
  label?: string;
  children?: ReactNode;
}

// Border utility props interface
export interface BorderProps {
  side?: 'all' | 'top' | 'right' | 'bottom' | 'left' | 'none';
  color?: string;
  width?: '0' | '1' | '2' | '4' | '8';
  style?: 'solid' | 'dashed' | 'dotted';
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  opacity?: '10' | '25' | '50' | '75' | '100';
  className?: string;
  children?: ReactNode;
}
