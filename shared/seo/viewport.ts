// Viewport configuration utilities for Next.js App Router
import type { Viewport } from 'next';
import { siteConfig } from './config';

/**
 * Default viewport configuration
 */
export const defaultViewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: siteConfig.themeColor,
  colorScheme: 'light dark',
};

/**
 * Generate viewport configuration for pages
 */
export function generateViewport(config?: Partial<Viewport>): Viewport {
  return {
    ...defaultViewport,
    ...config,
  };
}

/**
 * Viewport configuration for authentication pages
 */
export const authViewport: Viewport = generateViewport({
  themeColor: '#0066cc',
  colorScheme: 'light dark',
});

/**
 * Viewport configuration for dashboard pages
 */
export const dashboardViewport: Viewport = generateViewport({
  themeColor: '#0066cc',
  colorScheme: 'light dark',
});

/**
 * Viewport configuration for user management pages
 */
export const userManagementViewport: Viewport = generateViewport({
  themeColor: '#0066cc',
  colorScheme: 'light dark',
});

/**
 * Viewport configuration for error pages
 */
export const errorViewport: Viewport = generateViewport({
  themeColor: '#dc2626',
  colorScheme: 'light dark',
});
