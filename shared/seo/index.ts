// SEO module exports - Server-Side Rendering optimized SEO utilities
// This module provides comprehensive SEO optimization for Next.js App Router

// Types
export type {
  BaseSEOConfig,
  OpenGraphConfig,
  TwitterConfig,
  SEOConfig,
  PageSEOData,
  MetadataParams,
  StructuredDataConfig,
  OrganizationStructuredData,
  WebsiteStructuredData,
  ArticleStructuredData,
  BreadcrumbStructuredData,
  FAQStructuredData,
  ProductStructuredData,
  LocalBusinessStructuredData,
  AnyStructuredData,
  SiteConfig,
} from './types';

// Configuration
import { siteConfig as _siteConfig } from './config';
export {
  siteConfig,
  defaultSEOConfig,
  pageConfigs,
  structuredDataConfigs,
  environmentConfig,
  getEnvironmentConfig,
} from './config';

// Metadata generation utilities
import { generatePageMetadata as _generatePageMetadata } from './metadata';
export {
  generatePageMetadata,
  generateDynamicMetadata,
  generateUserManagementMetadata,
  generateAuthMetadata,
  generateErrorMetadata,
  generateDashboardMetadata,
  generateFinancialReportMetadata,
  createCanonicalUrl,
  generateBreadcrumbMetadata,
  generateMetadataWithStructuredData,
  generateApiDrivenMetadata,
  generatePaginatedMetadata,
} from './metadata';

// Viewport utilities
export {
  defaultViewport,
  generateViewport,
  authViewport,
  dashboardViewport,
  userManagementViewport,
  errorViewport,
} from './viewport';

// Structured data utilities
export {
  generateStructuredDataScript,
  createOrganizationStructuredData,
  createWebsiteStructuredData,
  createArticleStructuredData,
  createBreadcrumbStructuredData,
  createFAQStructuredData,
  createProductStructuredData,
  createLocalBusinessStructuredData,
  createSoftwareApplicationStructuredData,
  createPersonStructuredData,
  createSearchActionStructuredData,
  createCourseStructuredData,
  combineStructuredData,
  generateUserManagementStructuredData,
  generateDashboardStructuredData,
} from './structured-data';

// React components
export {
  StructuredData,
  OrganizationStructuredData as OrganizationStructuredDataComponent,
  WebsiteStructuredData as WebsiteStructuredDataComponent,
  BreadcrumbStructuredData as BreadcrumbStructuredDataComponent,
  ArticleStructuredData as ArticleStructuredDataComponent,
  FAQStructuredData as FAQStructuredDataComponent,
  ProductStructuredData as ProductStructuredDataComponent,
  SoftwareApplicationStructuredData,
  PageStructuredData,
} from './components/StructuredData';

// Utility functions for common SEO tasks

/**
 * Quick metadata generation for standard pages
 */
export function createPageMetadata(
  title: string,
  description: string,
  path?: string,
  options?: {
    keywords?: string[];
    image?: string;
    noindex?: boolean;
    type?: 'website' | 'article';
  }
) {
  return _generatePageMetadata({
    title,
    description,
    keywords: options?.keywords,
    canonical: path ? `${_siteConfig.url}${path.startsWith('/') ? path : `/${path}`}` : undefined,
    noindex: options?.noindex,
    openGraph: {
      title,
      description,
      type: options?.type || 'website',
      images: options?.image ? [{
        url: options.image,
        width: 1200,
        height: 630,
        alt: title,
      }] : undefined,
    },
    twitter: {
      title,
      description,
      images: options?.image ? [options.image] : undefined,
    },
  });
}

/**
 * Quick structured data generation for common page types
 */
export function createPageStructuredData(
  _pageType: 'dashboard' | 'userManagement' | 'article',
  _data?: {
    title?: string;
    description?: string;
    url?: string;
    breadcrumbs?: Array<{ name: string; url?: string }>;
  }
) {
  // Return empty array for now to avoid circular dependencies
  // Use the structured data components directly in your pages
  return [];
}

/**
 * Generate complete SEO package for a page (metadata + structured data)
 */
export function generateCompleteSEO(config: {
  title: string;
  description: string;
  path?: string;
  pageType?: 'dashboard' | 'userManagement' | 'article';
  keywords?: string[];
  image?: string;
  breadcrumbs?: Array<{ name: string; url?: string }>;
  structuredData?: any[];
  noindex?: boolean;
}) {
  const metadata = createPageMetadata(
    config.title,
    config.description,
    config.path,
    {
      keywords: config.keywords,
      image: config.image,
      noindex: config.noindex,
      type: config.pageType === 'article' ? 'article' : 'website',
    }
  );

  const structuredData = config.structuredData || [];

  return {
    metadata,
    structuredData,
  };
}

/**
 * SEO validation utility
 */
export function validateSEO(config: {
  title?: string;
  description?: string;
  keywords?: string[];
}) {
  const issues: string[] = [];

  if (!config.title) {
    issues.push('Title is required');
  } else if (config.title.length > 60) {
    issues.push('Title should be 60 characters or less');
  } else if (config.title.length < 30) {
    issues.push('Title should be at least 30 characters');
  }

  if (!config.description) {
    issues.push('Description is required');
  } else if (config.description.length > 160) {
    issues.push('Description should be 160 characters or less');
  } else if (config.description.length < 120) {
    issues.push('Description should be at least 120 characters');
  }

  if (config.keywords && config.keywords.length > 10) {
    issues.push('Too many keywords (max 10 recommended)');
  }

  return {
    isValid: issues.length === 0,
    issues,
  };
}

/**
 * Generate robots.txt content
 */
export function generateRobotsTxt(options?: {
  disallow?: string[];
  allow?: string[];
  sitemap?: string;
  crawlDelay?: number;
}) {
  const lines = ['User-agent: *'];

  if (options?.disallow) {
    options.disallow.forEach(path => {
      lines.push(`Disallow: ${path}`);
    });
  }

  if (options?.allow) {
    options.allow.forEach(path => {
      lines.push(`Allow: ${path}`);
    });
  }

  if (options?.crawlDelay) {
    lines.push(`Crawl-delay: ${options.crawlDelay}`);
  }

  if (options?.sitemap) {
    lines.push('', `Sitemap: ${options.sitemap}`);
  }

  return lines.join('\n');
}

/**
 * Default export with commonly used functions
 */
const seoUtils = {
  generatePageMetadata: _generatePageMetadata,
  createPageMetadata,
  createPageStructuredData,
  generateCompleteSEO,
  validateSEO,
};

export default seoUtils;
