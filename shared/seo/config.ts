// Site-wide SEO configuration
import { SiteConfig } from './types';

/**
 * Global site configuration for SEO
 * This configuration is used across all pages as default values
 */
export const siteConfig: SiteConfig = {
  name: "Xintra",
  description: "Advanced user management and analytics platform for modern businesses. Comprehensive dashboard for managing users, tracking analytics, and optimizing business operations.",
  url: process.env.NEXT_PUBLIC_SITE_URL || "https://xintra.com",
  ogImage: "/assets/images/og-image.png",
  links: {
    twitter: "https://twitter.com/xintra",
    github: "https://github.com/xintra",
    linkedin: "https://linkedin.com/company/xintra",
    facebook: "https://facebook.com/xintra"
  },
  creator: {
    name: "Xintra Team",
    url: "https://xintra.com",
    twitter: "@xintra"
  },
  keywords: [
    "user management",
    "analytics platform",
    "business dashboard",
    "user analytics",
    "admin panel",
    "business intelligence",
    "data visualization",
    "user tracking",
    "performance metrics",
    "business optimization",
    "enterprise software",
    "SaaS platform",
    "customer management",
    "business tools",
    "data analytics"
  ],
  authors: [
    {
      name: "Xintra Team",
      url: "https://xintra.com/team"
    }
  ],
  themeColor: "#0066cc",
  locale: "en_US",
  alternateLocales: ["en_GB", "en_AU"]
};

/**
 * Default SEO metadata base configuration
 */
export const defaultSEOConfig = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`
  },
  description: siteConfig.description,
  keywords: siteConfig.keywords,
  authors: siteConfig.authors,
  creator: siteConfig.creator.name,
  publisher: siteConfig.name,
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website' as const,
    locale: siteConfig.locale,
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: `${siteConfig.name} - ${siteConfig.description}`,
      }
    ],
  },
  twitter: {
    card: 'summary_large_image' as const,
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: siteConfig.creator.twitter,
    site: siteConfig.creator.twitter,
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
    other: [
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        url: '/favicon-32x32.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        url: '/favicon-16x16.png',
      }
    ]
  },
  manifest: '/manifest.webmanifest',
  // Note: themeColor, colorScheme, and viewport moved to viewport export
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large' as const,
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
    // Add other verification codes as needed
  },
  alternates: {
    canonical: siteConfig.url,
  },
  category: 'technology',
  classification: 'Business Software',
  referrer: 'origin-when-cross-origin' as any,
  generator: 'Next.js',
  applicationName: siteConfig.name,
};

/**
 * Page-specific SEO configurations
 */
export const pageConfigs = {
  home: {
    title: "Dashboard - Business Analytics & User Management",
    description: "Comprehensive business dashboard with advanced analytics, user management, and performance tracking. Get insights into your business operations with real-time data visualization.",
    keywords: ["business dashboard", "analytics", "user management", "real-time data", "business intelligence"],
  },
  userManagement: {
    title: "User Management - Comprehensive User Administration",
    description: "Advanced user management system with detailed user profiles, activity tracking, and administrative controls. Manage user accounts, permissions, and analytics efficiently.",
    keywords: ["user management", "user administration", "user profiles", "account management", "user analytics"],
  },
  userDetails: {
    title: "User Details - Comprehensive User Profile",
    description: "Detailed user profile with complete information, activity history, wallet details, and administrative controls for comprehensive user management.",
    keywords: ["user profile", "user details", "user activity", "account information", "user analytics"],
  },
  createUser: {
    title: "Create User - Add New User Account",
    description: "Create new user accounts with comprehensive profile setup, security configurations, and administrative settings for effective user management.",
    keywords: ["create user", "new account", "user registration", "account setup", "user administration"],
  },
  editUser: {
    title: "Edit User - Modify User Account",
    description: "Edit and update user account information, settings, and configurations with comprehensive administrative controls and security options.",
    keywords: ["edit user", "update account", "user settings", "account modification", "user administration"],
  },
  financialReport: {
    title: "Financial Report - User Financial Analytics",
    description: "Comprehensive financial reporting and analytics for user accounts with detailed transaction history, balance tracking, and financial insights.",
    keywords: ["financial report", "user finances", "transaction history", "financial analytics", "account balance"],
  },
  authentication: {
    signIn: {
      title: "Sign In - Access Your Account",
      description: "Secure sign-in to access your Xintra dashboard with advanced user management and analytics capabilities.",
      keywords: ["sign in", "login", "authentication", "account access", "secure login"],
    },
    signUp: {
      title: "Sign Up - Create Your Account",
      description: "Create your Xintra account to access advanced user management and analytics platform for your business needs.",
      keywords: ["sign up", "register", "create account", "new user", "account registration"],
    },
    forgotPassword: {
      title: "Forgot Password - Reset Your Password",
      description: "Reset your password securely to regain access to your Xintra account and continue managing your business operations.",
      keywords: ["forgot password", "password reset", "account recovery", "secure reset"],
    }
  },
  errors: {
    401: {
      title: "Unauthorized Access - 401 Error",
      description: "You don't have permission to access this resource. Please sign in with proper credentials to continue.",
      keywords: ["401 error", "unauthorized", "access denied", "authentication required"],
      noindex: true,
    },
    404: {
      title: "Page Not Found - 404 Error",
      description: "The page you're looking for doesn't exist. Return to the dashboard to continue managing your business operations.",
      keywords: ["404 error", "page not found", "missing page"],
      noindex: true,
    },
    500: {
      title: "Server Error - 500 Error",
      description: "We're experiencing technical difficulties. Please try again later or contact support if the problem persists.",
      keywords: ["500 error", "server error", "technical difficulties"],
      noindex: true,
    }
  }
};

/**
 * Structured data configurations
 */
export const structuredDataConfigs = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteConfig.name,
    url: siteConfig.url,
    logo: `${siteConfig.url}/assets/images/logo.png`,
    description: siteConfig.description,
    sameAs: [
      siteConfig.links.twitter,
      siteConfig.links.linkedin,
      siteConfig.links.facebook,
      siteConfig.links.github
    ].filter(Boolean),
    contactPoint: [
      {
        '@type': 'ContactPoint',
        contactType: 'customer service',
        email: '<EMAIL>'
      }
    ]
  },
  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteConfig.name,
    url: siteConfig.url,
    description: siteConfig.description,
    potentialAction: [
      {
        '@type': 'SearchAction',
        target: `${siteConfig.url}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    ]
  }
};

/**
 * Environment-specific configurations
 */
export const environmentConfig = {
  development: {
    robots: {
      index: false,
      follow: false,
    },
    verification: {
      // No verification in development
    }
  },
  staging: {
    robots: {
      index: false,
      follow: false,
    },
    verification: {
      // Staging verification codes
    }
  },
  production: {
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large' as const,
        'max-snippet': -1,
      },
    },
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
    }
  }
};

/**
 * Get environment-specific configuration
 */
export function getEnvironmentConfig() {
  const env = process.env.NODE_ENV as keyof typeof environmentConfig;
  return environmentConfig[env] || environmentConfig.development;
}
