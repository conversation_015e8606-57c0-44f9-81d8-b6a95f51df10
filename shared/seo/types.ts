// SEO Types and Interfaces for Server-Side Rendering
// import { Metadata } from 'next';

/**
 * Base SEO configuration interface
 */
export interface BaseSEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
}

/**
 * Open Graph configuration interface
 */
export interface OpenGraphConfig {
  title?: string;
  description?: string;
  type?: 'website' | 'article' | 'profile' | 'book' | 'music' | 'video';
  url?: string;
  siteName?: string;
  images?: Array<{
    url: string;
    width?: number;
    height?: number;
    alt?: string;
  }>;
  locale?: string;
  alternateLocale?: string[];
}

/**
 * Twitter Card configuration interface
 */
export interface TwitterConfig {
  card?: 'summary' | 'summary_large_image' | 'app' | 'player';
  site?: string;
  creator?: string;
  title?: string;
  description?: string;
  images?: string | string[];
}

/**
 * Comprehensive SEO configuration interface
 */
export interface SEOConfig extends BaseSEOConfig {
  openGraph?: OpenGraphConfig;
  twitter?: TwitterConfig;
  alternates?: {
    canonical?: string;
    languages?: Record<string, string>;
  };
  robots?: {
    index?: boolean;
    follow?: boolean;
    googleBot?: {
      index?: boolean;
      follow?: boolean;
      'max-video-preview'?: number;
      'max-image-preview'?: 'none' | 'standard' | 'large';
      'max-snippet'?: number;
    };
  };
  verification?: {
    google?: string;
    yandex?: string;
    yahoo?: string;
    other?: Record<string, string>;
  };
  icons?: {
    icon?: string | string[];
    shortcut?: string;
    apple?: string | string[];
    other?: Array<{
      rel: string;
      url: string;
    }>;
  };
  manifest?: string;
  themeColor?: string;
  colorScheme?: 'light' | 'dark' | 'light dark';
  viewport?: string;
  category?: string;
  classification?: string;
  creator?: string;
  publisher?: string;
  formatDetection?: {
    email?: boolean;
    address?: boolean;
    telephone?: boolean;
  };
  metadataBase?: URL;
  bookmarks?: string[];
  appLinks?: {
    ios?: {
      url?: string;
      app_store_id?: string;
      app_name?: string;
    };
    android?: {
      package?: string;
      url?: string;
      class?: string;
      app_name?: string;
    };
    web?: {
      url?: string;
      should_fallback?: boolean;
    };
  };
  archives?: string[];
  assets?: string[];
  generator?: string;
  applicationName?: string;
  referrer?: 'origin' | 'no-referrer' | 'no-referrer-when-downgrade' | 'origin-when-cross-origin' | 'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url';
  authors?: Array<{ name: string; url?: string }>;
  other?: Record<string, string | number | Array<string | number>>;
}

/**
 * Page-specific SEO data interface
 */
export interface PageSEOData {
  title: string;
  description: string;
  keywords?: string[];
  path: string;
  image?: string;
  type?: 'website' | 'article' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
}

/**
 * Dynamic metadata generation parameters
 */
export interface MetadataParams {
  params: Record<string, string>;
  searchParams: Record<string, string | string[] | undefined>;
}

/**
 * Structured data types for JSON-LD
 */
export interface StructuredDataConfig {
  '@context': string;
  '@type': string;
  [key: string]: any;
}

/**
 * Organization structured data
 */
export interface OrganizationStructuredData extends StructuredDataConfig {
  '@type': 'Organization';
  name: string;
  url: string;
  logo?: string;
  description?: string;
  contactPoint?: Array<{
    '@type': 'ContactPoint';
    telephone?: string;
    contactType?: string;
    email?: string;
  }>;
  sameAs?: string[];
  address?: {
    '@type': 'PostalAddress';
    streetAddress?: string;
    addressLocality?: string;
    addressRegion?: string;
    postalCode?: string;
    addressCountry?: string;
  };
}

/**
 * Website structured data
 */
export interface WebsiteStructuredData extends StructuredDataConfig {
  '@type': 'WebSite';
  name: string;
  url: string;
  description?: string;
  potentialAction?: Array<{
    '@type': 'SearchAction';
    target: string;
    'query-input': string;
  }>;
}

/**
 * Article structured data
 */
export interface ArticleStructuredData extends StructuredDataConfig {
  '@type': 'Article';
  headline: string;
  description?: string;
  image?: string | string[];
  datePublished?: string;
  dateModified?: string;
  author?: {
    '@type': 'Person';
    name: string;
    url?: string;
  };
  publisher?: {
    '@type': 'Organization';
    name: string;
    logo?: {
      '@type': 'ImageObject';
      url: string;
    };
  };
  mainEntityOfPage?: {
    '@type': 'WebPage';
    '@id': string;
  };
}

/**
 * Breadcrumb structured data
 */
export interface BreadcrumbStructuredData extends StructuredDataConfig {
  '@type': 'BreadcrumbList';
  itemListElement: Array<{
    '@type': 'ListItem';
    position: number;
    name: string;
    item?: string;
  }>;
}

/**
 * FAQ structured data
 */
export interface FAQStructuredData extends StructuredDataConfig {
  '@type': 'FAQPage';
  mainEntity: Array<{
    '@type': 'Question';
    name: string;
    acceptedAnswer: {
      '@type': 'Answer';
      text: string;
    };
  }>;
}

/**
 * Product structured data
 */
export interface ProductStructuredData extends StructuredDataConfig {
  '@type': 'Product';
  name: string;
  description?: string;
  image?: string | string[];
  brand?: {
    '@type': 'Brand';
    name: string;
  };
  offers?: {
    '@type': 'Offer';
    price?: string;
    priceCurrency?: string;
    availability?: string;
    url?: string;
  };
  aggregateRating?: {
    '@type': 'AggregateRating';
    ratingValue: number;
    reviewCount: number;
  };
}

/**
 * Local Business structured data
 */
export interface LocalBusinessStructuredData extends StructuredDataConfig {
  '@type': 'LocalBusiness';
  name: string;
  description?: string;
  url?: string;
  telephone?: string;
  address?: {
    '@type': 'PostalAddress';
    streetAddress?: string;
    addressLocality?: string;
    addressRegion?: string;
    postalCode?: string;
    addressCountry?: string;
  };
  geo?: {
    '@type': 'GeoCoordinates';
    latitude: number;
    longitude: number;
  };
  openingHours?: string[];
  priceRange?: string;
}

/**
 * Union type for all structured data types
 */
export type AnyStructuredData = 
  | OrganizationStructuredData
  | WebsiteStructuredData
  | ArticleStructuredData
  | BreadcrumbStructuredData
  | FAQStructuredData
  | ProductStructuredData
  | LocalBusinessStructuredData
  | StructuredDataConfig;

/**
 * Site configuration interface
 */
export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  ogImage: string;
  links: {
    twitter?: string;
    github?: string;
    linkedin?: string;
    facebook?: string;
  };
  creator: {
    name: string;
    url?: string;
    twitter?: string;
  };
  keywords: string[];
  authors: Array<{
    name: string;
    url?: string;
  }>;
  themeColor: string;
  locale: string;
  alternateLocales?: string[];
}
