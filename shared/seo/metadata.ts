// Server-side metadata generation utilities for Next.js App Router
import { Metadata } from 'next';
import { siteConfig, defaultSEOConfig, pageConfigs, getEnvironmentConfig } from './config';
import { SEOConfig, PageSEOData } from './types';

/**
 * Generate metadata for a page with SEO optimization
 * This function creates server-side metadata that's rendered before the page loads
 */
export function generatePageMetadata(config: Partial<SEOConfig>): Metadata {
  const envConfig = getEnvironmentConfig();

  const title = config.title
    ? `${config.title} | ${siteConfig.name}`
    : siteConfig.name;

  const description = config.description || siteConfig.description;
  const canonical = config.canonical || config.alternates?.canonical;
  const keywords = config.keywords || siteConfig.keywords;

  // Merge robots configuration with environment-specific settings
  const robots = {
    ...defaultSEOConfig.robots,
    ...envConfig.robots,
    ...config.robots,
    index: config.noindex ? false : (config.robots?.index ?? envConfig.robots?.index ?? true),
    follow: config.nofollow ? false : (config.robots?.follow ?? envConfig.robots?.follow ?? true),
  };

  const metadata: Metadata = {
    title,
    description,
    keywords: Array.isArray(keywords) ? keywords.join(', ') : keywords,
    authors: config.authors || defaultSEOConfig.authors,
    creator: config.creator || defaultSEOConfig.creator,
    publisher: config.publisher || defaultSEOConfig.publisher,
    formatDetection: config.formatDetection || defaultSEOConfig.formatDetection,
    category: config.category || defaultSEOConfig.category,
    classification: config.classification || defaultSEOConfig.classification,
    referrer: config.referrer || defaultSEOConfig.referrer,
    generator: config.generator || defaultSEOConfig.generator,
    applicationName: config.applicationName || defaultSEOConfig.applicationName,
    metadataBase: config.metadataBase || defaultSEOConfig.metadataBase,

    // Open Graph
    openGraph: {
      ...defaultSEOConfig.openGraph,
      ...config.openGraph,
      title: config.openGraph?.title || title,
      description: config.openGraph?.description || description,
      url: config.openGraph?.url || canonical,
      images: config.openGraph?.images || defaultSEOConfig.openGraph?.images,
    },

    // Twitter
    twitter: {
      ...defaultSEOConfig.twitter,
      ...config.twitter,
      title: config.twitter?.title || title,
      description: config.twitter?.description || description,
    },

    // Robots
    robots,

    // Alternates
    alternates: {
      canonical,
      ...config.alternates,
    },

    // Icons
    icons: config.icons || defaultSEOConfig.icons,

    // Manifest
    manifest: config.manifest || defaultSEOConfig.manifest,

    // Note: themeColor, colorScheme, and viewport should be in viewport export, not metadata

    // Verification
    verification: {
      ...defaultSEOConfig.verification,
      ...envConfig.verification,
      ...config.verification,
    },

    // App Links - temporarily disabled due to type issues
    // ...(config.appLinks && {
    //   appLinks: config.appLinks
    // }),

    // Other metadata
    other: config.other,
  };

  // Remove undefined values
  return Object.fromEntries(
    Object.entries(metadata).filter(([_, value]) => value !== undefined)
  ) as Metadata;
}

/**
 * Generate metadata for dynamic pages (e.g., user details, posts)
 */
export function generateDynamicMetadata(
  baseConfig: Partial<SEOConfig>,
  dynamicData: Partial<PageSEOData>
): Metadata {
  const title = dynamicData.title || baseConfig.title;
  const description = dynamicData.description || baseConfig.description;
  const canonical = dynamicData.path
    ? `${siteConfig.url}${dynamicData.path}`
    : baseConfig.canonical;

  const config: Partial<SEOConfig> = {
    ...baseConfig,
    title,
    description,
    canonical,
    keywords: dynamicData.keywords || baseConfig.keywords,
    openGraph: {
      ...baseConfig.openGraph,
      title,
      description,
      url: canonical,
      type: dynamicData.type || baseConfig.openGraph?.type || 'website',
      images: dynamicData.image ? [
        {
          url: dynamicData.image,
          width: 1200,
          height: 630,
          alt: title,
        }
      ] : baseConfig.openGraph?.images,
      ...(dynamicData.publishedTime && { publishedTime: dynamicData.publishedTime }),
      ...(dynamicData.modifiedTime && { modifiedTime: dynamicData.modifiedTime }),
      ...(dynamicData.author && { authors: [dynamicData.author] }),
      ...(dynamicData.section && { section: dynamicData.section }),
      ...(dynamicData.tags && { tags: dynamicData.tags }),
    } as any,
    twitter: {
      ...baseConfig.twitter,
      title,
      description,
      images: dynamicData.image ? [dynamicData.image] : baseConfig.twitter?.images,
    },
    alternates: {
      canonical,
      ...baseConfig.alternates,
    },
  };

  return generatePageMetadata(config);
}

/**
 * Generate metadata for user management pages
 */
export function generateUserManagementMetadata(
  pageType: 'list' | 'details' | 'create' | 'edit',
  dynamicData?: Partial<PageSEOData>
): Metadata {
  const configs = {
    list: pageConfigs.userManagement,
    details: pageConfigs.userDetails,
    create: pageConfigs.createUser,
    edit: pageConfigs.editUser,
  };

  const baseConfig = configs[pageType];

  if (dynamicData) {
    return generateDynamicMetadata(baseConfig, dynamicData);
  }

  return generatePageMetadata(baseConfig);
}

/**
 * Generate metadata for authentication pages
 */
export function generateAuthMetadata(
  pageType: 'signIn' | 'signUp' | 'forgotPassword'
): Metadata {
  const config = pageConfigs.authentication[pageType];
  return generatePageMetadata(config);
}

/**
 * Generate metadata for error pages
 */
export function generateErrorMetadata(
  errorType: '401' | '404' | '500'
): Metadata {
  const config = pageConfigs.errors[errorType];
  return generatePageMetadata(config);
}

/**
 * Generate metadata for dashboard/home page
 */
export function generateDashboardMetadata(): Metadata {
  return generatePageMetadata(pageConfigs.home);
}

/**
 * Generate metadata for financial report pages
 */
export function generateFinancialReportMetadata(
  userId?: string,
  userName?: string
): Metadata {
  const baseConfig = pageConfigs.financialReport;

  if (userId && userName) {
    const dynamicData: Partial<PageSEOData> = {
      title: `Financial Report - ${userName}`,
      description: `Comprehensive financial report and analytics for ${userName} including transaction history, balance tracking, and financial insights.`,
      path: `/user-management/details/${userId}/financial-report`,
    };

    return generateDynamicMetadata(baseConfig, dynamicData);
  }

  return generatePageMetadata(baseConfig);
}

/**
 * Utility function to create canonical URL
 */
export function createCanonicalUrl(path: string): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${siteConfig.url}${cleanPath}`;
}

/**
 * Utility function to generate breadcrumb metadata
 */
export function generateBreadcrumbMetadata(
  breadcrumbs: Array<{ name: string; href?: string }>
): Record<string, any> {
  return {
    'breadcrumb': breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.href ? `${siteConfig.url}${crumb.href}` : undefined,
    })),
  };
}

/**
 * Generate metadata with structured data
 */
export function generateMetadataWithStructuredData(
  config: Partial<SEOConfig>,
  structuredData?: Record<string, any>
): Metadata {
  const metadata = generatePageMetadata(config);

  if (structuredData) {
    const filteredOther = metadata.other ?
      Object.fromEntries(
        Object.entries(metadata.other).filter(([_, value]) => value !== undefined)
      ) : {};

    metadata.other = {
      ...filteredOther,
      'structured-data': JSON.stringify(structuredData),
    };
  }

  return metadata;
}

/**
 * Generate metadata for API-driven pages
 */
export async function generateApiDrivenMetadata(
  baseConfig: Partial<SEOConfig>,
  apiCall: () => Promise<any>
): Promise<Metadata> {
  try {
    const data = await apiCall();

    const dynamicData: Partial<PageSEOData> = {
      title: data.title || data.name,
      description: data.description || data.summary,
      image: data.image || data.thumbnail,
      publishedTime: data.createdAt,
      modifiedTime: data.updatedAt,
      author: data.author?.name,
    };

    return generateDynamicMetadata(baseConfig, dynamicData);
  } catch {
    // console.error('Failed to generate API-driven metadata:', error);
    return generatePageMetadata(baseConfig);
  }
}

/**
 * Generate metadata for paginated content
 */
export function generatePaginatedMetadata(
  baseConfig: Partial<SEOConfig>,
  currentPage: number,
  totalPages: number,
  basePath: string
): Metadata {
  const title = currentPage > 1
    ? `${baseConfig.title} - Page ${currentPage}`
    : baseConfig.title;

  const canonical = currentPage > 1
    ? `${siteConfig.url}${basePath}?page=${currentPage}`
    : `${siteConfig.url}${basePath}`;

  const config: Partial<SEOConfig> = {
    ...baseConfig,
    title,
    canonical,
    alternates: {
      canonical,
      ...(currentPage > 1 && {
        prev: currentPage > 2
          ? `${siteConfig.url}${basePath}?page=${currentPage - 1}`
          : `${siteConfig.url}${basePath}`,
      }),
      ...(currentPage < totalPages && {
        next: `${siteConfig.url}${basePath}?page=${currentPage + 1}`,
      }),
    },
  };

  return generatePageMetadata(config);
}
