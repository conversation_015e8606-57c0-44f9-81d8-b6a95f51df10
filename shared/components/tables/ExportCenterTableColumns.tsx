// shared/components/tables/ExportCenterTableColumns.tsx - Table columns for Export Center

import React from 'react';
import { ColumnDef, Row } from '@tanstack/react-table';
import {
  EXPORT_STATUS_COLORS,
  EXPORT_STATUS_ICONS,
  EXPORT_TYPE_DISPLAY_NAMES
} from '@/shared/types/export-types';
import { TransformedExportCenterItem } from '@/shared/hooks/business/useExportCenter';

interface ExportCenterTableColumnsProps {
  // No longer need onDownload function - using direct links
}

export const getExportCenterTableColumns = (
  _props?: ExportCenterTableColumnsProps
): ColumnDef<TransformedExportCenterItem>[] => [
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }: { row: Row<TransformedExportCenterItem> }) => {
        const id = row.getValue('id') as string;
        return (
          <div className="font-rubik text-white text-sm font-medium">
            #{id}
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }: { row: Row<TransformedExportCenterItem> }) => {
        const type = row.getValue('type') as keyof typeof EXPORT_TYPE_DISPLAY_NAMES;
        const recordModule = row.original.module;

        // Determine display name based on type and module
        let displayName: string = EXPORT_TYPE_DISPLAY_NAMES[type] || type;

        // Special case for financial reports which use casino_transactions_db type
        if (type === 'casino_transactions_db' && recordModule === 'financial_report') {
          displayName = 'Financial Report';
        }

        return (
          <div className="font-rubik text-text-secondary text-sm">
            {displayName}
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Date',
      cell: ({ row }: { row: Row<TransformedExportCenterItem> }) => {
        const createdAt = row.getValue('createdAt') as string;
        const date = new Date(createdAt);

        return (
          <div className="font-rubik text-text-secondary text-sm">
            <div>{date.toLocaleDateString()}</div>
            <div className="text-xs text-text-muted">
              {date.toLocaleTimeString()}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }: { row: Row<TransformedExportCenterItem> }) => {
        const status = row.getValue('status') as keyof typeof EXPORT_STATUS_COLORS;
        const colorClass = EXPORT_STATUS_COLORS[status] || EXPORT_STATUS_COLORS.pending;
        const iconClass = EXPORT_STATUS_ICONS[status] || EXPORT_STATUS_ICONS.pending;

        return (
          <div className={`${colorClass} border font-rubik text-xs px-2 py-1 rounded-md flex items-center gap-1.5 w-fit`}>
            <i className={`${iconClass} text-xs ${status === 'processing' ? 'animate-spin' : ''}`}></i>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </div>
        );
      },
    },
    {
      accessorKey: 'fullDownloadUrl',
      header: 'Download',
      cell: ({ row }: { row: Row<TransformedExportCenterItem> }) => {
        const item = row.original;
        const isCompleted = item.status === 'completed';
        const hasDownloadUrl = !!item.fullDownloadUrl;

        // Only show download if completed and has URL
        if (!isCompleted || !hasDownloadUrl) {
          return null; // Hide download button entirely if not available
        }

        return (
          <div className="flex items-center justify-center">
            <a
              href={item.fullDownloadUrl}
              download
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gradient-to-r from-[var(--golden)] to-[#8A5911] text-white px-2 py-1 rounded text-xs font-medium hover:from-[#E3B84B] hover:to-[#8A5911] transition-all duration-200 flex items-center gap-1 font-rubik"
            >
              <i className="ri-download-line text-xs"></i>
              Download
            </a>
          </div>
        );
      },
    },
  ];

// Export the function for use in components
export default getExportCenterTableColumns;
