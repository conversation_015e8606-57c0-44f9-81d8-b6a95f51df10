// shared/components/tables/BetReportTableColumns.tsx
import { CopyToClipboard, SpkTableColumn, CurrencyDisplay } from "@/shared/UI/components";
import { StatusBadge } from "@/shared/UI/components";
import { BetReportData } from "@/shared/types/report-types";

// Helper function to format date
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Status styling is now handled by the StatusBadge component

interface BetReportTableColumnsOptions {
  onSettleBet?: (betId: string, record: BetReportData) => void;
}

export const getBetReportTableColumns = (options: BetReportTableColumnsOptions = {}): SpkTableColumn[] => {
  const { onSettleBet } = options;
  return [
    {
      key: "userName",
      title: "Username",
      sortable: false,
      width: "140px",
      render: (value, record: BetReportData) => (
        <div className="flex items-center gap-2">
          <span className="font-medium text-text-muted text-sm truncate">
            {value || record.userName || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "createdAt",
      title: "Date & Time",
      sortable: false,
      width: "160px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <span className="text-text-muted truncate block">
            {formatDate(value || record.createdAt)}
          </span>
        </div>
      )
    },
    {
      key: "marketId",
      title: "Market ID",
      sortable: false,
      width: "120px",
      render: (value, record: BetReportData) => {
        const marketId = value || record.marketId || "N/A";
        return (
          <div className="flex items-center gap-2">
            <span className="font-mono text-text-muted text-sm truncate">
              {marketId}
            </span>
            {marketId !== "N/A" && (
              <CopyToClipboard
                text={marketId}
                iconSize={14}
                className="opacity-60 hover:opacity-100"
              />
            )}
          </div>
        );
      }
    },
    {
      key: "betId",
      title: "Bet ID",
      sortable: false,
      width: "140px",
      render: (value, record: BetReportData) => {
        const betId = value || record.betId || "N/A";
        return (
          <div className="flex items-center gap-2">
            <span className="font-mono text-sm text-text-muted truncate">
              {betId}
            </span>
            {betId !== "N/A" && (
              <CopyToClipboard
                text={betId}
                iconSize={14}
                className="opacity-60 hover:opacity-100"
              />
            )}
          </div>
        );
      }
    },
    {
      key: "marketName",
      title: "Market Name",
      sortable: false,
      width: "180px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <span className="text-text-muted truncate block">
            {value || record.marketName || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "betType",
      title: "Bet Type",
      sortable: false,
      width: "100px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <span className="text-text-muted capitalize truncate block">
            {value || record.betType || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "odds",
      title: "Odds",
      sortable: false,
      width: "80px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <span className="text-text-muted truncate block">
            {value || record.odds || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "betAmount",
      title: "Bet Amount",
      sortable: false,
      width: "120px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <CurrencyDisplay
            amount={value || record.betAmount}
            context="table"
            size={14}
            amountClassName="font-medium text-text-muted truncate block"
            gap="sm"
          />
        </div>
      )
    },
    {
      key: "winAmount",
      title: "Win Amount",
      sortable: false,
      width: "120px",
      render: (value, record: BetReportData) => (
        <div className="text-sm text-green-600">
          <CurrencyDisplay
            amount={value || record.winAmount}
            context="table"
            size={14}
            amountClassName="font-medium truncate block"
            gap="sm"
          />
        </div>
      )
    },
    {
      key: "status",
      title: "Status",
      sortable: false,
      width: "120px",
      render: (value, record: BetReportData) => {
        const status = value || record.status || "unknown";
        const normalizedStatus = status.toLowerCase();

        // For bet reports, show Win/Loss based on actual bet outcome
        let displayStatus = status;
        if (normalizedStatus === 'won') {
          displayStatus = 'Win';
        } else if (normalizedStatus === 'lost') {
          displayStatus = 'Loss';
        }

        return <StatusBadge status={displayStatus} />;
      }
    },
    {
      key: "settlement",
      title: "Settlement",
      sortable: false,
      width: "140px",
      render: (_, record: BetReportData) => {
        // Helper function to map numeric payoutStatus to string
        const mapPayoutStatus = (payoutStatus: any): string | undefined => {
          if (typeof payoutStatus === 'number') {
            switch (payoutStatus) {
              case 1: return 'settled';
              case 2: return 'rejected';
              case 3: return 'unsettled';
              default: return undefined;
            }
          }
          return typeof payoutStatus === 'string' ? payoutStatus.toLowerCase() : undefined;
        };

        // Use payoutSettlementStatus if available, otherwise fallback to status-based logic
        const payoutStatus = mapPayoutStatus(record.payoutStatus);
        const status = record.status?.toLowerCase() || "unknown";

        // Check if payout is settled or rejected
        if (payoutStatus === 'settled' || payoutStatus === 'rejected') {
          const displayText = payoutStatus === 'settled' ? 'Settled' : 'Rejected';
          const isSettled = payoutStatus === 'settled';

          return (
            <div className="text-sm">
              <span
                className="inline-flex items-center justify-between font-rubik font-normal text-sm leading-[100%]"
                style={{
                  background: isSettled ? '#83838333' : '#5B2424', // Gray for settled, red for rejected
                  color: isSettled ? '#999999' : '#FB3D32', // Gray text for settled, red text for rejected
                  padding: '8px 15px',
                  borderRadius: '4px',
                  gap: '8px',
                  minWidth: '100px',
                  width: "120px"
                }}
              >
                <span>{displayText}</span>
                <i
                  className={`ri-${isSettled ? 'check' : 'close'}-line`}
                  style={{ fontSize: '14px' }}
                />
              </span>
            </div>
          );
        }

        // Fallback to old logic for backward compatibility
        const isSettled = status === 'settled' || status === 'won' || status === 'lost';
        if (isSettled && !payoutStatus) {
          // Settled bets styling (legacy)
          return (
            <div className="text-sm">
              <span
                className="inline-flex items-center justify-between font-rubik font-normal text-sm leading-[100%]"
                style={{
                  background: '#83838333',
                  color: '#999999', // text-muted color from tailwind config
                  padding: '8px 15px',
                  borderRadius: '4px',
                  gap: '8px',
                  minWidth: '120px',
                }}
              >
                <span>Settled</span>
                <i
                  className="ri-check-line"
                  style={{ fontSize: '14px' }}
                />
              </span>
            </div>
          );
        }

        // Show Mark as Settled button for pending or unsettled bets
        return (
          <div className="text-sm">
            <button
              onClick={() => onSettleBet?.(record.betId, record)}
              className="inline-flex items-center text-white font-rubik font-normal text-sm leading-[100%] transition-all duration-200 hover:shadow-lg"
              style={{
                background: 'linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)',
                boxShadow: '4px 4px 8px 0px #FFFFFF40 inset, -4px -4px 8px 0px #916600 inset',
                padding: '8px 15px',
                borderRadius: '4px',
                gap: '8px',
                width: "120px"
              }}
            >
              Mark as Settled
            </button>
          </div>
        );
      }
    }
  ];
};
