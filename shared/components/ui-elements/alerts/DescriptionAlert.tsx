import React, { Fragment } from 'react';
import { AlertProps } from '../../../types/ui-element-types';

const DescriptionAlert: React.FC<AlertProps> = ({
  variant = 'warning',
  className = '',
  role = 'alert',
  id,
  children,
  icon,
  title
}) => {
  const alertClasses = [
    `bg-${variant}/10 border border-${variant} text-sm text-${variant}/80 rounded-lg p-4`,
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div className={alertClasses} role={role} id={id}>
        <div className="flex">
          {icon && (
            <div className="flex-shrink-0">
              {icon}
            </div>
          )}
          
          <div className={icon ? 'ms-4' : ''}>
            {title && (
              <h3 className="text-sm font-semibold">
                {title}
              </h3>
            )}
            
            <div className={title ? 'mt-1 text-sm' : 'text-sm'}>
              {children}
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default DescriptionAlert;
