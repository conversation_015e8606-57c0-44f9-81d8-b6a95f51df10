import React, { Fragment } from 'react';
import { AlertProps } from '../../../types/ui-element-types';

const ActionsAlert: React.FC<AlertProps> = ({
  variant = 'info',
  className = '',
  role = 'alert',
  id,
  children,
  icon,
  title,
  actions
}) => {
  const alertClasses = [
    `bg-${variant}/10 border border-${variant}/20 text-${variant}/80 rounded-lg p-4`,
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div className={alertClasses} role={role} id={id}>
        <div className="flex">
          {icon && (
            <div className="flex-shrink-0">
              {icon}
            </div>
          )}
          
          <div className={icon ? 'ms-3' : ''}>
            {title && (
              <h6 className="font-semibold">
                {title}
              </h6>
            )}
            
            <div className={title ? 'mt-2 text-sm text-gray-600 dark:text-gray-400' : 'text-sm text-gray-600 dark:text-gray-400'}>
              {children}
            </div>
            
            {actions && (
              <div className="mt-4">
                <div className="flex space-x-3">
                  {actions}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default ActionsAlert;
