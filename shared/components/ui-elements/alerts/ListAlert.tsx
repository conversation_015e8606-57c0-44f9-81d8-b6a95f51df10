import React, { Fragment } from 'react';
import { AlertProps } from '../../../types/ui-element-types';

interface ListAlertProps extends AlertProps {
  items?: string[];
}

const ListAlert: React.FC<ListAlertProps> = ({
  variant = 'danger',
  className = '',
  role = 'alert',
  id,
  children,
  icon,
  title,
  items = []
}) => {
  const alertClasses = [
    `bg-${variant}/20 border border-${variant}/20 text-sm text-${variant}/80 rounded-lg p-4`,
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div className={alertClasses} role={role} id={id}>
        <div className="flex">
          {icon && (
            <div className="flex-shrink-0">
              {icon}
            </div>
          )}
          
          <div className={icon ? 'ms-4' : ''}>
            {title && (
              <h3 className="text-sm font-semibold">
                {title}
              </h3>
            )}
            
            {children && (
              <div className={title ? 'mt-2 text-sm text-red-700 dark:text-red-400' : 'text-sm text-red-700 dark:text-red-400'}>
                {children}
              </div>
            )}
            
            {items.length > 0 && (
              <div className="mt-2 text-sm text-red-700 dark:text-red-400">
                <ul className="list-disc space-y-1 ps-5">
                  {items.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default ListAlert;
