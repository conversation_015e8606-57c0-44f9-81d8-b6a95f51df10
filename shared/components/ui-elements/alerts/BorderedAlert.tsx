import React, { Fragment } from 'react';
import { AlertProps } from '../../../types/ui-element-types';

const BorderedAlert: React.FC<AlertProps> = ({
  variant = 'success',
  className = '',
  role = 'alert',
  id,
  children,
  icon,
  title,
  borderPosition = 'top'
}) => {
  const getBorderClasses = () => {
    const borderClass = borderPosition === 'top' ? 'border-t-2' : 
                       borderPosition === 'bottom' ? 'border-b-2' :
                       borderPosition === 'left' ? 'border-s-4' :
                       borderPosition === 'right' ? 'border-e-4' : 'border-t-2';
    
    return `bg-${variant}/20 ${borderClass} border-${variant} rounded-lg p-4 dark:bg-${variant}/20`;
  };

  const alertClasses = [
    getBorderClasses(),
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div className={alertClasses} role={role} id={id}>
        <div className="flex">
          {icon && (
            <div className="flex-shrink-0">
              {icon}
            </div>
          )}
          
          <div className={icon ? 'ms-3' : ''}>
            {title && (
              <h6 className="text-gray-800 font-semibold dark:text-white">
                {title}
              </h6>
            )}
            
            <div className={title ? 'mt-1 text-sm text-gray-700 dark:text-gray-400' : 'text-sm text-gray-700 dark:text-gray-400'}>
              {children}
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default BorderedAlert;
