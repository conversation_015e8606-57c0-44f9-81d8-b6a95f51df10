import React, { Fragment } from 'react';
import { BadgeProps } from '../../../types/ui-element-types';

const BasicBadge: React.FC<BadgeProps> = ({
  variant = 'primary',
  children,
  className = '',
  id,
  onClick,
  pill = false,
  size = 'md'
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'text-xs px-2 py-1';
      case 'lg':
        return 'text-base px-3 py-2';
      default:
        return 'text-sm px-2.5 py-1.5';
    }
  };

  const badgeClasses = [
    'badge',
    `bg-${variant}`,
    pill ? 'rounded-full' : 'rounded',
    getSizeClass(),
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <span
        className={badgeClasses}
        id={id}
        onClick={onClick}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick ? 0 : undefined}
      >
        {children}
      </span>
    </Fragment>
  );
};

export default BasicBadge;
