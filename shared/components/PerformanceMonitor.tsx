// shared/components/PerformanceMonitor.tsx - Client-side performance monitoring component
"use client";

import React, { useEffect } from 'react';
import { initPerformanceMonitoring, performanceUtils } from '@/shared/utils/performance';

interface PerformanceMonitorProps {
	/**
	 * Whether to enable performance monitoring
	 * @default true in production, false in development
	 */
	enabled?: boolean;
	
	/**
	 * Whether to preload critical resources
	 * @default true
	 */
	preloadCriticalResources?: boolean;
	
	/**
	 * Whether to enable lazy loading for images
	 * @default true
	 */
	enableLazyLoading?: boolean;
}

/**
 * Performance monitoring component that should be included in the root layout
 * Handles Core Web Vitals tracking, resource preloading, and performance optimizations
 */
export default function PerformanceMonitor({
	enabled = process.env.NODE_ENV === 'production',
	preloadCriticalResources = true,
	enableLazyLoading = true,
}: PerformanceMonitorProps) {
	useEffect(() => {
		if (!enabled || typeof window === 'undefined') return;

		// Initialize performance monitoring
		initPerformanceMonitoring();

		// Preload critical resources
		if (preloadCriticalResources) {
			// Preload critical CSS
			performanceUtils.preloadResource('/assets/css/styles.css', 'style', 'text/css');
			
			// Preload critical fonts
			performanceUtils.preloadResource('/assets/fonts/inter.woff2', 'font', 'font/woff2');
			
			// Preload critical images
			performanceUtils.preloadResource('/assets/images/logo.svg', 'image', 'image/svg+xml');
		}

		// Enable lazy loading for images
		if (enableLazyLoading) {
			// Initial lazy loading setup
			performanceUtils.lazyLoadImages();
			
			// Re-run lazy loading when new content is added
			const observer = new MutationObserver(() => {
				performanceUtils.lazyLoadImages();
			});
			
			observer.observe(document.body, {
				childList: true,
				subtree: true,
			});
			
			return () => observer.disconnect();
		}
	}, [enabled, preloadCriticalResources, enableLazyLoading]);

	// This component doesn't render anything
	return null;
}

/**
 * Hook for measuring component render performance
 */
export function useRenderPerformance(componentName: string) {
	useEffect(() => {
		const startTime = performance.now();
		
		return () => {
			performanceUtils.measureRenderTime(componentName, startTime);
		};
	}, [componentName]);
}

/**
 * HOC for measuring component render performance
 */
export function withPerformanceMonitoring<P extends object>(
	WrappedComponent: React.ComponentType<P>,
	componentName?: string
) {
	const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';
	
	const PerformanceWrappedComponent = (props: P) => {
		useRenderPerformance(displayName);
		return <WrappedComponent {...props} />;
	};
	
	PerformanceWrappedComponent.displayName = `withPerformanceMonitoring(${displayName})`;
	
	return PerformanceWrappedComponent;
}
