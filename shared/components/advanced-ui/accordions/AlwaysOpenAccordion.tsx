import React, { Fragment } from 'react';
import { AccordionProps } from '../../../types/accordion-types';

const AlwaysOpenAccordion: React.FC<AccordionProps> = ({
  items,
  SvgIcon = true,
  Titletext = 'after',
  Toggleclass = 'hs-accordion-active:text-primary py-3 inline-flex items-center gap-x-3 w-full font-semibold text-start text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none dark:hs-accordion-active:text-blue-500 dark:text-gray-200 dark:hover:text-gray-400 dark:focus:outline-none dark:focus:text-gray-400',
  Groupclass = ''
}) => {
  return (
    <Fragment>
      <div className={`hs-accordion-group ${Groupclass}`} data-hs-accordion-always-open>
        {items.map((item, index) => (
          <div key={index} className={`hs-accordion ${item.Customclass}`} id={item.Mainid}>
            <button
              className={`hs-accordion-toggle ${Toggleclass}`}
              aria-controls={item.Id}
            >
              {SvgIcon && Titletext === 'before' && (
                <>
                  <svg
                    className="hs-accordion-active:hidden block size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    {item.Svgcontent1}
                  </svg>
                  <svg
                    className="hs-accordion-active:block hidden size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    {item.Svgcontent2}
                  </svg>
                </>
              )}
              
              {item.title}
              
              {SvgIcon && Titletext === 'after' && (
                <>
                  <svg
                    className="hs-accordion-active:hidden block size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    {item.Svgcontent1}
                  </svg>
                  <svg
                    className="hs-accordion-active:block hidden size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    {item.Svgcontent2}
                  </svg>
                </>
              )}
            </button>
            
            <div
              id={item.Id}
              className={`hs-accordion-content w-full overflow-hidden transition-[height] duration-300 ${item.Custombodyclass}`}
              aria-labelledby={item.Mainid}
            >
              {item.content}
            </div>
          </div>
        ))}
      </div>
    </Fragment>
  );
};

export default AlwaysOpenAccordion;
