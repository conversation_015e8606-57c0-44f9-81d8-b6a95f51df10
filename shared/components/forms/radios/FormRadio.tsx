import React, { Fragment } from 'react';
import { CheckboxRadioProps } from '../../../types/form-types';

const FormRadio: React.FC<CheckboxRadioProps> = ({
  id,
  name,
  value,
  checked = false,
  disabled = false,
  onChange,
  label,
  size = 'md',
  inline = false,
  reverse = false
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'form-check-sm';
      case 'lg':
        return 'form-check-lg';
      default:
        return '';
    }
  };

  const containerClasses = [
    'form-check',
    inline ? 'form-check-inline' : '',
    reverse ? 'form-check-reverse' : '',
    getSizeClass(),
    inline ? 'flex items-center gap-2' : 'flex items-center gap-2'
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div className={containerClasses}>
        <input
          className="form-check-input"
          type="radio"
          id={id}
          name={name}
          value={value}
          checked={checked}
          disabled={disabled}
          onChange={onChange}
        />
        {label && (
          <label className="form-check-label" htmlFor={id}>
            {label}
          </label>
        )}
      </div>
    </Fragment>
  );
};

export default FormRadio;
