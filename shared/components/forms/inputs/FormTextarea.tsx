import React, { Fragment } from 'react';
import { TextareaProps } from '../../../types/form-types';

const FormTextarea: React.FC<TextareaProps> = ({
  id,
  name,
  value,
  placeholder,
  className = '',
  disabled = false,
  readOnly = false,
  required = false,
  rows = 3,
  cols,
  onChange,
  label,
  helpText
}) => {
  const textareaClasses = [
    'form-control',
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      {label && (
        <label htmlFor={id} className="ti-form-label">
          {label}
        </label>
      )}
      
      <textarea
        id={id}
        name={name}
        value={value}
        placeholder={placeholder}
        className={textareaClasses}
        disabled={disabled}
        readOnly={readOnly}
        required={required}
        rows={rows}
        cols={cols}
        onChange={onChange}
      />
      
      {helpText && (
        <div className="form-text">
          {helpText}
        </div>
      )}
    </Fragment>
  );
};

export default FormTextarea;
