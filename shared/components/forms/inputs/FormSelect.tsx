import React, { Fragment } from 'react';
import { SelectProps } from '../../../types/form-types';

const FormSelect: React.FC<SelectProps> = ({
  id,
  name,
  value,
  disabled = false,
  onChange,
  options,
  placeholder,
  className = '',
  size = 'md'
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'form-select-sm';
      case 'lg':
        return 'form-select-lg';
      default:
        return '';
    }
  };

  const selectClasses = [
    'form-select',
    getSizeClass(),
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <select
        id={id}
        name={name}
        value={value}
        disabled={disabled}
        onChange={onChange}
        className={selectClasses}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option, index) => (
          <option
            key={index}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>
    </Fragment>
  );
};

export default FormSelect;
