import React, { Fragment } from 'react';
import { InputProps } from '../../../types/form-types';

const FormInput: React.FC<InputProps> = ({
  type = 'text',
  id,
  name,
  value,
  placeholder,
  className = '',
  disabled = false,
  readOnly = false,
  required = false,
  onChange,
  label,
  helpText,
  size = 'md',
  variant = 'default',
  radius = 'md'
}) => {
  // Build CSS classes based on props
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'form-control-sm';
      case 'lg':
        return 'form-control-lg';
      default:
        return '';
    }
  };

  const getVariantClass = () => {
    switch (variant) {
      case 'dotted':
        return 'border border-border-primary dark:border-border-secondary border-dotted';
      case 'dashed':
        return 'border border-border-primary dark:border-border-secondary border-dashed';
      default:
        return '';
    }
  };

  const getRadiusClass = () => {
    switch (radius) {
      case 'none':
        return '!rounded-none';
      case 'sm':
        return '!rounded-sm';
      case 'lg':
        return '!rounded-lg';
      case 'full':
        return '!rounded-full';
      default:
        return '';
    }
  };

  const inputClasses = [
    'form-control',
    getSizeClass(),
    getVariantClass(),
    getRadiusClass(),
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      {label && (
        <label htmlFor={id} className="ti-form-label">
          {label}
        </label>
      )}
      
      <input
        type={type}
        id={id}
        name={name}
        value={value}
        placeholder={placeholder}
        className={inputClasses}
        disabled={disabled}
        readOnly={readOnly}
        required={required}
        onChange={onChange}
      />
      
      {helpText && (
        <div className="form-text">
          {helpText}
        </div>
      )}
    </Fragment>
  );
};

export default FormInput;
