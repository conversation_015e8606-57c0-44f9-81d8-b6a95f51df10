import React, { Fragment } from 'react';
import { SwitchProps } from '../../../types/form-types';

const FormSwitch: React.FC<SwitchProps> = ({
  id,
  name,
  checked = false,
  disabled = false,
  onChange,
  label,
  labelPosition = 'after',
  size = 'md'
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'ti-switch-sm';
      case 'lg':
        return 'ti-switch-lg';
      default:
        return '';
    }
  };

  const switchClasses = [
    'ti-switch',
    getSizeClass()
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div className="flex items-center">
        {label && labelPosition === 'before' && (
          <label htmlFor={id} className="text-sm text-gray-500 ltr:mr-3 rtl:ml-3 dark:text-white/70">
            {label}
          </label>
        )}
        
        <input
          type="checkbox"
          id={id}
          name={name}
          checked={checked}
          disabled={disabled}
          onChange={onChange}
          className={switchClasses}
        />
        
        {label && labelPosition === 'after' && (
          <label htmlFor={id} className="text-sm text-gray-500 ltr:ml-3 rtl:mr-3 dark:text-white/70">
            {label}
          </label>
        )}
      </div>
    </Fragment>
  );
};

export default FormSwitch;
