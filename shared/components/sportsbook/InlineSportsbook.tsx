// shared/components/sportsbook/InlineSportsbook.tsx
"use client";

import React from "react";
import { useInlineSportsbookLogic } from "@/shared/hooks/business/useInlineSportsbookLogic";
import { InlineSportsbookUI } from "@/shared/UI/components";

interface InlineSportsbookProps {
  className?: string;
}

export const InlineSportsbook: React.FC<InlineSportsbookProps> = ({
  className = ""
}) => {
  // Use custom hook for all business logic
  const {
    isOpen,
    url,
    userName,
    error,
    isIframeLoading,
    iframeError,
    iframeRef,
    handleIframeLoad,
    handleIframeError,
    handleClose,
  } = useInlineSportsbookLogic();

  // Render pure UI component with data from hook
  return (
    <InlineSportsbookUI
      className={className}
      isOpen={isOpen}
      url={url}
      userName={userName}
      error={error}
      isIframeLoading={isIframeLoading}
      iframeError={iframeError}
      iframeRef={iframeRef}
      onIframeLoad={handleIframeLoad}
      onIframeError={handleIframeError}
      onClose={handleClose}
    />
  );
};
