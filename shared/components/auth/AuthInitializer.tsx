// shared/components/auth/AuthInitializer.tsx - Initialize authentication from cookies
'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/shared/stores/authStore';
import { syncAuthFromCookies, hasAuthCookies } from '@/shared/utils/authCookies';

/**
 * Component that initializes authentication state from cookies
 * This ensures that server-side rendered pages have access to auth state
 * and maintains consistency between server and client
 */
export function AuthInitializer() {
  const { setAuth, _hasHydrated } = useAuthStore();

  useEffect(() => {
    // Only run after hydration to avoid SSR/client mismatch
    if (!_hasHydrated) return;

    // Check if we have auth cookies but no auth state
    const { isAuthenticated, token, user } = useAuthStore.getState();
    
    if (!isAuthenticated && hasAuthCookies()) {
      // Sync authentication state from cookies
      const { token: cookieToken, user: cookieUser } = syncAuthFromCookies();
      
      if (cookieToken && cookieUser) {
        // Update auth store with cookie data
        setAuth(cookieToken, cookieUser);
      }
    } else if (isAuthenticated && token && user) {
      // Ensure cookies are set if we have auth state but no cookies
      if (!hasAuthCookies()) {
        setAuth(token, user); // This will set the cookies
      }
    }
  }, [_hasHydrated, setAuth]);

  // This component doesn't render anything
  return null;
}
