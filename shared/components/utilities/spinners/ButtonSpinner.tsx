import React, { Fragment } from 'react';
import { SpinnerProps } from '../../../types/ui-element-types';

interface ButtonSpinnerProps extends SpinnerProps {
  buttonText?: string;
  buttonVariant?: string;
  disabled?: boolean;
}

const ButtonSpinner: React.FC<ButtonSpinnerProps> = ({
  size = 'sm',
  color = 'white',
  className = '',
  label = 'Loading...',
  buttonText = 'Loading...',
  buttonVariant = 'primary',
  disabled = true
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'xs':
        return 'w-3 h-3';
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-4 h-4';
    }
  };

  const spinnerClasses = [
    getSizeClass(),
    `animate-spin border-2 border-${color}/20 border-t-${color}`,
    'rounded-full',
    className
  ].filter(Boolean).join(' ');

  const buttonClasses = [
    'ti-btn',
    `ti-btn-${buttonVariant}`,
    'flex items-center',
    disabled ? 'opacity-50 cursor-not-allowed' : ''
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <button type="button" className={buttonClasses} disabled={disabled}>
        <div className={spinnerClasses} role="status" aria-label={label}>
          <span className="sr-only">{label}</span>
        </div>
        <span className="ms-2">{buttonText}</span>
      </button>
    </Fragment>
  );
};

export default ButtonSpinner;
