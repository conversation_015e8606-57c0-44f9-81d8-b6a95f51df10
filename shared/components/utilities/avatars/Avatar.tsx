import React, { Fragment } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { AvatarProps } from '../../../types/avatar-types';

const Avatar: React.FC<AvatarProps> = ({
  src,
  alt = 'avatar',
  size = 'md',
  shape = 'rounded',
  status,
  badge,
  initials,
  backgroundColor = 'primary',
  className = ''
}) => {
  const getSizeClass = () => {
    return `avatar-${size}`;
  };

  const getShapeClass = () => {
    switch (shape) {
      case 'circle':
        return 'avatar-rounded';
      case 'square':
        return 'avatar-radius-0';
      default:
        return '';
    }
  };

  const getStatusClass = () => {
    if (status) {
      return status;
    }
    return '';
  };

  const avatarClasses = [
    'avatar',
    getSizeClass(),
    getShapeClass(),
    getStatusClass(),
    'me-2',
    className
  ].filter(Boolean).join(' ');

  const renderBadge = () => {
    if (!badge) return null;

    if (badge.icon) {
      return (
        <Link
          aria-label="anchor"
          scroll={false}
          href="#!"
          className={`badge bg-${badge.color} rounded-full text-white avatar-badge`}
        >
          <i className={`fe ${badge.icon} text-[.5rem]`}></i>
        </Link>
      );
    }

    return (
      <span className={`badge bg-${badge.color} rounded-full text-white avatar-badge`}>
        {badge.content}
      </span>
    );
  };

  return (
    <Fragment>
      <span className={avatarClasses}>
        {src ? (
          <Image src={src} alt={alt} width={size === 'xs' ? 20 : size === 'sm' ? 32 : size === 'md' ? 48 : size === 'lg' ? 56 : size === 'xl' ? 64 : 40} height={size === 'xs' ? 20 : size === 'sm' ? 32 : size === 'md' ? 48 : size === 'lg' ? 56 : size === 'xl' ? 64 : 40} />
        ) : initials ? (
          <span className={`text-white bg-${backgroundColor}`}>
            {initials}
          </span>
        ) : (
          <div className={`bg-${backgroundColor} text-white flex items-center justify-center w-full h-full`}>
            {initials || '?'}
          </div>
        )}
        {renderBadge()}
      </span>
    </Fragment>
  );
};

export default Avatar;
