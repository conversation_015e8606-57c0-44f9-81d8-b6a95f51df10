import React, { Fragment } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { AvatarStackType } from '../../../types/avatar-types';

interface AvatarStackProps {
  avatars: (AvatarStackType & { src: string })[];
  maxVisible?: number;
  shape?: 'rounded' | 'circle' | 'square';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  className?: string;
}

const AvatarStack: React.FC<AvatarStackProps> = ({
  avatars,
  maxVisible = 6,
  shape = 'rounded',
  size = 'md',
  className = ''
}) => {
  const getShapeClass = () => {
    switch (shape) {
      case 'circle':
        return 'avatar-rounded';
      case 'square':
        return 'avatar-radius-0';
      default:
        return '';
    }
  };

  const getSizeClass = () => {
    return size !== 'md' ? `avatar-${size}` : '';
  };

  const visibleAvatars = avatars.slice(0, maxVisible);
  const remainingCount = avatars.length - maxVisible;

  const avatarClasses = [
    'avatar',
    getSizeClass(),
    getShapeClass()
  ].filter(Boolean).join(' ');

  const stackClasses = [
    'avatar-list-stacked',
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      <div className={stackClasses}>
        {visibleAvatars.map((avatar, index) => (
          <span key={index} className={avatarClasses}>
            <Image src={avatar.src} alt="Avatar" width={size === 'xs' ? 20 : size === 'sm' ? 32 : size === 'md' ? 48 : size === 'lg' ? 56 : size === 'xl' ? 64 : 40} height={size === 'xs' ? 20 : size === 'sm' ? 32 : size === 'md' ? 48 : size === 'lg' ? 56 : size === 'xl' ? 64 : 40} />
          </span>
        ))}

        {remainingCount > 0 && (
          <Link
            className={`avatar bg-primary text-white ${getShapeClass()}`}
            scroll={false}
            href="#!"
          >
            +{remainingCount}
          </Link>
        )}
      </div>
    </Fragment>
  );
};

export default AvatarStack;
