import React, { Fragment } from 'react';
import { BorderProps } from '../../../types/ui-element-types';

const BorderContainer: React.FC<BorderProps> = ({
  side = 'all',
  color = 'defaultborder',
  width = '1',
  style = 'solid',
  radius = 'md',
  opacity = '100',
  className = '',
  children
}) => {
  const getBorderSideClass = () => {
    switch (side) {
      case 'top':
        return 'border-t';
      case 'right':
        return 'border-e';
      case 'bottom':
        return 'border-b';
      case 'left':
        return 'border-s';
      case 'none':
        return 'border-0';
      default:
        return 'border';
    }
  };

  const getBorderColorClass = () => {
    if (color === 'defaultborder') {
      return 'dark:border-defaultborder/10 border-defaultborder';
    }
    return `!border-${color}`;
  };

  const getBorderWidthClass = () => {
    if (width === '0') return 'border-0';
    if (width === '1') return 'border-1';
    return `border-${width}`;
  };

  const getBorderStyleClass = () => {
    switch (style) {
      case 'dashed':
        return 'border-dashed';
      case 'dotted':
        return 'border-dotted';
      default:
        return '';
    }
  };

  const getBorderRadiusClass = () => {
    switch (radius) {
      case 'none':
        return '!rounded-none';
      case 'sm':
        return 'rounded-sm';
      case 'lg':
        return 'rounded-lg';
      case 'xl':
        return 'rounded-xl';
      case 'full':
        return 'rounded-full';
      default:
        return 'rounded-md';
    }
  };

  const getOpacityClass = () => {
    if (opacity !== '100') {
      return `border-${color}/${opacity}`;
    }
    return '';
  };

  const borderClasses = [
    getBorderSideClass(),
    getBorderColorClass(),
    getBorderWidthClass(),
    getBorderStyleClass(),
    getBorderRadiusClass(),
    getOpacityClass(),
    'border-container',
    className
  ].filter(Boolean).join(' ');

  return (
    <Fragment>
      {children ? (
        <div className={borderClasses}>
          {children}
        </div>
      ) : (
        <span className={borderClasses}></span>
      )}
    </Fragment>
  );
};

export default BorderContainer;
