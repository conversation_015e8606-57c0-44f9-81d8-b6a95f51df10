// shared/components/error-boundaries/SSRErrorBoundary.tsx - Error boundary for SSR failures
'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

/**
 * SSR Error Boundary Component
 * 
 * Gracefully handles SSR failures and hydration mismatches by:
 * - Catching errors during server-side rendering
 * - Providing fallback UI when errors occur
 * - Allowing recovery through client-side rendering
 * - Logging errors for debugging in development
 * 
 * Features:
 * - Automatic error recovery
 * - Custom fallback components
 * - Error reporting and logging
 * - Reset functionality for dynamic content
 */
class SSRErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    this.setState({ errorInfo });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.group('🚨 SSR Error Boundary Caught Error');
      // eslint-disable-next-line no-console
      console.error('Error:', error);
      // eslint-disable-next-line no-console
      console.error('Error Info:', errorInfo);
      // eslint-disable-next-line no-console
      console.error('Component Stack:', errorInfo.componentStack);
      // eslint-disable-next-line no-console
      console.groupEnd();
    }

    // In production, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to error reporting service
      // errorReportingService.captureException(error, { extra: errorInfo });
    }
  }

  componentDidUpdate(prevProps: Props) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    // Reset error state when specified props change
    if (hasError && resetOnPropsChange && resetKeys) {
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => prevProps.resetKeys?.[index] !== key
      );

      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  resetErrorBoundary = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  render() {
    const { hasError, error, errorInfo, errorId } = this.state;
    const { children, fallback } = this.props;

    if (hasError) {
      // Custom fallback UI
      if (fallback) {
        return fallback;
      }

      // Default fallback UI
      return (
        <div className="min-h-[400px] flex items-center justify-center bg-background rounded-lg border border-border-secondary">
          <div className="text-center p-8 max-w-md">
            <div className="mb-4">
              <i className="ri-error-warning-line text-4xl text-red-500"></i>
            </div>
            
            <h3 className="text-lg font-medium text-white mb-2">
              Something went wrong
            </h3>
            
            <p className="text-text-secondary mb-4">
              We encountered an error while loading this page. The page will automatically retry loading.
            </p>

            {process.env.NODE_ENV === 'development' && (
              <details className="text-left bg-surface p-4 rounded border border-border-secondary mb-4">
                <summary className="cursor-pointer text-sm font-medium text-white mb-2">
                  Error Details (Development Only)
                </summary>
                <div className="text-xs text-text-secondary space-y-2">
                  <div>
                    <strong>Error ID:</strong> {errorId}
                  </div>
                  <div>
                    <strong>Error:</strong> {error?.message}
                  </div>
                  <div>
                    <strong>Stack:</strong>
                    <pre className="mt-1 overflow-auto max-h-32 text-xs">
                      {error?.stack}
                    </pre>
                  </div>
                  {errorInfo && (
                    <div>
                      <strong>Component Stack:</strong>
                      <pre className="mt-1 overflow-auto max-h-32 text-xs">
                        {errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            <button
              onClick={this.resetErrorBoundary}
              className="px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return children;
  }
}

export default SSRErrorBoundary;

/**
 * Hook to create error boundary reset function
 */
export const useErrorBoundaryReset = () => {
  const [resetKey, setResetKey] = React.useState(0);

  const reset = React.useCallback(() => {
    setResetKey(prev => prev + 1);
  }, []);

  return { resetKey, reset };
};

/**
 * Higher-order component to wrap components with SSR error boundary
 */
export const withSSRErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const ComponentWithErrorBoundary: React.FC<P> = (props) => {
    return (
      <SSRErrorBoundary fallback={fallback}>
        <WrappedComponent {...props} />
      </SSRErrorBoundary>
    );
  };

  ComponentWithErrorBoundary.displayName = `withSSRErrorBoundary(${
    WrappedComponent.displayName || WrappedComponent.name
  })`;

  return ComponentWithErrorBoundary;
};

/**
 * Specialized error boundary for user management pages
 */
export const UserManagementErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    // Log specific user management errors (only in development)
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('User Management Error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
      });
    }
  };

  const fallback = (
    <div className="min-h-[600px] flex items-center justify-center bg-background rounded-lg">
      <div className="text-center p-8">
        <i className="ri-user-settings-line text-4xl text-primary-500 mb-4"></i>
        <h3 className="text-xl font-medium text-white mb-2">
          User Management Temporarily Unavailable
        </h3>
        <p className="text-text-secondary mb-4">
          We're experiencing technical difficulties with the user management system. 
          Please try refreshing the page or contact support if the issue persists.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
        >
          Refresh Page
        </button>
      </div>
    </div>
  );

  return (
    <SSRErrorBoundary fallback={fallback} onError={handleError}>
      {children}
    </SSRErrorBoundary>
  );
};
