import { BetDetailsData } from "@/shared/types";
import { getCurrentCurrencyConfig } from "@/shared/config/currencyConfig";

/**
 * Generate currency icon SVG string for printing
 * Returns the LKR currency icon SVG or fallback text for other currencies
 */
const getCurrencyIconForPrint = (size: number = 16, color: string = '#000000'): string => {
  const currencyConfig = getCurrentCurrencyConfig();

  if (currencyConfig.hasCustomIcon && currencyConfig.code === 'LKR') {
    // Return the LKR currency icon SVG
    return `<svg width="${size}" height="${size}" viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: inline-block; vertical-align: middle; margin-right: 2px;">
      <path d="M400 8.03174C616.474 8.03174 791.968 183.526 791.968 400C791.968 616.474 616.474 791.969 400 791.969C183.526 791.969 8.03125 616.474 8.03125 400C8.03125 183.526 183.526 8.03174 400 8.03174ZM683.627 380.394H752.207C747.681 297.754 714.705 222.682 662.915 164.817L614.414 213.328C653.91 258.654 679.281 316.642 683.627 380.394ZM752.207 419.607H683.627C679.281 483.368 653.92 541.346 614.414 586.682L662.915 635.183C714.705 577.318 747.681 502.256 752.207 419.607ZM635.183 662.916L586.681 614.414C541.346 653.92 483.367 679.272 419.606 683.618V752.208C502.256 747.682 577.318 714.714 635.183 662.916ZM380.393 752.208V683.628C316.642 679.281 258.654 653.911 213.327 614.414L164.817 662.916C222.682 714.705 297.753 747.682 380.393 752.208ZM137.084 635.193L185.595 586.682C146.089 541.346 120.728 483.368 116.382 419.607H47.7922C52.3182 502.256 85.2948 577.328 137.084 635.193ZM154.933 400C152.571 534.382 267.328 645.076 400 645.076C535.355 645.076 645.076 535.356 645.076 400C645.076 264.692 535.412 154.99 400.104 154.924C267.375 154.924 152.571 265.533 154.933 400ZM47.7922 380.394H116.382C120.719 316.642 146.089 258.654 185.595 213.318L137.094 164.817C85.2948 222.682 52.3182 297.754 47.7922 380.394ZM380.393 116.382V47.7927C297.753 52.3187 222.682 85.2953 164.817 137.094L213.318 185.595C258.654 146.09 316.642 120.719 380.393 116.382ZM419.606 47.7927V116.382C483.367 120.719 541.346 146.09 586.681 185.595L635.183 137.094C577.318 85.2953 502.256 52.3187 419.606 47.7927ZM380.393 241.769C380.393 215.983 419.606 215.983 419.606 241.769V262.944H450.844C476.63 262.944 476.63 302.166 450.844 302.166H419.606V380.394H438.334C470.696 380.394 497.097 406.794 497.097 439.147V478.303C497.097 510.609 470.611 537.056 438.334 537.056H419.606V558.231C419.606 584.027 380.393 584.027 380.393 558.231V537.056H349.165C323.369 537.056 323.369 497.843 349.165 497.843H380.393V419.607H361.666C329.369 419.607 302.912 393.178 302.912 360.853V321.707C302.912 289.344 329.313 262.944 361.666 262.944H380.393V241.769ZM419.606 419.607V497.843H438.334C449.096 497.843 457.874 489.047 457.874 478.303V439.147C457.874 428.451 449.039 419.607 438.334 419.607H419.606ZM380.393 380.394V302.166H361.666C350.96 302.166 342.125 311.001 342.125 321.707V360.853C342.125 371.606 350.932 380.394 361.666 380.394H380.393Z" fill="${color}"/>
    </svg>`;
  }

  // For other currencies, return the text symbol
  return currencyConfig.symbol;
};

interface Props extends BetDetailsData {
  qrCodeDataUrl: string;
}
export const PrintBetSlipUI = ({ qrCodeDataUrl, ...betDetailsData }: Props) => {
  // Get currency icon for printing
  const currencyIcon = getCurrencyIconForPrint(10, '#000000');

  return `
      <!DOCTYPE html>
      <html>
        <head>
        <title>BET SLIP</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 10px;
              background-color: white;
              color: black;
              line-height: 1.6;
              font-size: 10px;
            }
            .header {
              text-align: center;
              margin-bottom: 5px;
              position: relative;
            }
            .title {
              color: black;
              font-size: 14px;
              font-weight: bold;
              margin: 0;
            }
            .bet-slip-id {
              position: absolute;
              top: 100px;
              margin-top: 10px;
              right: 0;
              font-size: 12px;
              font-weight: bold;
              color: black;
            }
            .subtitle {
              color: black;
              font-size: 12px;
              margin: 10px 0 0 0;
              font-weight: normal;
            }
            .section {
              margin: 10px 0;
              background-color: white;
            }
            .section-title {
              font-size: 14px;
              font-weight: bold;
              color: black;
              margin-bottom: 5px;
              border-bottom: 2px solid #000;
              text-transform: uppercase;
            }
            .detail-row {
              display: flex;
              justify-content: space-between;
              align-tems:center,
              text-align: center;
              padding: 8px 0;
              border-bottom: 1px solid #ccc;
            }
            .detail-row:last-child {
              border-bottom: none;
            }
            .label {
              font-weight: bold;
              color: black;
              flex: 1;
              font-size: 10px;
            }
            .value {
              color: black;
              flex: 2;
              text-align: right;
              font-size: 10px;
            }
            .bet-list {
              margin-top: 5px;
            }
            .bet-item {
              background: white;
              padding: 5px;
              margin: 5px 0;
            }
            .qr-section {
              margin-top: 10px;
              text-align: center;
              padding:5px;
              border-top: 2px solid #000;
            }
            .qr-code {
              width: 100px;
              height: 100px;
              margin: 0 auto 10px;
              border: 2px solid #000;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: white;
              font-size: 14px;
              color: black;
            }
            .qr-label {
              font-size: 12px;
              color: black;
              font-weight: bold;
              margin-top: 5px;
            }
            .footer {
              margin-top: 5px;
              font-size: 10px;
              color: black;
              border-top: 2px solid #000;
              padding-top: 5px;
            }
            .footer-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin: 5px 0;
            }
            .footer-item {
              flex: 1;
              text-align: center;
            }
            @media print {
              body {
                margin: 5px;
                background-color: white !important;
                color: black !important;
              }
              .qr-section {
                break-inside: avoid;
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <p class="subtitle">Transaction Confirmation</p>
          </div>

          <!-- Market Details Section -->
          <div class="section">
            <div class="section-title">Market Information</div>
            <div class="detail-row">
              <span class="label">Market ID:</span>
              <span class="value">${betDetailsData.marketDetail?.marketId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Name:</span>
              <span class="value">${betDetailsData.marketDetail?.marketName || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Status:</span>
              <span class="value">${betDetailsData.marketDetail?.marketStatus || 'N/A'}</span>
            </div>
          </div>

          <!-- Bet Details Section -->
          <div class="section">
            <div class="section-title">Bet Details</div>
            <div class="detail-row">
              <span class="label">Bet ID:</span>
              <span class="value">${betDetailsData.betDetails?.betId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Type:</span>
              <span class="value">${betDetailsData.betDetails?.betType || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Status:</span>
              <span class="value">${betDetailsData.betDetails?.settlementStatus || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Amount:</span>
              <span class="value">${currencyIcon} ${(betDetailsData.betDetails?.betAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Amount:</span>
              <span class="value">${currencyIcon} ${(betDetailsData.betDetails?.settlementAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Created Date:</span>
              <span class="value">${betDetailsData.betDetails?.createdDate ? new Date(betDetailsData.betDetails.createdDate).toLocaleString() : 'N/A'}</span>
            </div>
          </div>

          <!-- Individual Bets Section -->
          ${betDetailsData.betList && betDetailsData.betList.length > 0 ? `
          <div class="section">
            <div class="section-title">Individual Bets</div>
            <div class="bet-list">
              ${betDetailsData.betList.map((bet, index) => `
                <div class="bet-item">
                  <div class="detail-row">
                    <span class="label">Bet ${index + 1} ID:</span>
                    <span class="value">${bet.betId || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Market Name:</span>
                    <span class="value">${bet.marketName || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Bet Type:</span>
                    <span class="value">${bet.betType || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Rate:</span>
                    <span class="value">${bet.rate || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Stake:</span>
                    <span class="value">${currencyIcon}${(bet.stake || 0).toFixed(2)}</span>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          ` : ''}

          <!-- QR Code Section -->
          ${qrCodeDataUrl ? `
          <div class="qr-section">
            <img src="${qrCodeDataUrl}" alt="Bet Verification QR Code" class="qr-code" style="width: 100px; height: 100px; margin: 0 auto; display: block;" />
            <div class="qr-label">Scan for bet verification</div>
          </div>
          ` : `
          <div class="qr-section">
            <div class="qr-code">QR CODE</div>
            <div class="qr-label">Scan for bet verification</div>
          </div>
          `}

          <div class="footer">
            <p style="text-align: center; margin-bottom: 10px;"><strong>Thank you for your bet. Good luck!</strong></p>
            <div class="footer-row">
              <div class="footer-item">This is an automatically generated bet slip.</div>
              <div class="footer-item">Printed on: 22/07/2025, 21:51:37</div>
              <div class="footer-item">Printer: Default System Printer</div>
            </div>
          </div>
        </body>
      </html>
    `;
};
