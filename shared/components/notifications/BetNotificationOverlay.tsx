// shared/components/notifications/BetNotificationOverlay.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { CashierTurboPlaceBetDetails } from '@/shared/types/user-management-types';
import { useTurboStarsBetNotifications } from '@/shared/hooks';

interface NotificationItem {
  id: string;
  notification: CashierTurboPlaceBetDetails;
  timestamp: number;
}

interface BetNotificationOverlayProps {
  className?: string;
  maxNotifications?: number;
  autoCloseDelay?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center';
}

export const BetNotificationOverlay: React.FC<BetNotificationOverlayProps> = ({
  className = '',
  maxNotifications = 3,
  autoCloseDelay = 5000,
  position = 'top-right'
}) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  // Handle new bet notifications
  const handleBetPlaced = useCallback((notification: CashierTurboPlaceBetDetails) => {
    const newNotification: NotificationItem = {
      id: `${notification.transactionId}-${Date.now()}`,
      notification,
      timestamp: Date.now()
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      // Keep only the latest notifications up to maxNotifications
      return updated.slice(0, maxNotifications);
    });

    // console.log('Bet notification overlay: New notification received:', notification);
  }, [maxNotifications]);

  // Subscribe to bet notifications
  useTurboStarsBetNotifications(handleBetPlaced);

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(item => item.id !== id));
  }, []);

  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 right-4';
    }
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div
      className={`
        fixed ${getPositionClasses()} z-[9999] 
        pointer-events-none space-y-2 max-w-sm w-full
        ${className}
      `}
    >
      {notifications.map((item) => (
        <BetNotificationCard
          key={item.id}
          notification={item.notification}
          onClose={() => removeNotification(item.id)}
          autoCloseDelay={autoCloseDelay}
        />
      ))}
    </div>
  );
};

interface BetNotificationCardProps {
  notification: CashierTurboPlaceBetDetails;
  onClose: () => void;
  autoCloseDelay: number;
}

const BetNotificationCard: React.FC<BetNotificationCardProps> = ({
  notification,
  onClose,
  autoCloseDelay
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isClosing, setIsClosing] = useState(false);

  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      setIsVisible(false);
      onClose();
    }, 300); // Animation duration
  }, [onClose]);

  useEffect(() => {
    const timer = setTimeout(() => {
      handleClose();
    }, autoCloseDelay);

    return () => clearTimeout(timer);
  }, [autoCloseDelay, handleClose]);

  if (!isVisible) return null;

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out pointer-events-auto
        ${isClosing ? 'translate-x-full opacity-0' : 'translate-x-0 opacity-100'}
      `}
    >
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden backdrop-blur-sm bg-opacity-95 dark:bg-opacity-95">
        {/* Header */}
        <div className="bg-green-50 dark:bg-green-900/20 border-b border-green-200 dark:border-green-800 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="flex-shrink-0">
                <svg
                  className="w-5 h-5 text-green-600 dark:text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Bet Placed Successfully
                </h3>
                <p className="text-xs text-green-600 dark:text-green-400">
                  TurboStars Sportsbook
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="flex-shrink-0 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors duration-200"
              aria-label="Close notification"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-4 py-3">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                User ID:
              </span>
              <span className="text-xs text-gray-900 dark:text-gray-100 font-mono">
                {notification.userId}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Market ID:
              </span>
              <span className="text-xs text-gray-900 dark:text-gray-100 font-mono truncate max-w-[120px]" title={notification.marketId}>
                {notification.marketId}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Transaction ID:
              </span>
              <span className="text-xs text-gray-900 dark:text-gray-100 font-mono truncate max-w-[120px]" title={notification.transactionId}>
                {notification.transactionId}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Provider:
              </span>
              <span className="text-xs text-gray-900 dark:text-gray-100 capitalize">
                {notification.provider}
              </span>
            </div>
          </div>
        </div>

        {/* Progress bar for auto-close */}
        <div className="h-1 bg-gray-200 dark:bg-gray-700">
          <div
            className="h-full bg-green-500 dark:bg-green-400 transition-all ease-linear"
            style={{
              animation: `shrink ${autoCloseDelay}ms linear forwards`
            }}
          />
        </div>
      </div>

      <style jsx>{`
        @keyframes shrink {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
    </div>
  );
};

// Compact overlay version for minimal space usage
interface CompactBetNotificationOverlayProps {
  className?: string;
  maxNotifications?: number;
}

export const CompactBetNotificationOverlay: React.FC<CompactBetNotificationOverlayProps> = ({
  className = '',
  maxNotifications = 5
}) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  const handleBetPlaced = useCallback((notification: CashierTurboPlaceBetDetails) => {
    const newNotification: NotificationItem = {
      id: `${notification.transactionId}-${Date.now()}`,
      notification,
      timestamp: Date.now()
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      return updated.slice(0, maxNotifications);
    });

    // Auto-remove after 3 seconds for compact version
    setTimeout(() => {
      setNotifications(prev => prev.filter(item => item.id !== newNotification.id));
    }, 3000);
  }, [maxNotifications]);

  useTurboStarsBetNotifications(handleBetPlaced);

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div
      className={`
        fixed top-4 right-4 z-[9999] pointer-events-none space-y-1 max-w-xs
        ${className}
      `}
    >
      {notifications.map((item) => (
        <div
          key={item.id}
          className="bg-green-500 dark:bg-green-600 text-white text-xs px-3 py-2 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out animate-pulse"
        >
          <div className="flex items-center space-x-2">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>Bet placed for User {item.notification.userId}</span>
          </div>
        </div>
      ))}
    </div>
  );
};
