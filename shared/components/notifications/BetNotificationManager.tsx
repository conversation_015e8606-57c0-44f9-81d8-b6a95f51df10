// shared/components/notifications/BetNotificationManager.tsx
'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { CashierTurboPlaceBetDetails } from '@/shared/types/user-management-types';
import { BetNotification, BetNotificationToast } from './BetNotification';
import { useTurboStarsBetNotifications } from '@/shared/hooks';

interface NotificationItem {
  id: string;
  notification: CashierTurboPlaceBetDetails;
  timestamp: number;
}

interface BetNotificationManagerProps {
  maxNotifications?: number;
  displayMode?: 'detailed' | 'toast';
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  className?: string;
}

export const BetNotificationManager: React.FC<BetNotificationManagerProps> = ({
  maxNotifications = 5,
  displayMode = 'detailed',
  position = 'top-right',
  className = ''
}) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  // Handle new bet notifications
  const handleBetPlaced = useCallback((notification: CashierTurboPlaceBetDetails) => {
    const newNotification: NotificationItem = {
      id: `${notification.transactionId}-${Date.now()}`,
      notification,
      timestamp: Date.now()
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      // Keep only the latest notifications up to maxNotifications
      return updated.slice(0, maxNotifications);
    });

    // console.log('New bet notification received:', notification);
  }, [maxNotifications]);

  // Subscribe to bet notifications
  useTurboStarsBetNotifications(handleBetPlaced);

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(item => item.id !== id));
  }, []);

  // Auto-cleanup old notifications (fallback)
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now();
      setNotifications(prev =>
        prev.filter(item => now - item.timestamp < 30000) // Remove after 30 seconds
      );
    }, 5000);

    return () => clearInterval(cleanup);
  }, []);

  // Position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-right':
      default:
        return 'top-4 right-4';
    }
  };

  if (notifications.length === 0) return null;

  return (
    <div
      className={`
        fixed ${getPositionClasses()} z-50 max-w-sm w-full space-y-2
        ${className}
      `}
    >
      {displayMode === 'detailed' ? (
        // Show only the latest notification in detailed mode
        <BetNotification
          key={notifications[0].id}
          notification={notifications[0].notification}
          onClose={() => removeNotification(notifications[0].id)}
        />
      ) : (
        // Show multiple notifications in toast mode
        notifications.map((item) => (
          <BetNotificationToast
            key={item.id}
            notification={item.notification}
            onClose={() => removeNotification(item.id)}
          />
        ))
      )}
    </div>
  );
};

// Hook for manual notification management
export const useBetNotificationManager = (maxNotifications = 5) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  const addNotification = useCallback((notification: CashierTurboPlaceBetDetails) => {
    const newNotification: NotificationItem = {
      id: `${notification.transactionId}-${Date.now()}`,
      notification,
      timestamp: Date.now()
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      return updated.slice(0, maxNotifications);
    });
  }, [maxNotifications]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(item => item.id !== id));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications
  };
};

// Simple notification badge for header
interface BetNotificationBadgeProps {
  className?: string;
}

export const BetNotificationBadge: React.FC<BetNotificationBadgeProps> = ({
  className = ''
}) => {
  const [recentBetsCount, setRecentBetsCount] = useState(0);
  const [lastNotification, setLastNotification] = useState<CashierTurboPlaceBetDetails | null>(null);

  const handleBetPlaced = useCallback((notification: CashierTurboPlaceBetDetails) => {
    setLastNotification(notification);
    setRecentBetsCount(prev => prev + 1);

    // Reset count after 10 seconds
    setTimeout(() => {
      setRecentBetsCount(prev => Math.max(0, prev - 1));
    }, 10000);
  }, []);

  useTurboStarsBetNotifications(handleBetPlaced);

  if (recentBetsCount === 0) return null;

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center space-x-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-full px-3 py-1">
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
        <span className="text-xs font-medium text-green-700 dark:text-green-300">
          {recentBetsCount} new bet{recentBetsCount !== 1 ? 's' : ''}
        </span>
      </div>

      {lastNotification && (
        <div className="absolute top-full left-0 mt-1 p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50 min-w-[200px]">
          <div className="text-xs space-y-1">
            <div className="font-medium text-gray-900 dark:text-gray-100">Latest Bet:</div>
            <div className="text-gray-600 dark:text-gray-400">
              User: {lastNotification.userId}
            </div>
            <div className="text-gray-600 dark:text-gray-400 font-mono truncate">
              {lastNotification.transactionId}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
