"use client";

import {
  buildImageUrl,
  getOptimizedImageProps,
  handleImageError,
  IMAGE_SIZES,
  isExternalImage,
  OptimizedImageProps,
  trackImagePerformance
} from '@/shared/utils/imageOptimization';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';

interface OptimizedImageComponentProps extends Partial<OptimizedImageProps> {
  src: string;
  alt: string;
  type?: keyof typeof IMAGE_SIZES;
  size?: string;
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
  loading?: 'lazy' | 'eager';
  unoptimized?: boolean;
  useBackendUrl?: boolean; // Whether to process src through buildImageUrl utility
}

const OptimizedImage: React.FC<OptimizedImageComponentProps> = ({
  src,
  alt,
  type,
  size,
  fallbackSrc,
  onLoad,
  onError,
  loading = 'lazy',
  unoptimized = false,
  useBackendUrl = false,
  ...props
}) => {
  // Process src through buildImageUrl if useBackendUrl is true
  const processedSrc = useBackendUrl ? buildImageUrl(src, fallbackSrc) : src;

  const [imageSrc, setImageSrc] = useState(processedSrc);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [loadStartTime] = useState(performance.now());

  // Get optimized props if type and size are provided
  const optimizedProps = type && size
    ? getOptimizedImageProps(type, size, src, alt, props)
    : { src, alt, ...props };

  // Handle external images
  const shouldUnoptimize = unoptimized || isExternalImage(imageSrc);

  useEffect(() => {
    const newSrc = useBackendUrl ? buildImageUrl(src, fallbackSrc) : src;
    setImageSrc(newSrc);
    setHasError(false);
    setIsLoading(true);
  }, [src, useBackendUrl, fallbackSrc]);

  const handleLoad = () => {
    setIsLoading(false);
    trackImagePerformance(imageSrc, loadStartTime);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);

    if (fallbackSrc && imageSrc !== fallbackSrc) {
      setImageSrc(fallbackSrc);
      setHasError(false);
      setIsLoading(true);
    } else {
      const defaultFallback = handleImageError(imageSrc, fallbackSrc);
      if (defaultFallback !== imageSrc) {
        setImageSrc(defaultFallback);
        setHasError(false);
        setIsLoading(true);
      }
    }

    onError?.();
  };

  // Loading placeholder
  if (isLoading && !hasError) {
    return (
      <div
        className={`animate-pulse bg-gray-200 ${props.className || ''}`}
        style={{
          width: optimizedProps.width || props.width || 'auto',
          height: optimizedProps.height || props.height || 'auto',
        }}
      >
        <div className="w-full h-full bg-gray-300 rounded"></div>
      </div>
    );
  }

  // Error state
  if (hasError && !fallbackSrc) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 text-gray-400 ${props.className || ''}`}
        style={{
          width: optimizedProps.width || props.width || 'auto',
          height: optimizedProps.height || props.height || 'auto',
        }}
      >
        <svg
          className="w-8 h-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>
    );
  }

  return (
    <Image
      {...optimizedProps}
      src={imageSrc}
      alt={alt}
      onLoad={handleLoad}
      onError={handleError}
      loading={loading}
      unoptimized={shouldUnoptimize}
      style={{
        objectFit: props.objectFit || 'cover',
        objectPosition: props.objectPosition || 'center',
        ...props.style,
      }}
    />
  );
};

// Specialized components for common use cases
export const AvatarImage: React.FC<Omit<OptimizedImageComponentProps, 'type'> & { size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' }> = (props) => (
  <OptimizedImage {...props} type="avatar" useBackendUrl={true} className={`rounded-full ${props.className || ''}`} />
);

export const LogoImage: React.FC<Omit<OptimizedImageComponentProps, 'type'> & { size?: 'sm' | 'md' | 'lg' }> = (props) => (
  <OptimizedImage {...props} type="logo" priority />
);

export const CardImage: React.FC<Omit<OptimizedImageComponentProps, 'type'> & { size?: 'sm' | 'md' | 'lg' }> = (props) => (
  <OptimizedImage {...props} type="card" />
);

export const ThumbnailImage: React.FC<Omit<OptimizedImageComponentProps, 'type'> & { size?: 'xs' | 'sm' | 'md' }> = (props) => (
  <OptimizedImage {...props} type="thumbnail" />
);

export const BannerImage: React.FC<Omit<OptimizedImageComponentProps, 'type'> & { size?: 'sm' | 'md' | 'lg' }> = (props) => (
  <OptimizedImage {...props} type="banner" priority />
);

// Hook for image preloading
export const useImagePreloader = () => {
  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = document.createElement('img');
      img.onload = () => resolve();
      img.onerror = reject;
      img.src = src;
    });
  };

  const preloadImages = async (srcs: string[]): Promise<void> => {
    try {
      await Promise.all(srcs.map(preloadImage));
    } catch {
      // console.error('Error preloading images:', error);
    }
  };

  return { preloadImage, preloadImages };
};

// Progressive image loading component
export const ProgressiveImage: React.FC<OptimizedImageComponentProps & {
  lowQualitySrc?: string;
}> = ({ lowQualitySrc, ...props }) => {
  const [isHighQualityLoaded, setIsHighQualityLoaded] = useState(false);

  return (
    <div className="relative">
      {lowQualitySrc && !isHighQualityLoaded && (
        <OptimizedImage
          {...props}
          src={lowQualitySrc}
          className={`absolute inset-0 ${props.className || ''}`}
          quality={10}
        />
      )}
      <OptimizedImage
        {...props}
        onLoad={() => {
          setIsHighQualityLoaded(true);
          props.onLoad?.();
        }}
        className={`transition-opacity duration-300 ${isHighQualityLoaded ? 'opacity-100' : 'opacity-0'
          } ${props.className || ''}`}
      />
    </div>
  );
};

export default OptimizedImage;
