// shared/utils/passwordEncryption.ts - Utility functions for password and PIN encryption

/**
 * Encodes a password or PIN to base64 format as expected by the backend
 * The backend decodes using: Buffer.from(password, 'base64').toString('ascii')
 * 
 * @param password - The plain text password or PIN to encode
 * @returns The base64 encoded password/PIN
 */
export const encodePassword = (password: string): string => {
  if (!password) {
    return '';
  }
  
  // Convert the password to base64
  // This matches what the backend expects to decode with <PERSON>uffer.from(password, 'base64').toString('ascii')
  return btoa(password);
};

/**
 * Encodes a PIN to base64 format as expected by the backend
 * This is an alias for encodePassword for semantic clarity when dealing with PINs
 * 
 * @param pin - The plain text PIN to encode
 * @returns The base64 encoded PIN
 */
export const encodePin = (pin: string): string => {
  return encodePassword(pin);
};

/**
 * Validates that a password is not empty before encoding
 * 
 * @param password - The password to validate and encode
 * @returns The base64 encoded password
 * @throws Error if password is empty
 */
export const validateAndEncodePassword = (password: string): string => {
  if (!password || password.trim() === '') {
    throw new Error('Password cannot be empty');
  }
  
  return encodePassword(password);
};

/**
 * Validates that a PIN is not empty and has the correct length before encoding
 * 
 * @param pin - The PIN to validate and encode
 * @param expectedLength - The expected length of the PIN (default: 8)
 * @returns The base64 encoded PIN
 * @throws Error if PIN is invalid
 */
export const validateAndEncodePin = (pin: string, expectedLength: number = 8): string => {
  if (!pin || pin.trim() === '') {
    throw new Error('PIN cannot be empty');
  }
  
  if (pin.length !== expectedLength) {
    throw new Error(`PIN must be exactly ${expectedLength} characters long`);
  }
  
  return encodePin(pin);
};
