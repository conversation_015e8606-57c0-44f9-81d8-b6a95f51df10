// Image optimization utilities for the BetShop Starterkit

import { CSSProperties } from "react";

export interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  className?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  objectPosition?: string;
  style?: CSSProperties;
}

// Common image sizes for responsive design
export const IMAGE_SIZES = {
  avatar: {
    xs: { width: 24, height: 24 },
    sm: { width: 32, height: 32 },
    md: { width: 48, height: 48 },
    lg: { width: 64, height: 64 },
    xl: { width: 80, height: 80 },
    '2xl': { width: 96, height: 96 },
  },
  logo: {
    sm: { width: 100, height: 30 },
    md: { width: 150, height: 40 },
    lg: { width: 200, height: 50 },
  },
  card: {
    sm: { width: 200, height: 150 },
    md: { width: 300, height: 200 },
    lg: { width: 400, height: 250 },
  },
  banner: {
    sm: { width: 600, height: 200 },
    md: { width: 800, height: 300 },
    lg: { width: 1200, height: 400 },
  },
  thumbnail: {
    xs: { width: 50, height: 50 },
    sm: { width: 75, height: 75 },
    md: { width: 100, height: 100 },
  }
};

// Responsive sizes for different breakpoints
export const RESPONSIVE_SIZES = {
  avatar: '(max-width: 768px) 32px, 48px',
  logo: '(max-width: 768px) 100px, 150px',
  card: '(max-width: 768px) 200px, (max-width: 1024px) 300px, 400px',
  banner: '(max-width: 768px) 100vw, (max-width: 1024px) 80vw, 1200px',
  thumbnail: '(max-width: 768px) 50px, 75px',
  full: '100vw',
};

// Generate blur placeholder for images
export const generateBlurDataURL = (width: number, height: number): string => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');

  if (ctx) {
    // Create a simple gradient blur placeholder
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#f3f4f6');
    gradient.addColorStop(1, '#e5e7eb');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  }

  return canvas.toDataURL();
};

// Check if image is external URL
export const isExternalImage = (src: string): boolean => {
  return src.startsWith('http://') || src.startsWith('https://') || src.startsWith('//');
};

// Optimize image path for Next.js
export const optimizeImagePath = (src: string): string => {
  // If it's an external URL, return as is
  if (isExternalImage(src)) {
    return src;
  }

  // Ensure local images start with /
  if (!src.startsWith('/')) {
    return `/${src}`;
  }

  return src;
};

// Get optimized image props based on type
export const getOptimizedImageProps = (
  type: keyof typeof IMAGE_SIZES,
  size: string,
  src: string,
  alt: string,
  options: Partial<OptimizedImageProps> = {}
): OptimizedImageProps => {
  const dimensions = IMAGE_SIZES[type]?.[size as keyof typeof IMAGE_SIZES[typeof type]];

  if (!dimensions) {
    throw new Error(`Invalid image type "${type}" or size "${size}"`);
  }

  return {
    src: optimizeImagePath(src),
    alt,
    width: dimensions.width,
    height: dimensions.height,
    sizes: RESPONSIVE_SIZES[type] || RESPONSIVE_SIZES.full,
    quality: 85,
    placeholder: 'blur',
    blurDataURL: generateBlurDataURL(dimensions.width, dimensions.height),
    ...options,
  };
};

// Preload critical images
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

// Preload multiple images
export const preloadImages = async (srcs: string[]): Promise<void> => {
  try {
    await Promise.all(srcs.map(preloadImage));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error preloading images:', error);
  }
};

// Critical images that should be preloaded
export const CRITICAL_IMAGES = [
  '/assets/images/brand-logos/desktop-logo.png',
  '/assets/images/brand-logos/desktop-white.png',
  '/assets/images/brand-logos/toggle-logo.png',
  '/assets/images/brand-logos/toggle-dark.png',
];

// Image format detection and optimization
export const getOptimalImageFormat = (): string[] => {
  const formats: string[] = [];

  // Check for AVIF support
  const avifCanvas = document.createElement('canvas');
  if (avifCanvas.toDataURL('image/avif').indexOf('data:image/avif') === 0) {
    formats.push('image/avif');
  }

  // Check for WebP support
  const webpCanvas = document.createElement('canvas');
  if (webpCanvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
    formats.push('image/webp');
  }

  // Fallback to JPEG/PNG
  formats.push('image/jpeg', 'image/png');

  return formats;
};

// Image lazy loading intersection observer
export const createImageObserver = (callback: (entries: IntersectionObserverEntry[]) => void) => {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return null;
  }

  return new IntersectionObserver(callback, {
    rootMargin: '50px 0px', // Start loading 50px before the image enters viewport
    threshold: 0.01,
  });
};

// Performance monitoring for images
export const trackImagePerformance = (src: string, startTime: number) => {
  const endTime = performance.now();
  const loadTime = endTime - startTime;

  // Send to analytics if available
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', 'image_load_time', {
      eventCategory: 'Performance',
      eventLabel: src,
      value: Math.round(loadTime),
    });
  }
};

// Image error handling
export const handleImageError = (src: string, fallbackSrc?: string) => {

  if (fallbackSrc) {
    return fallbackSrc;
  }

  // Return a default placeholder image
  return '/assets/images/placeholder.png';
};

/**
 * Build a complete image URL from a backend image path
 * Handles various edge cases and provides consistent URL construction across the application
 *
 * @param imagePath - The image path from backend API response (can be null, undefined, relative, or absolute)
 * @param fallbackImage - Optional fallback image path to use if imagePath is invalid
 * @returns Complete image URL ready for use in Image components
 *
 * @example
 * ```typescript
 * // Backend returns relative path
 * buildImageUrl('uploads/avatars/user123.jpg')
 * // Returns: 'https://assets.roylfc.com/uploads/avatars/user123.jpg'
 *
 * // Backend returns absolute URL
 * buildImageUrl('https://example.com/image.jpg')
 * // Returns: 'https://example.com/image.jpg'
 *
 * // Backend returns null/undefined
 * buildImageUrl(null, '/images/default-avatar.png')
 * // Returns: '/images/default-avatar.png'
 * ```
 */
export const buildImageUrl = (
  imagePath: string | null | undefined,
  fallbackImage: string = '/images/profile.png'
): string => {
  // Handle null, undefined, or empty string
  if (!imagePath || typeof imagePath !== 'string' || imagePath.trim() === '') {
    return fallbackImage;
  }

  const trimmedPath = imagePath.trim();

  // If it's already a complete URL (absolute), return as-is
  if (isExternalImage(trimmedPath)) {
    return trimmedPath;
  }

  // Get the primary image base URL from environment
  const primaryBaseUrl = process.env.NEXT_PUBLIC_IMAGES_BASE_URL;

  // Fallback to staging backend URL if primary is not available
  const fallbackBaseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL;

  // Use primary base URL if available, otherwise fallback
  const baseUrl = primaryBaseUrl || fallbackBaseUrl;

  // If no base URL is available, return the fallback image
  if (!baseUrl) {
    // eslint-disable-next-line no-console
    console.warn('No image base URL configured. Please set NEXT_PUBLIC_IMAGES_BASE_URL or NEXT_PUBLIC_STAGING_BACKEND_URL');
    return fallbackImage;
  }

  // Normalize the path - remove leading slash if present to avoid double slashes
  const normalizedPath = trimmedPath.startsWith('/') ? trimmedPath.slice(1) : trimmedPath;

  // Construct the full URL
  const fullUrl = `${baseUrl}/${normalizedPath}`;

  // Validate the constructed URL
  try {
    new URL(fullUrl);
    return fullUrl;
  } catch {
    // eslint-disable-next-line no-console
    console.warn(`Invalid image URL constructed: ${fullUrl}. Using fallback image.`);
    return fallbackImage;
  }
};

// Utility to convert image to base64 for blur placeholder
export const imageToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

const imageOptimizationUtils = {
  IMAGE_SIZES,
  RESPONSIVE_SIZES,
  CRITICAL_IMAGES,
  getOptimizedImageProps,
  preloadImage,
  preloadImages,
  optimizeImagePath,
  isExternalImage,
  generateBlurDataURL,
  getOptimalImageFormat,
  createImageObserver,
  trackImagePerformance,
  handleImageError,
  imageToBase64,
  buildImageUrl,
};

export default imageOptimizationUtils;
