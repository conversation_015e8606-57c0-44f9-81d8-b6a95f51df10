// shared/utils/cache.ts - Advanced caching utilities for performance optimization

/**
 * Cache configuration interface
 */
export interface CacheConfig {
	ttl: number; // Time to live in milliseconds
	maxSize?: number; // Maximum number of entries
	staleWhileRevalidate?: number; // Stale-while-revalidate time in milliseconds
}

/**
 * Cache entry interface
 */
interface CacheEntry<T> {
	data: T;
	timestamp: number;
	ttl: number;
	staleWhileRevalidate?: number;
}

/**
 * Advanced in-memory cache with TTL and stale-while-revalidate support
 */
export class AdvancedCache<T = any> {
	private cache = new Map<string, CacheEntry<T>>();
	private maxSize: number;
	private revalidationPromises = new Map<string, Promise<T>>();

	constructor(private config: CacheConfig) {
		this.maxSize = config.maxSize || 1000;
	}

	/**
	 * Set cache entry
	 */
	set(key: string, data: T, customTtl?: number): void {
		// Evict oldest entries if cache is full
		if (this.cache.size >= this.maxSize) {
			const oldestKey = this.cache.keys().next().value;
			if (oldestKey !== undefined) {
				this.cache.delete(oldestKey);
			}
		}

		const entry: CacheEntry<T> = {
			data,
			timestamp: Date.now(),
			ttl: customTtl || this.config.ttl,
			staleWhileRevalidate: this.config.staleWhileRevalidate,
		};

		this.cache.set(key, entry);
	}

	/**
	 * Get cache entry with stale-while-revalidate support
	 */
	async get(
		key: string,
		revalidateFn?: () => Promise<T>
	): Promise<T | null> {
		const entry = this.cache.get(key);
		if (!entry) return null;

		const now = Date.now();
		const age = now - entry.timestamp;

		// If data is fresh, return it
		if (age < entry.ttl) {
			return entry.data;
		}

		// If data is stale but within stale-while-revalidate window
		if (entry.staleWhileRevalidate && age < entry.ttl + entry.staleWhileRevalidate) {
			// Return stale data immediately
			const staleData = entry.data;

			// Revalidate in background if function provided
			if (revalidateFn && !this.revalidationPromises.has(key)) {
				const revalidationPromise = revalidateFn()
					.then((newData) => {
						this.set(key, newData);
						this.revalidationPromises.delete(key);
						return newData;
					})
					.catch((_error) => {
						// console.warn(`Cache revalidation failed for key ${key}:`, error);
						this.revalidationPromises.delete(key);
						return staleData;
					});

				this.revalidationPromises.set(key, revalidationPromise);
			}

			return staleData;
		}

		// Data is too old, remove it
		this.cache.delete(key);
		return null;
	}

	/**
	 * Check if key exists and is not expired
	 */
	has(key: string): boolean {
		const entry = this.cache.get(key);
		if (!entry) return false;

		const age = Date.now() - entry.timestamp;
		if (age >= entry.ttl + (entry.staleWhileRevalidate || 0)) {
			this.cache.delete(key);
			return false;
		}

		return true;
	}

	/**
	 * Delete cache entry
	 */
	delete(key: string): boolean {
		return this.cache.delete(key);
	}

	/**
	 * Clear all cache entries
	 */
	clear(): void {
		this.cache.clear();
		this.revalidationPromises.clear();
	}

	/**
	 * Get cache statistics
	 */
	getStats() {
		const now = Date.now();
		let fresh = 0;
		let stale = 0;
		let expired = 0;

		for (const [_key, entry] of this.cache.entries()) {
			const age = now - entry.timestamp;
			if (age < entry.ttl) {
				fresh++;
			} else if (entry.staleWhileRevalidate && age < entry.ttl + entry.staleWhileRevalidate) {
				stale++;
			} else {
				expired++;
			}
		}

		return {
			total: this.cache.size,
			fresh,
			stale,
			expired,
			hitRate: fresh / (fresh + stale + expired) || 0,
		};
	}

	/**
	 * Cleanup expired entries
	 */
	cleanup(): number {
		const now = Date.now();
		let cleaned = 0;

		for (const [key, entry] of this.cache.entries()) {
			const age = now - entry.timestamp;
			if (age >= entry.ttl + (entry.staleWhileRevalidate || 0)) {
				this.cache.delete(key);
				cleaned++;
			}
		}

		return cleaned;
	}
}

/**
 * HTTP cache headers utility
 */
export const cacheHeaders = {
	/**
	 * No cache headers
	 */
	noCache: {
		'Cache-Control': 'no-cache, no-store, must-revalidate',
		'Pragma': 'no-cache',
		'Expires': '0',
	},

	/**
	 * Short cache (5 minutes)
	 */
	short: {
		'Cache-Control': 'public, max-age=300, s-maxage=300',
		'Vary': 'Accept-Encoding',
	},

	/**
	 * Medium cache (1 hour)
	 */
	medium: {
		'Cache-Control': 'public, max-age=3600, s-maxage=3600',
		'Vary': 'Accept-Encoding',
	},

	/**
	 * Long cache (1 day)
	 */
	long: {
		'Cache-Control': 'public, max-age=86400, s-maxage=86400',
		'Vary': 'Accept-Encoding',
	},

	/**
	 * Very long cache (1 year) for static assets
	 */
	static: {
		'Cache-Control': 'public, max-age=31536000, s-maxage=31536000, immutable',
		'Vary': 'Accept-Encoding',
	},

	/**
	 * Stale-while-revalidate cache
	 */
	staleWhileRevalidate: (maxAge: number, staleTime: number) => ({
		'Cache-Control': `public, max-age=${maxAge}, stale-while-revalidate=${staleTime}`,
		'Vary': 'Accept-Encoding',
	}),

	/**
	 * Custom cache headers
	 */
	custom: (maxAge: number, options: {
		sMaxAge?: number;
		staleWhileRevalidate?: number;
		staleIfError?: number;
		mustRevalidate?: boolean;
		private?: boolean;
	} = {}) => {
		const cacheControl = [
			options.private ? 'private' : 'public',
			`max-age=${maxAge}`,
		];

		if (options.sMaxAge) {
			cacheControl.push(`s-maxage=${options.sMaxAge}`);
		}

		if (options.staleWhileRevalidate) {
			cacheControl.push(`stale-while-revalidate=${options.staleWhileRevalidate}`);
		}

		if (options.staleIfError) {
			cacheControl.push(`stale-if-error=${options.staleIfError}`);
		}

		if (options.mustRevalidate) {
			cacheControl.push('must-revalidate');
		}

		return {
			'Cache-Control': cacheControl.join(', '),
			'Vary': 'Accept-Encoding',
		};
	},
};

/**
 * Global cache instances for different use cases
 */
export const caches = {
	// API responses cache (5 minutes TTL, 2 minutes stale-while-revalidate)
	api: new AdvancedCache({
		ttl: 5 * 60 * 1000,
		staleWhileRevalidate: 2 * 60 * 1000,
		maxSize: 500,
	}),

	// User data cache (10 minutes TTL, 5 minutes stale-while-revalidate)
	user: new AdvancedCache({
		ttl: 10 * 60 * 1000,
		staleWhileRevalidate: 5 * 60 * 1000,
		maxSize: 100,
	}),

	// Static data cache (1 hour TTL, 30 minutes stale-while-revalidate)
	static: new AdvancedCache({
		ttl: 60 * 60 * 1000,
		staleWhileRevalidate: 30 * 60 * 1000,
		maxSize: 200,
	}),

	// Metadata cache (24 hours TTL, 12 hours stale-while-revalidate)
	metadata: new AdvancedCache({
		ttl: 24 * 60 * 60 * 1000,
		staleWhileRevalidate: 12 * 60 * 60 * 1000,
		maxSize: 50,
	}),
};

/**
 * Cache key generator utility
 */
export const cacheKeys = {
	/**
	 * Generate cache key for API endpoint
	 */
	api: (endpoint: string, params?: Record<string, any>) => {
		const paramString = params ? JSON.stringify(params) : '';
		return `api:${endpoint}:${paramString}`;
	},

	/**
	 * Generate cache key for user data
	 */
	user: (userId: string, dataType?: string) => {
		return `user:${userId}${dataType ? `:${dataType}` : ''}`;
	},

	/**
	 * Generate cache key for static data
	 */
	static: (type: string, identifier?: string) => {
		return `static:${type}${identifier ? `:${identifier}` : ''}`;
	},

	/**
	 * Generate cache key for metadata
	 */
	metadata: (page: string, params?: Record<string, any>) => {
		const paramString = params ? JSON.stringify(params) : '';
		return `metadata:${page}:${paramString}`;
	},
};

// Auto cleanup caches every 10 minutes
if (typeof window !== 'undefined') {
	setInterval(() => {
		Object.values(caches).forEach(cache => cache.cleanup());
	}, 10 * 60 * 1000);
}
