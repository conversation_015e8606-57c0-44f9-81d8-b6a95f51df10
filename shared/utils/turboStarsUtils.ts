// shared/utils/turboStarsUtils.ts
import { TurboStarsGameLaunchResponse } from '@/shared/types/user-management-types';
import { useSportsbookStore } from '@/shared/stores/sportsbookStore';
import { useAuthStore } from '@/shared/stores/authStore';

/**
 * Opens TurboStars sportsbook inline within the current page
 * @param response - The response from TurboStars game launch API
 * @param userName - Username for the sportsbook session
 * @param userId - Optional user ID, will use auth store if not provided
 */
export const openTurboStarsSportsbookInline = (
  response: TurboStarsGameLaunchResponse,
  userName: string,
  userId?: number
): void => {
  if (!response.data.url) {
    throw new Error('No URL provided in TurboStars response');
  }

  try {
    // Get userId from parameter or auth store
    const currentUserId = userId || useAuthStore.getState().user?.id;

    // Use the sportsbook store to open inline
    const { openSportsbook } = useSportsbookStore.getState();
    openSportsbook(response, userName, currentUserId);

    // console.log('TurboStars sportsbook opened inline for user:', userName, 'userId:', currentUserId);
  } catch {
    // console.error('Failed to open TurboStars sportsbook inline:', error);
    throw new Error('Failed to open sportsbook inline');
  }
};

/**
 * Opens TurboStars sportsbook in a new window/tab (legacy function)
 * @param response - The response from TurboStars game launch API
 * @param windowFeatures - Optional window features for popup
 * @deprecated Use openTurboStarsSportsbookInline for better user experience
 */
export const openTurboStarsSportsbook = (
  response: TurboStarsGameLaunchResponse,
  windowFeatures?: string
): Window | null => {
  if (!response.data.url) {
    throw new Error('No URL provided in TurboStars response');
  }

  // Default window features for sportsbook
  const defaultFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no';
  const features = windowFeatures || defaultFeatures;

  try {
    // Open in new window/tab
    const newWindow = window.open(response.data.url, '_blank', features);

    if (!newWindow) {
      // Fallback if popup was blocked
      window.location.href = response.data.url;
      return null;
    }

    // Focus the new window
    newWindow.focus();

    return newWindow;
  } catch {
    // console.error('Failed to open TurboStars sportsbook:', error);
    throw new Error('Failed to open sportsbook window');
  }
};

/**
 * Validates TurboStars response data
 * @param response - The response to validate
 */
export const validateTurboStarsResponse = (response: TurboStarsGameLaunchResponse): boolean => {
  if (!response) {
    return false;
  }

  if (response.success !== 1) {
    return false;
  }

  if (!response.data || !response.data.url) {
    return false;
  }

  // Basic URL validation
  try {
    new URL(response.data.url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Extracts error message from TurboStars response or error
 * @param error - Error object or TurboStars response
 */
export const getTurboStarsErrorMessage = (error: any): string => {
  if (error instanceof Error) {
    return error.message;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.errors && Array.isArray(error.errors) && error.errors.length > 0) {
    return error.errors.join(', ');
  }

  return 'Unknown error occurred while launching sportsbook';
};

/**
 * Creates a safe window name for TurboStars sportsbook
 * @param userName - Username for the sportsbook session
 */
export const createSportsbookWindowName = (userName: string): string => {
  // Remove special characters and spaces for safe window name
  const safeName = userName.replace(/[^a-zA-Z0-9]/g, '_');
  return `turbostars_sportsbook_${safeName}`;
};

/**
 * Closes the inline TurboStars sportsbook
 */
export const closeTurboStarsSportsbookInline = (): void => {
  try {
    const { closeSportsbook } = useSportsbookStore.getState();
    closeSportsbook();
    // console.log('TurboStars sportsbook closed');
  } catch {
    // console.error('Failed to close TurboStars sportsbook:', error);
  }
};

/**
 * Checks if TurboStars sportsbook is currently open inline
 */
export const isTurboStarsSportsbookOpen = (): boolean => {
  try {
    const { isOpen } = useSportsbookStore.getState();
    return isOpen;
  } catch {
    // console.error('Failed to check TurboStars sportsbook status:', error);
    return false;
  }
};

/**
 * Checks if TurboStars sportsbook is supported in current environment
 */
export const isTurboStarsSupported = (): boolean => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return false;
  }

  // Check if required environment variables are set
  if (!process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL) {
    // console.warn('NEXT_PUBLIC_REPORTING_BACKEND_URL is not configured');
    return false;
  }

  return true;
};
