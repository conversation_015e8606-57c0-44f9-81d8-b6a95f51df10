// shared/utils/authClearingUtils.ts
import { useAuthStore } from '@/shared/stores/authStore';
import { clearAuthCookies } from '@/shared/utils/authCookies';
import { caches } from '@/shared/utils/cache';

/**
 * Comprehensive authentication clearing utility
 * Handles clearing all client-side authentication data and caches
 */

/**
 * Clear all localStorage items related to authentication and app state
 */
export const clearLocalStorage = (): void => {
  try {
    // Clear auth-related items
    localStorage.removeItem('auth-storage');

    // Also clear authentication cookies
    clearAuthCookies();

    // Clear theme and UI state items that might contain user preferences
    localStorage.removeItem('xintraverticalstyles');
    localStorage.removeItem('xintranavstyles');
    localStorage.removeItem('xintraMenu');
    localStorage.removeItem('xintraHeader');
    localStorage.removeItem('xintralighttheme');
    localStorage.removeItem('xintradarktheme');
    localStorage.removeItem('bodyBg');
    localStorage.removeItem('darkBg');
    localStorage.removeItem('inputBorder');
    localStorage.removeItem('lightRgb');
    localStorage.removeItem('gray');
    localStorage.removeItem('bgImg');

    // Clear any other app-specific storage keys
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('auth-') ||
        key.startsWith('user-') ||
        key.startsWith('token-') ||
        key.startsWith('session-') ||
        key.includes('authentication') ||
        key.includes('login')
      )) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('error: ', error);
    // Handle error silently in production
  }
};

/**
 * Clear all sessionStorage items
 */
export const clearSessionStorage = (): void => {
  try {
    sessionStorage.clear();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('error: ', error);
    // Handle error silently in production
  }
};

/**
 * Clear all cookies (especially authentication-related ones)
 */
export const clearCookies = (): void => {
  try {
    // Get all cookies
    const cookies = document.cookie.split(';');

    // Clear each cookie
    cookies.forEach(cookie => {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

      if (name) {
        // Clear cookie for current domain
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;

        // Clear cookie for parent domain (if subdomain)
        const domainParts = window.location.hostname.split('.');
        if (domainParts.length > 2) {
          const parentDomain = '.' + domainParts.slice(-2).join('.');
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${parentDomain}`;
        }
      }
    });

  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error clearing cookies:', error);
  }
};

/**
 * Clear all application caches
 */
export const clearApplicationCaches = (): void => {
  try {
    // Clear custom cache instances
    if (caches.api) {
      caches.api.clear();
    }
    if (caches.user) {
      caches.user.clear();
    }
    if (caches.static) {
      caches.static.clear();
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error clearing application caches:', error);
  }
};

/**
 * Clear browser caches (if supported)
 */
export const clearBrowserCaches = async (): Promise<void> => {
  try {
    // Clear Cache API if available
    if ('caches' in window) {
      const browserCaches = window.caches;
      const cacheNames = await browserCaches.keys();
      await Promise.all(
        cacheNames.map(cacheName => browserCaches.delete(cacheName))
      );
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error clearing browser caches:', error);
  }
};

/**
 * Clear IndexedDB (if used by the application)
 */
export const clearIndexedDB = async (): Promise<void> => {
  try {
    if ('indexedDB' in window) {
      // Get all databases (this is a newer API, may not be supported everywhere)
      if ('databases' in indexedDB) {
        const databases = await indexedDB.databases();
        await Promise.all(
          databases.map(db => {
            if (db.name) {
              return new Promise<void>((resolve, reject) => {
                const deleteReq = indexedDB.deleteDatabase(db.name!);
                deleteReq.onsuccess = () => resolve();
                deleteReq.onerror = () => reject(deleteReq.error);
              });
            }
          })
        );
      }
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error clearing IndexedDB:', error);
  }
};

/**
 * Reset Zustand auth store to initial state
 */
export const clearAuthStore = (): void => {
  try {
    const { clearAuth } = useAuthStore.getState();
    clearAuth();
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error clearing auth store:', error);
  }
};

/**
 * Clear React Query cache
 * Note: This should be called with the QueryClient instance
 */
export const clearReactQueryCache = (queryClient?: any): void => {
  try {
    if (queryClient) {
      queryClient.clear();
    } 
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error clearing React Query cache:', error);
  }
};

/**
 * Comprehensive authentication cleanup
 * Clears all authentication-related data from the client
 */
export const performCompleteAuthCleanup = async (queryClient?: any): Promise<void> => {
  try {
    // Clear all storage
    clearLocalStorage();
    clearSessionStorage();
    clearCookies();

    // Clear caches
    clearApplicationCaches();
    await clearBrowserCaches();
    await clearIndexedDB();

    // Clear application state
    clearAuthStore();
    clearReactQueryCache(queryClient);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error performing complete auth cleanup:', error);
    throw error;
  }
};

/**
 * Quick authentication cleanup (for immediate 401 response)
 * Clears essential authentication data quickly
 */
export const performQuickAuthCleanup = (queryClient?: any): void => {
  try {
    // Clear essential storage
    localStorage.removeItem('auth-storage');
    sessionStorage.clear();

    // Clear authentication cookies
    clearAuthCookies();

    // Clear application state
    clearAuthStore();
    clearReactQueryCache(queryClient);

    // Clear essential caches
    if (caches.user) {
      caches.user.clear();
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error performing quick auth cleanup:', error);
    throw error;
  }
};
