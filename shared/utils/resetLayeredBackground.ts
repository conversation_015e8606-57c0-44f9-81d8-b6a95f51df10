// shared/utils/resetLayeredBackground.ts
// Utility to reset and apply the new layered background system

import { useUIStore } from '@/shared/stores/uiStore';

/**
 * Reset localStorage and apply the new layered background system
 * This function clears old background color values and applies the new layered system
 */
export const resetToLayeredBackground = (): void => {
  // Clear old background-related localStorage values
  localStorage.removeItem('bodyBg');
  localStorage.removeItem('darkBg');
  localStorage.removeItem('lightRgb');
  localStorage.removeItem('gray');
  localStorage.removeItem('inputBorder');

  // Get the ThemeChanger function from Zustand store
  const { ThemeChanger } = useUIStore.getState();

  // Apply the NEW DARK THEME layered background system
  ThemeChanger({
    // Layer 1: Main body background
    bodyBg: "15 15 15",              // #0F0F0F - NEW DARK THEME main background

    // Layer 2: Navigation backgrounds
    darkBg: "29 29 29",              // #1D1D1D - NEW DARK THEME navigation background
    lightRgb: "29 29 29",            // #1D1D1D - NEW DARK THEME navigation background

    // Other dark theme settings
    class: "dark",
    dataHeaderStyles: "dark",
    dataMenuStyles: "dark",
    inputBorder: "rgba(255,255,255,0.1)",
    gray: "rgba(255,255,255,0.1)",
  });

  // Store the new layered background values
  localStorage.setItem("bodyBg", "29 29 29");      // #1D1D1D - swapped from header color
  localStorage.setItem("darkBg", "15 15 15");      // #0F0F0F - swapped from body color
  localStorage.setItem("lightRgb", "15 15 15");    // #0F0F0F - swapped from body color
  localStorage.setItem("inputBorder", "rgba(255,255,255,0.1)");
  localStorage.setItem("gray", "rgba(255,255,255,0.1)");
};

/**
 * Check if the current background colors match the layered system
 */
export const isUsingLayeredBackground = (): boolean => {
  const bodyBg = localStorage.getItem('bodyBg');
  const darkBg = localStorage.getItem('darkBg');
  const lightRgb = localStorage.getItem('lightRgb');

  return (
    bodyBg === "29 29 29" &&      // #1D1D1D - swapped from header color
    darkBg === "15 15 15" &&      // #0F0F0F - swapped from body color
    lightRgb === "15 15 15"       // #0F0F0F - swapped from body color
  );
};

/**
 * Get the current layered background colors as CSS custom properties
 */
export const getLayeredBackgroundCSS = () => ({
  '--body-bg': '29 29 29',           // Layer 1: Main body background (#1D1D1D) - swapped from header color
  '--dark-bg': '15 15 15',           // Layer 2: Section background (#0F0F0F) - swapped from body color
  '--menu-bg': '15 15 15',           // Layer 2: Navigation background (#0F0F0F) - swapped from body color
  '--header-bg': '15 15 15',         // Layer 2: Header background (#0F0F0F) - swapped from body color
  '--elevated-bg': '39 39 41',       // Layer 3: Elevated content (#272729)
});

/**
 * Apply layered background system on app initialization
 * Call this in your main layout or app component
 */
export const initializeLayeredBackground = (): void => {
  // Only apply if we're in a browser environment
  if (typeof window === 'undefined') return;

  // Check if user has custom background colors set
  const hasCustomColors = localStorage.getItem('bodyBg') &&
    !isUsingLayeredBackground();

  if (hasCustomColors) {
    resetToLayeredBackground();
  } else if (!localStorage.getItem('bodyBg')) {
    // First time user or no background set - apply layered system
    resetToLayeredBackground();
  }
};
