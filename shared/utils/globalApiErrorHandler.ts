// shared/utils/globalApiErrorHandler.ts
import { performQuickAuthCleanup } from './authClearingUtils';

// Extract types from the global fetch function
type FetchInput = Parameters<typeof fetch>[0];
type FetchInit = Parameters<typeof fetch>[1];

/**
 * Global API Error Handler
 * Centralized error handling for all API calls with special handling for 401 errors
 */

export interface ApiErrorHandlerConfig {
  /** QueryClient instance for clearing React Query cache */
  queryClient?: any;
  /** Function to show the 401 error modal */
  show401Modal?: () => void;
  /** Function to redirect to login page */
  redirectToLogin?: () => void;
  /** Whether to enable debug logging */
  enableDebugLogging?: boolean;
}

// Global configuration for the error handler
let globalConfig: ApiErrorHandlerConfig = {
  enableDebugLogging: process.env.NODE_ENV === 'development'
};

/**
 * Configure the global API error handler
 */
export const configureGlobalApiErrorHandler = (config: ApiErrorHandlerConfig): void => {
  globalConfig = { ...globalConfig, ...config };
};

/**
 * Check if a response or error indicates a 401 Unauthorized status
 */
export const is401Error = (error: any): boolean => {
  // Check Response object
  if (error instanceof Response) {
    return error.status === 401;
  }

  // Check fetch error with response
  if (error?.response?.status === 401) {
    return true;
  }

  // Check error status property
  if (error?.status === 401) {
    return true;
  }

  // Check error message for 401 indicators
  if (typeof error?.message === 'string') {
    const message = error.message.toLowerCase();
    return message.includes('401') ||
      message.includes('unauthorized') ||
      message.includes('authentication required') ||
      message.includes('invalid token') ||
      message.includes('token expired');
  }

  return false;
};

/**
 * Check if this is a betshop settings request that should be handled more leniently
 */
export const isBetshopSettingsRequest = (error: any): boolean => {
  // Check if the error is from a betshop settings request
  if (error instanceof Response) {
    return error.headers?.get('X-Betshop-Settings-Request') === 'true';
  }

  // Check if the error URL contains betshop-settings
  if (error && error.url && typeof error.url === 'string') {
    return error.url.includes('/api/v2/admin/betshop-settings');
  }

  return false;
};

/**
 * Handle 401 authentication errors
 */
export const handle401Error = async (error: any): Promise<void> => {
  // Check if this is a betshop settings request - be more lenient
  if (isBetshopSettingsRequest(error)) {
    // eslint-disable-next-line no-console
    console.warn('Betshop settings request returned 401, but not triggering logout (likely timing issue)');
    return; // Don't trigger logout for betshop settings 401s
  }

  try {
    // Perform quick authentication cleanup
    performQuickAuthCleanup(globalConfig.queryClient);

    // Show the 401 error modal if configured
    if (globalConfig.show401Modal) {
      globalConfig.show401Modal();
    }

    // Small delay to allow modal to show before potential redirect
    await new Promise(resolve => setTimeout(resolve, 100));

  } catch (cleanupError) {

    // eslint-disable-next-line no-console
    console.error('cleanupError: ', cleanupError);
    // eslint-disable-next-line no-console
    console.error('error: ', error);
    // Fallback: still try to redirect even if cleanup fails
    if (globalConfig.redirectToLogin) {
      globalConfig.redirectToLogin();
    }
  }
};

/**
 * Global API error interceptor for fetch responses
 */
export const handleApiResponse = async (response: Response): Promise<Response> => {
  if (is401Error(response)) {
    // Clone the response before handling to avoid consuming the body
    const clonedResponse = response.clone();

    // Handle 401 error asynchronously to not block the original request
    setTimeout(() => {
      handle401Error(clonedResponse);
    }, 0);
  }

  return response;
};

/**
 * Global API error interceptor for fetch errors
 */
export const handleApiError = async (error: any): Promise<never> => {
  if (is401Error(error)) {
    // Handle 401 error asynchronously
    setTimeout(() => {
      handle401Error(error);
    }, 0);
  }

  // Re-throw the error to maintain normal error handling flow
  throw error;
};

/**
 * Enhanced fetch wrapper with global error handling
 */
export const fetchWithGlobalErrorHandling = async (
  input: FetchInput,
  init?: FetchInit
): Promise<Response> => {
  try {
    const response = await fetch(input, init);

    // Handle the response through our global handler
    return await handleApiResponse(response);
  } catch (error) {
    // Handle the error through our global handler
    return await handleApiError(error);
  }
};

/**
 * Utility to wrap existing fetch calls with global error handling
 */
export const wrapFetchWithErrorHandling = (
  originalFetch: typeof fetch
): typeof fetch => {
  return async (input: FetchInput, init?: FetchInit): Promise<Response> => {
    try {
      const response = await originalFetch(input, init);
      return await handleApiResponse(response);
    } catch (error) {
      return await handleApiError(error);
    }
  };
};

/**
 * Check and handle API response for 401 errors
 * Use this in existing API query functions
 */
export const checkAndHandle401 = async (response: Response): Promise<Response> => {
  if (!response.ok && response.status === 401) {
    // Handle 401 error
    await handle401Error(response);
  }

  return response;
};

/**
 * Utility for handling errors in React Query mutations and queries
 */
export const handleQueryError = (error: any): void => {
  if (is401Error(error)) {
    handle401Error(error);
  }
};

/**
 * Create a fetch interceptor that can be used to wrap all API calls
 */
export const createFetchInterceptor = () => {
  const originalFetch = window.fetch;

  window.fetch = async (input: FetchInput, init?: FetchInit): Promise<Response> => {
    try {
      const response = await originalFetch(input, init);
      return await handleApiResponse(response);
    } catch (error) {
      return await handleApiError(error);
    }
  };

  // Return a function to restore original fetch if needed
  return () => {
    window.fetch = originalFetch;
  };
};
