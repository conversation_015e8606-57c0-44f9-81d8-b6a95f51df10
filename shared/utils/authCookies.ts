// shared/utils/authCookies.ts - Authentication cookie management utilities
import { User } from '@/shared/types/global';

/**
 * Cookie configuration for authentication
 */
const AUTH_COOKIE_CONFIG = {
  TOKEN_NAME: 'auth-token',
  USER_NAME: 'auth-user',
  MAX_AGE: 7 * 24 * 60 * 60, // 7 days in seconds
  SECURE: process.env.NODE_ENV === 'production',
  SAME_SITE: 'lax' as const,
  HTTP_ONLY: false, // Allow client-side access for compatibility
  PATH: '/',
};

/**
 * Set authentication cookies (client-side)
 */
export function setAuthCookies(token: string, user: User): void {
  try {
    // Set token cookie
    document.cookie = `${AUTH_COOKIE_CONFIG.TOKEN_NAME}=${token}; max-age=${AUTH_COOKIE_CONFIG.MAX_AGE}; path=${AUTH_COOKIE_CONFIG.PATH}; samesite=${AUTH_COOKIE_CONFIG.SAME_SITE}${AUTH_COOKIE_CONFIG.SECURE ? '; secure' : ''}`;
    
    // Set user data cookie (<PERSON><PERSON><PERSON> stringified)
    const userJson = JSON.stringify(user);
    document.cookie = `${AUTH_COOKIE_CONFIG.USER_NAME}=${encodeURIComponent(userJson)}; max-age=${AUTH_COOKIE_CONFIG.MAX_AGE}; path=${AUTH_COOKIE_CONFIG.PATH}; samesite=${AUTH_COOKIE_CONFIG.SAME_SITE}${AUTH_COOKIE_CONFIG.SECURE ? '; secure' : ''}`;
    
    // eslint-disable-next-line no-console
    console.log('✅ Authentication cookies set successfully');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error setting authentication cookies:', error);
  }
}

/**
 * Get authentication token from cookies (client-side)
 */
export function getAuthTokenFromCookies(): string | null {
  try {
    if (typeof document === 'undefined') return null;
    
    const cookies = document.cookie.split(';');
    const tokenCookie = cookies.find(cookie => 
      cookie.trim().startsWith(`${AUTH_COOKIE_CONFIG.TOKEN_NAME}=`)
    );
    
    if (tokenCookie) {
      return tokenCookie.split('=')[1];
    }
    
    return null;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error getting auth token from cookies:', error);
    return null;
  }
}

/**
 * Get user data from cookies (client-side)
 */
export function getUserFromCookies(): User | null {
  try {
    if (typeof document === 'undefined') return null;
    
    const cookies = document.cookie.split(';');
    const userCookie = cookies.find(cookie => 
      cookie.trim().startsWith(`${AUTH_COOKIE_CONFIG.USER_NAME}=`)
    );
    
    if (userCookie) {
      const userJson = decodeURIComponent(userCookie.split('=')[1]);
      return JSON.parse(userJson);
    }
    
    return null;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error getting user from cookies:', error);
    return null;
  }
}

/**
 * Clear authentication cookies (client-side)
 */
export function clearAuthCookies(): void {
  try {
    // Clear token cookie
    document.cookie = `${AUTH_COOKIE_CONFIG.TOKEN_NAME}=; max-age=0; path=${AUTH_COOKIE_CONFIG.PATH}; samesite=${AUTH_COOKIE_CONFIG.SAME_SITE}${AUTH_COOKIE_CONFIG.SECURE ? '; secure' : ''}`;
    
    // Clear user cookie
    document.cookie = `${AUTH_COOKIE_CONFIG.USER_NAME}=; max-age=0; path=${AUTH_COOKIE_CONFIG.PATH}; samesite=${AUTH_COOKIE_CONFIG.SAME_SITE}${AUTH_COOKIE_CONFIG.SECURE ? '; secure' : ''}`;
    
    // eslint-disable-next-line no-console
    console.log('✅ Authentication cookies cleared successfully');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error clearing authentication cookies:', error);
  }
}

/**
 * Check if authentication cookies exist (client-side)
 */
export function hasAuthCookies(): boolean {
  try {
    const token = getAuthTokenFromCookies();
    const user = getUserFromCookies();
    return !!(token && user);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error checking auth cookies:', error);
    return false;
  }
}

/**
 * Sync authentication state from cookies to localStorage
 * This ensures backward compatibility with existing localStorage-based auth
 */
export function syncAuthFromCookies(): { token: string | null; user: User | null } {
  try {
    const token = getAuthTokenFromCookies();
    const user = getUserFromCookies();
    
    if (token && user) {
      // Update localStorage to maintain compatibility
      const authData = {
        state: {
          token,
          user,
          isAuthenticated: true,
          _hasHydrated: true,
          twoStepVerificationRequired: false,
          tempUserFor2FA: null,
          loginMessage: null,
        },
        version: 0,
      };
      
      localStorage.setItem('auth-storage', JSON.stringify(authData));
      
      // eslint-disable-next-line no-console
      console.log('✅ Authentication state synced from cookies to localStorage');
    }
    
    return { token, user };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Error syncing auth from cookies:', error);
    return { token: null, user: null };
  }
}

export { AUTH_COOKIE_CONFIG };
