// shared/utils/userDetailsUtils.ts
// Utility functions for user details page

/**
 * User type mapping for display
 */
export const getUserTypeLabel = (userType: number): string => {
  const userTypeMap: Record<number, string> = {
    1: "Online",
    2: "Kiosk",
    3: "Kiosk & Online",
    4: "Guest"
  };

  return userTypeMap[userType] || "Unknown";
};

/**
 * Get user type with icon and variant for badges
 */
export const getUserTypeInfo = (userType: number) => {
  const userTypeMap: Record<number, {
    label: string;
    variant: "primary" | "secondary" | "success" | "warning" | "danger" | "info";
    icon: string
  }> = {
    1: { label: "Online", variant: "success", icon: "ri-global-line" },
    2: { label: "Kiosk", variant: "info", icon: "ri-computer-line" },
    3: { label: "Kiosk & Online", variant: "primary", icon: "ri-device-line" },
    4: { label: "Guest", variant: "secondary", icon: "ri-user-line" }
  };

  return userTypeMap[userType] || { label: "Unknown", variant: "secondary" as const, icon: "ri-question-line" };
};

/**
 * Format currency to always display as Sri Lankan Rupee
 * If API returns Sri Lankan currency, show that; otherwise convert/display as Sri Lankan currency
 */
export const formatSriLankanCurrency = (amount: number | string | undefined, apiCurrency?: string): string => {
  if (amount === undefined || amount === null) return "N/A";

  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;

  if (isNaN(numAmount)) return "N/A";

  // Always display as Sri Lankan Rupee regardless of API response
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: apiCurrency || "LKR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numAmount);
};

/**
 * Format date for user details display
 */
export const formatUserDate = (dateString: string | null | undefined): string => {
  if (!dateString) return "N/A";

  try {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('error: ', error);
    return "Invalid Date";
  }
};

/**
 * Get time ago string for activity timestamps
 */
export const getTimeAgo = (dateString: string | null | undefined): string => {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return formatUserDate(dateString);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('error: ', error);
    return "N/A";
  }
};

/**
 * Get verification status badge info
 */
export const getVerificationInfo = (verified: boolean) => {
  return {
    label: verified ? "Verified" : "Pending",
    variant: verified ? "success" as const : "warning" as const,
    icon: verified ? "ri-checkbox-circle-fill" : "ri-time-line"
  };
};

/**
 * Get status badge info for user active status
 */
export const getActiveStatusInfo = (active: boolean) => {
  return {
    label: active ? "Active" : "Inactive",
    variant: active ? "success" as const : "danger" as const,
    icon: active ? "ri-check-line" : "ri-close-line"
  };
};
