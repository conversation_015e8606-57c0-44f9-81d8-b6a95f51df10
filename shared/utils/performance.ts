// shared/utils/performance.ts - Performance monitoring and optimization utilities

/**
 * Performance monitoring utilities for tracking Core Web Vitals and other metrics
 */

// Types for performance metrics
export interface PerformanceMetric {
	name: string;
	value: number;
	rating: 'good' | 'needs-improvement' | 'poor';
	timestamp: number;
}

export interface CoreWebVitals {
	CLS?: PerformanceMetric;
	FID?: PerformanceMetric;
	FCP?: PerformanceMetric;
	LCP?: PerformanceMetric;
	TTFB?: PerformanceMetric;
	INP?: PerformanceMetric;
}

// Thresholds for Core Web Vitals (based on Google's recommendations)
const THRESHOLDS = {
	CLS: { good: 0.1, poor: 0.25 },
	FID: { good: 100, poor: 300 },
	FCP: { good: 1800, poor: 3000 },
	LCP: { good: 2500, poor: 4000 },
	TTFB: { good: 800, poor: 1800 },
	INP: { good: 200, poor: 500 },
};

/**
 * Get performance rating based on metric value and thresholds
 */
function getPerformanceRating(metricName: keyof typeof THRESHOLDS, value: number): 'good' | 'needs-improvement' | 'poor' {
	const threshold = THRESHOLDS[metricName];
	if (value <= threshold.good) return 'good';
	if (value <= threshold.poor) return 'needs-improvement';
	return 'poor';
}

/**
 * Create a performance metric object
 */
function createMetric(name: string, value: number): PerformanceMetric {
	return {
		name,
		value,
		rating: getPerformanceRating(name as keyof typeof THRESHOLDS, value),
		timestamp: Date.now(),
	};
}

/**
 * Report Core Web Vitals to analytics
 */
export function reportWebVitals(metric: PerformanceMetric) {
	// Send to analytics service
	// Send to Google Analytics 4
	if (typeof window !== 'undefined' && 'gtag' in window) {
		(window as any).gtag('event', metric.name, {
			value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
			metricRating: metric.rating,
			customParameter: metric.timestamp,
		});
	}

	// Send to custom analytics endpoint
	if (typeof window !== 'undefined') {
		fetch('/api/analytics/performance', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(metric),
		}).catch(() => {
			// Silently fail to avoid affecting user experience
		});
	}
}

/**
 * Initialize performance monitoring
 */
export function initPerformanceMonitoring() {
	if (typeof window === 'undefined') return;

	// Monitor Core Web Vitals using the web-vitals library pattern
	const observer = new PerformanceObserver((list) => {
		for (const entry of list.getEntries()) {
			if (entry.entryType === 'largest-contentful-paint') {
				const metric = createMetric('LCP', entry.startTime);
				reportWebVitals(metric);
			}

			if (entry.entryType === 'first-input') {
				const metric = createMetric('FID', (entry as any).processingStart - entry.startTime);
				reportWebVitals(metric);
			}

			if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
				const metric = createMetric('CLS', (entry as any).value);
				reportWebVitals(metric);
			}
		}
	});

	// Observe different entry types
	try {
		observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
	} catch {
		// Fallback for browsers that don't support all entry types
	}

	// Monitor TTFB
	if ('navigation' in performance) {
		const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
		if (navEntry) {
			const ttfb = navEntry.responseStart - navEntry.requestStart;
			const metric = createMetric('TTFB', ttfb);
			reportWebVitals(metric);
		}
	}

	// Monitor FCP
	const fcpObserver = new PerformanceObserver((list) => {
		for (const entry of list.getEntries()) {
			if (entry.name === 'first-contentful-paint') {
				const metric = createMetric('FCP', entry.startTime);
				reportWebVitals(metric);
				fcpObserver.disconnect();
			}
		}
	});

	try {
		fcpObserver.observe({ entryTypes: ['paint'] });
	} catch {
		// Fallback for browsers that don't support paint timing
	}
}

/**
 * Performance optimization utilities
 */
export const performanceUtils = {
	/**
	 * Preload critical resources
	 */
	preloadResource(href: string, as: string, type?: string) {
		if (typeof document === 'undefined') return;

		const link = document.createElement('link');
		link.rel = 'preload';
		link.href = href;
		link.as = as;
		if (type) link.type = type;
		document.head.appendChild(link);
	},

	/**
	 * Prefetch resources for next navigation
	 */
	prefetchResource(href: string) {
		if (typeof document === 'undefined') return;

		const link = document.createElement('link');
		link.rel = 'prefetch';
		link.href = href;
		document.head.appendChild(link);
	},

	/**
	 * Preconnect to external domains
	 */
	preconnectDomain(href: string, crossorigin = false) {
		if (typeof document === 'undefined') return;

		const link = document.createElement('link');
		link.rel = 'preconnect';
		link.href = href;
		if (crossorigin) link.crossOrigin = 'anonymous';
		document.head.appendChild(link);
	},

	/**
	 * DNS prefetch for external domains
	 */
	dnsPrefetch(href: string) {
		if (typeof document === 'undefined') return;

		const link = document.createElement('link');
		link.rel = 'dns-prefetch';
		link.href = href;
		document.head.appendChild(link);
	},

	/**
	 * Lazy load images with intersection observer
	 */
	lazyLoadImages(selector: string = 'img[data-src]') {
		if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return;

		const images = document.querySelectorAll(selector);
		const imageObserver = new IntersectionObserver((entries) => {
			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					const img = entry.target as HTMLImageElement;
					const src = img.dataset.src;
					if (src) {
						img.src = src;
						img.removeAttribute('data-src');
						imageObserver.unobserve(img);
					}
				}
			});
		});

		images.forEach((img) => imageObserver.observe(img));
	},

	/**
	 * Debounce function for performance optimization
	 */
	debounce<T extends (..._args: any[]) => any>(func: T, wait: number): T {
		let timeout: ReturnType<typeof setTimeout>;
		return ((...args: any[]) => {
			clearTimeout(timeout);
			timeout = setTimeout(() => func.apply(this, args), wait);
		}) as T;
	},

	/**
	 * Throttle function for performance optimization
	 */
	throttle<T extends (..._args: any[]) => any>(func: T, limit: number): T {
		let inThrottle: boolean;
		return ((...args: any[]) => {
			if (!inThrottle) {
				func.apply(this, args);
				inThrottle = true;
				setTimeout(() => inThrottle = false, limit);
			}
		}) as T;
	},

	/**
	 * Measure component render time
	 */
	measureRenderTime(componentName: string, startTime: number) {
		if (typeof window === 'undefined') return;

		const renderTime = performance.now() - startTime;

		// Create a performance metric for render time
		const metric = createMetric('render-time', renderTime);

		// Log render performance in development
		if (process.env.NODE_ENV === 'development') {
			// eslint-disable-next-line no-console
			console.log(`[Performance] ${componentName} render time: ${renderTime.toFixed(2)}ms`);
		}

		// Send to analytics in production
		if (process.env.NODE_ENV === 'production') {
			reportWebVitals({
				...metric,
				name: `render-time-${componentName}`,
			});
		}
	},
};

/**
 * Cache utilities for client-side caching
 */
export const cacheUtils = {
	/**
	 * Simple memory cache with TTL
	 */
	memoryCache: new Map<string, { data: any; expires: number }>(),

	/**
	 * Set cache entry with TTL
	 */
	set(key: string, data: any, ttlMs: number = 300000) { // Default 5 minutes
		const expires = Date.now() + ttlMs;
		this.memoryCache.set(key, { data, expires });
	},

	/**
	 * Get cache entry if not expired
	 */
	get(key: string) {
		const entry = this.memoryCache.get(key);
		if (!entry) return null;

		if (Date.now() > entry.expires) {
			this.memoryCache.delete(key);
			return null;
		}

		return entry.data;
	},

	/**
	 * Clear expired cache entries
	 */
	cleanup() {
		const now = Date.now();
		for (const [key, entry] of this.memoryCache.entries()) {
			if (now > entry.expires) {
				this.memoryCache.delete(key);
			}
		}
	},

	/**
	 * Clear all cache entries
	 */
	clear() {
		this.memoryCache.clear();
	},
};

// Auto cleanup cache every 5 minutes
if (typeof window !== 'undefined') {
	setInterval(() => cacheUtils.cleanup(), 300000);
}
