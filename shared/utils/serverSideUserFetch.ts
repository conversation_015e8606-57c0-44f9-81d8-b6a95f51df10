// shared/utils/serverSideUserFetch.ts - Server-side user data fetching utility
import { cookies } from 'next/headers';
import { UserListFilters, UserListResponse, DEFAULT_USER_FILTERS } from "@/shared/types/user-management-types";

/**
 * Get authentication token from server-side cookies
 */
async function getServerSideAuthToken(): Promise<string | null> {
  try {
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');
    return authToken?.value || null;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error accessing server-side auth token:', error);
    return null;
  }
}

/**
 * Server-side fetch function for user list data
 * Used for SSR to pre-populate initial user data
 * Now supports cookie-based authentication for true SSR
 */
export async function fetchUserListServerSide(
  filters: UserListFilters = DEFAULT_USER_FILTERS
): Promise<UserListResponse | null> {
  try {
    // Get auth token from server-side cookies
    const token = await getServerSideAuthToken();

    if (!token) {

      return null;
    }

    // Use the staging backend URL for server-side fetching
    const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL || process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

    if (!baseUrl) {
      return null;
    }

    const response = await fetch(`${baseUrl}/api/v2/cashier/players`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(filters),
      // Add cache control for server-side requests
      cache: 'no-store', // Always fetch fresh data for SSR
    });

    if (!response.ok) {
      // eslint-disable-next-line no-console
      console.error(`Server-side user fetch failed: ${response.status} ${response.statusText}`);
      return null;
    }

    const data: UserListResponse = await response.json();

    return data;

  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error in server-side user fetch:', error);
    return null;
  }
}

/**
 * Get initial user data for SSR with error handling
 * Returns null for userListResponse to trigger client-side fallback,
 * but provides the initial filters for consistent state
 */
export async function getInitialUserData(): Promise<{
  userListResponse: UserListResponse | null;
  initialFilters: UserListFilters;
}> {
  const initialFilters = { ...DEFAULT_USER_FILTERS };

  try {
    // Since auth is client-side only, always return null for server-side
    // This maintains SSR structure while ensuring client-side auth handling
    const userListResponse = await fetchUserListServerSide(initialFilters);

    return {
      userListResponse, // Will be null, triggering client-side fetch
      initialFilters
    };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to get initial user data:', error);
    return {
      userListResponse: null,
      initialFilters
    };
  }
}
