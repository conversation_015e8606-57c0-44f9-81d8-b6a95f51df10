// shared/utils/envValidation.ts
/**
 * Environment Variable Validation Utilities
 * Centralized validation for environment variables with detailed error reporting
 */

export interface EnvironmentConfig {
  adminBackendUrl: string;
  stagingBackendUrl: string;
  reportingBackendUrl: string;
  nodeEnv: string;
}

/**
 * Validates and returns environment variables with detailed error reporting
 * @throws Error if required environment variables are missing
 */
export const validateEnvironmentVariables = (): EnvironmentConfig => {
  const config = {
    adminBackendUrl: process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL,
    stagingBackendUrl: process.env.NEXT_PUBLIC_STAGING_BACKEND_URL,
    reportingBackendUrl: process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL,
    nodeEnv: process.env.NODE_ENV || "development",
  };

  const missingVars: string[] = [];

  if (!config.adminBackendUrl) {
    missingVars.push("NEXT_PUBLIC_ADMIN_BACKEND_URL");
  }

  if (!config.stagingBackendUrl) {
    missingVars.push("NEXT_PUBLIC_STAGING_BACKEND_URL");
  }

  if (!config.reportingBackendUrl) {
    missingVars.push("NEXT_PUBLIC_REPORTING_BACKEND_URL");
  }

  if (missingVars.length > 0) {

    throw new Error(
      `Environment configuration error: Missing required environment variables: ${missingVars.join(", ")}. ` +
      `Please ensure your .env file is properly configured for the ${config.nodeEnv} environment.`
    );
  }

  return config as EnvironmentConfig;
};

/**
 * Gets the admin backend URL with validation and fallback
 * @throws Error if NEXT_PUBLIC_ADMIN_BACKEND_URL is not configured
 */
export const getAdminBackendUrl = (): string => {
  let adminBackendUrl = process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  // Fallback configuration for production if environment variable is not loaded
  if (!adminBackendUrl && process.env.NODE_ENV === "production") {
    adminBackendUrl = "https://adminapi.ingrandstation.com";
  }

  if (!adminBackendUrl) {
    throw new Error("API configuration error: Admin backend URL is not configured. Please check your environment variables.");
  }

  return adminBackendUrl;
};

/**
 * Gets the staging backend URL with validation and fallback
 * @throws Error if NEXT_PUBLIC_STAGING_BACKEND_URL is not configured
 */
export const getStagingBackendUrl = (): string => {
  let stagingBackendUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL;

  // Fallback configuration for production if environment variable is not loaded
  if (!stagingBackendUrl && process.env.NODE_ENV === "production") {
    // eslint-disable-next-line no-console
    console.warn("NEXT_PUBLIC_STAGING_BACKEND_URL not found in environment, using production fallback");
    stagingBackendUrl = "https://staging-reports-api.8dexsuperadmin.com";
  }

  if (!stagingBackendUrl) {
    // eslint-disable-next-line no-console
    console.error("NEXT_PUBLIC_STAGING_BACKEND_URL is not defined");
    // eslint-disable-next-line no-console
    console.error("Environment:", process.env.NODE_ENV);
    throw new Error("API configuration error: Staging backend URL is not configured. Please check your environment variables.");
  }
  // eslint-disable-next-line no-console
  console.log("Using staging backend URL:", stagingBackendUrl);
  return stagingBackendUrl;
};

/**
 * Gets the reporting backend URL with validation and fallback
 * @throws Error if NEXT_PUBLIC_REPORTING_BACKEND_URL is not configured
 */
export const getReportingBackendUrl = (): string => {
  let reportingBackendUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;

  // Fallback configuration for production if environment variable is not loaded
  if (!reportingBackendUrl && process.env.NODE_ENV === "production") {
    // eslint-disable-next-line no-console
    console.warn("NEXT_PUBLIC_REPORTING_BACKEND_URL not found in environment, using production fallback");
    reportingBackendUrl = "https://reporting.ingrandstation.com";
  }

  if (!reportingBackendUrl) {
    throw new Error("API configuration error: Reporting backend URL is not configured. Please check your environment variables.");
  }

  return reportingBackendUrl;
};

/**
 * Logs environment configuration for debugging (safe for production)
 */
export const logEnvironmentConfig = (): void => {
  // Development-only environment logging would go here
};
