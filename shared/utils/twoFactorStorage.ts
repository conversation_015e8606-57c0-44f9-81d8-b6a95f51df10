// shared/utils/twoFactorStorage.ts
// Utility functions for managing 2FA state in session storage

interface TwoFactorState {
  twoStepVerificationRequired: boolean;
  tempUserFor2FA: { id: number; email: string } | null;
  loginMessage: string | null;
}

const TWO_FACTOR_STORAGE_KEY = 'temp-2fa-state';

/**
 * Save 2FA state to session storage
 * Session storage is cleared when the browser tab is closed, which is perfect for temporary 2FA state
 */
export const saveTwoFactorState = (state: TwoFactorState): void => {
  if (typeof window !== 'undefined') {
    try {
      sessionStorage.setItem(TWO_FACTOR_STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to save 2FA state to session storage:', error);
    }
  }
};

/**
 * Load 2FA state from session storage
 */
export const loadTwoFactorState = (): TwoFactorState | null => {
  if (typeof window !== 'undefined') {
    try {
      const stored = sessionStorage.getItem(TWO_FACTOR_STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to load 2FA state from session storage:', error);
    }
  }
  return null;
};

/**
 * Clear 2FA state from session storage
 */
export const clearTwoFactorState = (): void => {
  if (typeof window !== 'undefined') {
    try {
      sessionStorage.removeItem(TWO_FACTOR_STORAGE_KEY);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to clear 2FA state from session storage:', error);
    }
  }
};

/**
 * Check if 2FA state exists in session storage
 */
export const hasTwoFactorState = (): boolean => {
  if (typeof window !== 'undefined') {
    try {
      return sessionStorage.getItem(TWO_FACTOR_STORAGE_KEY) !== null;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to check 2FA state in session storage:', error);
    }
  }
  return false;
};
