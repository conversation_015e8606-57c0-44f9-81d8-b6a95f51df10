// shared/utils/serverSideReportFetch.ts - Server-side fetch utilities for report data
import {
  BetReportFilters,
  BetReportResponse,
  CashierReportFilters,
  CashierReportResponse,
  DEFAULT_BET_REPORT_FILTERS,
  DEFAULT_CASHIER_REPORT_FILTERS,
  DEFAULT_FINANCIAL_REPORT_FILTERS,
  FinancialReportFilters,
  FinancialReportResponse
} from '@/shared/types/report-types';
import { cookies } from 'next/headers';
import { transformActionTypeForApi } from '@/shared/config/transactionTypes';

/**
 * Generate the required timePeriod parameter with exact encoding format
 * Format: %7B%22startDate%22:%222025-06-25%2000:00:00%22,%22endDate%22:%222025-06-25%2023:59:59%22%7D
 */
const generateTimePeriod = (startDate?: string, endDate?: string): string => {
  // Use the same date as the working method (2025-06-25) if no dates provided
  // This ensures we query data that actually exists in the system
  const defaultStartDate = startDate || '2025-06-25 00:00:00';
  const defaultEndDate = endDate || '2025-06-25 23:59:59';

  // Use the exact encoding format from the working API example
  // Working example: %7B%22startDate%22:%222025-06-25%2000:00:00%22,%22endDate%22:%222025-06-25%2023:59:59%22%7D
  // Key: Only encode spaces as %20, keep colons as : (not %3A)
  return `%7B%22startDate%22:%22${defaultStartDate.replace(/ /g, '%20')}%22,%22endDate%22:%22${defaultEndDate.replace(/ /g, '%20')}%22%7D`;
};

/**
 * Get authentication token from server-side cookies
 */
async function getServerSideAuthToken(): Promise<string | null> {
  try {
    const cookieStore = await cookies();
    const authToken = cookieStore.get('authToken')?.value || cookieStore.get('token')?.value;
    return authToken || null;
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Failed to get server-side auth token:', error);
    return null;
  }
}

/**
 * Server-side fetch function for cashier report data
 * Used for SSR to pre-populate initial report data
 */
export async function fetchCashierReportServerSide(
  filters: CashierReportFilters = DEFAULT_CASHIER_REPORT_FILTERS
): Promise<CashierReportResponse | null> {
  try {
    // Get auth token from server-side cookies
    const token = await getServerSideAuthToken();

    if (!token) {
      //eslint-disable-next-line no-console
      console.log('No auth token found for server-side bet report fetch');
      return null;
    }

    // Use the staging backend URL for server-side fetching
    const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL;

    if (!baseUrl) {
      //eslint-disable-next-line no-console
      console.error('NEXT_PUBLIC_STAGING_BACKEND_URL not configured for server-side fetch');
      return null;
    }

    // Ensure required timePeriod parameter is always present and transform actionType
    const serverFilters = {
      ...filters,
      timePeriod: filters.timePeriod || generateTimePeriod(),
      // Transform actionType from numeric value to title string for API
      actionType: filters.actionType ? transformActionTypeForApi(filters.actionType) : undefined
    };

    // Build query string from filters
    const queryParams = new URLSearchParams();
    let actionTypeParam = '';

    Object.entries(serverFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (key === 'actionType') {
          // Handle actionType specially - don't encode the array brackets and quotes
          actionTypeParam = `actionType=${value.toString()}`;
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });

    // Combine regular params with special actionType param
    let queryString = queryParams.toString();
    if (actionTypeParam) {
      queryString = queryString ? `${queryString}&${actionTypeParam}` : actionTypeParam;
    }

    const response = await fetch(`${baseUrl}/api/v2/admin/transactions?${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      cache: 'no-store', // Ensure fresh data for SSR
    });

    if (!response.ok) {
      //eslint-disable-next-line no-console
      console.error(`Server-side cashier report fetch failed: ${response.status}`);
      return null;
    }

    const apiResponse = await response.json();

    // Transform to standardized format
    const result = apiResponse.data?.data?.result;
    return {
      data: result?.rows || [],
      success: apiResponse.success,
      message: apiResponse.data?.message || '',
      errors: apiResponse.errors || [],
      count: result?.count,
      totalPages: result?.total_pages,
      currentPage: result?.current_page
    };
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Server-side cashier report fetch error:', error);
    return null;
  }
}

/**
 * Server-side fetch function for bet report data
 * Used for SSR to pre-populate initial report data
 */
export async function fetchBetReportServerSide(
  filters: BetReportFilters = DEFAULT_BET_REPORT_FILTERS
): Promise<BetReportResponse | null> {
  try {
    // Get auth token from server-side cookies
    const token = await getServerSideAuthToken();

    if (!token) {
      //eslint-disable-next-line no-console
      console.log('No auth token found for server-side bet report fetch');
      return null;
    }

    // Use the reporting backend URL for server-side fetching
    const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;

    if (!baseUrl) {
      //eslint-disable-next-line no-console
      console.error('NEXT_PUBLIC_REPORTING_BACKEND_URL not configured for server-side fetch');
      return null;
    }

    // Build request body
    const requestBody: any = {
      page: filters.page,
      limit: filters.limit,
      startDate: filters.startDate,
      endDate: filters.endDate
    };

    // Add optional fields only if they have values
    if (filters.transactionId) {
      requestBody.transactionId = filters.transactionId;
    }
    if (filters.playerId) {
      requestBody.playerId = filters.playerId;
    }
    if (filters.marketId) {
      requestBody.marketId = filters.marketId;
    }

    const response = await fetch(`${baseUrl}/api/v2/cashier/betWinReport`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestBody),
      cache: 'no-store', // Ensure fresh data for SSR
    });

    if (!response.ok) {
      //eslint-disable-next-line no-console
      console.error(`Server-side bet report fetch failed: ${response.status}`);
      return null;
    }

    const apiResponse = await response.json();

    // Calculate totals from the data if available
    const data = apiResponse.data || [];
    const totalBetAmount = data.reduce((sum: number, bet: any) => sum + (parseFloat(bet.betAmount) || 0), 0);
    const totalWinAmount = data.reduce((sum: number, bet: any) => sum + (parseFloat(bet.winAmount) || 0), 0);
    const ggr = totalBetAmount - totalWinAmount;

    // Ensure the response has the expected structure
    return {
      data,
      success: apiResponse.success,
      message: apiResponse.message || '',
      errors: apiResponse.errors || [],
      totalBetAmount,
      totalWinAmount,
      ggr,
      count: apiResponse.data?.length || 0,
      totalPages: Math.ceil((apiResponse.data?.length || 0) / filters.limit),
      currentPage: filters.page
    };
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Server-side bet report fetch error:', error);
    return null;
  }
}

/**
 * Server-side fetch function for financial report data
 * Used for SSR to pre-populate initial report data
 */
export async function fetchFinancialReportServerSide(
  filters: FinancialReportFilters = DEFAULT_FINANCIAL_REPORT_FILTERS
): Promise<FinancialReportResponse | null> {
  try {
    // Get auth token from server-side cookies
    const token = await getServerSideAuthToken();

    if (!token) {
      return null;
    }

    // Use the reporting backend URL for server-side fetching
    const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;

    if (!baseUrl) {
      //eslint-disable-next-line no-console
      console.error('NEXT_PUBLIC_REPORTING_BACKEND_URL not configured for server-side fetch');
      return null;
    }

    // Ensure actionCategory is always set to 'financial', timePeriod is present, and transform actionType
    const serverFilters = {
      ...filters,
      actionCategory: 'financial',
      timePeriod: filters.timePeriod || generateTimePeriod(),
      // Transform actionType from numeric value to title string for API
      actionType: filters.actionType ? transformActionTypeForApi(filters.actionType) : undefined
    };

    // Build query string from filters
    const queryParams = new URLSearchParams();
    let actionTypeParam = '';

    Object.entries(serverFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (key === 'actionType') {
          // Handle actionType specially - don't encode the array brackets and quotes
          actionTypeParam = `actionType=${value.toString()}`;
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });

    // Combine regular params with special actionType param
    let queryString = queryParams.toString();
    if (actionTypeParam) {
      queryString = queryString ? `${queryString}&${actionTypeParam}` : actionTypeParam;
    }

    const response = await fetch(`${baseUrl}/api/v2/admin/transactions?${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      cache: 'no-store', // Ensure fresh data for SSR
    });

    if (!response.ok) {
      //eslint-disable-next-line no-console
      console.error(`Server-side financial report fetch failed: ${response.status}`);
      return null;
    }

    const apiResponse = await response.json();

    // Transform to standardized format
    const result = apiResponse.data?.data?.result;
    return {
      data: result?.rows || [],
      success: apiResponse.success,
      message: apiResponse.data?.message || '',
      errors: apiResponse.errors || [],
      count: result?.count,
      totalPages: result?.total_pages,
      currentPage: result?.current_page
    };
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Server-side financial report fetch error:', error);
    return null;
  }
}

/**
 * Get initial data for all reports
 */
export async function getInitialReportData() {
  return {
    cashierReport: await fetchCashierReportServerSide(),
    betReport: await fetchBetReportServerSide(),
    financialReport: await fetchFinancialReportServerSide()
  };
}
