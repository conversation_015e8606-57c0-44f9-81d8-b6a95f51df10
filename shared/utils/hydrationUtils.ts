// shared/utils/hydrationUtils.ts - Utilities to handle hydration mismatches and browser extension interference
"use client";

import { useEffect, useState } from 'react';

/**
 * Hook to handle hydration-safe rendering
 * Prevents hydration mismatches by ensuring client-side only rendering for dynamic content
 */
export function useHydrationSafe() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated;
}

/**
 * Browser extension attributes that commonly cause hydration mismatches
 * These are added by security software, ad blockers, and other browser extensions
 */
const BROWSER_EXTENSION_ATTRIBUTES = [
  'bis_skin_checked',     // Bitdefender  // cspell:disable-line
  'data-adblock-key',     // AdBlock
  'data-ublock',          // uBlock Origin  // cspell:disable-line
  'data-ghostery',        // Ghostery  // cspell:disable-line
  'data-avast',           // Avast
  'data-kaspersky',       // <PERSON><PERSON><PERSON>  // cspell:disable-line
  'data-mcafee',          // McAfee  // cspell:disable-line
  'data-norton',          // Norton
  'data-malwarebytes',    // Malwarebytes  // cspell:disable-line
  'data-extension-id',    // Generic extension marker
];

/**
 * Clean browser extension attributes from DOM elements
 * This helps prevent hydration mismatches caused by browser extensions
 */
export function cleanBrowserExtensionAttributes(element: Element): void {
  BROWSER_EXTENSION_ATTRIBUTES.forEach(attr => {
    if (element.hasAttribute(attr)) {
      element.removeAttribute(attr);
    }
  });

  // Also clean from child elements
  const children = element.querySelectorAll('*');
  children.forEach(child => {
    BROWSER_EXTENSION_ATTRIBUTES.forEach(attr => {
      if (child.hasAttribute(attr)) {
        child.removeAttribute(attr);
      }
    });
  });
}

/**
 * Initialize DOM cleaning for browser extension attributes
 * Call this in your root layout or main component
 */
export function initializeDOMCleaning(): void {
  if (typeof window === 'undefined') return;

  // Clean existing attributes
  const cleanExistingAttributes = () => {
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      BROWSER_EXTENSION_ATTRIBUTES.forEach(attr => {
        if (element.hasAttribute(attr)) {
          element.removeAttribute(attr);
        }
      });
    });
  };

  // Clean on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', cleanExistingAttributes);
  } else {
    cleanExistingAttributes();
  }

  // Set up mutation observer to clean attributes as they're added
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes') {
        const target = mutation.target as Element;
        const attributeName = mutation.attributeName;

        if (attributeName && BROWSER_EXTENSION_ATTRIBUTES.includes(attributeName)) {
          target.removeAttribute(attributeName);
        }
      } else if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            cleanBrowserExtensionAttributes(node as Element);
          }
        });
      }
    });
  });

  // Start observing
  observer.observe(document.body, {
    attributes: true,
    childList: true,
    subtree: true,
    attributeFilter: BROWSER_EXTENSION_ATTRIBUTES,
  });

  // Clean up on page unload
  window.addEventListener('beforeunload', () => {
    observer.disconnect();
  });
}

/**
 * React component wrapper that suppresses hydration warnings for elements
 * that might be modified by browser extensions
 *
 * Note: This component is temporarily disabled due to TypeScript compilation issues.
 * Use suppressHydrationWarning={true} directly on elements that need it.
 */
// export function HydrationSafeWrapper({ children, className, ...props }: any) {
//   const isHydrated = useHydrationSafe();
//   return (
//     <div className={className} suppressHydrationWarning={true} {...props}>
//       {children}
//     </div>
//   );
// }

/**
 * Custom hook to handle client-side only operations
 * Useful for preventing hydration mismatches with dynamic content
 */
export function useClientSideOnly<T>(
  clientSideValue: T,
  serverSideValue: T
): T {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient ? clientSideValue : serverSideValue;
}

/**
 * Utility to suppress hydration warnings on specific elements
 * Use this for elements that are known to be modified by browser extensions
 */
export const suppressHydrationProps = {
  suppressHydrationWarning: true,
} as const;
