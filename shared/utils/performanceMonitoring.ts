// shared/utils/performanceMonitoring.ts - Performance monitoring utilities for development
import { useEffect, useRef } from 'react';

/**
 * Performance monitoring configuration
 */
interface PerformanceConfig {
  enabled: boolean;
  logToConsole: boolean;
  trackRerenders: boolean;
  trackLoadTimes: boolean;
}

const defaultConfig: PerformanceConfig = {
  enabled: process.env.NODE_ENV === 'development',
  logToConsole: true,
  trackRerenders: true,
  trackLoadTimes: true,
};

/**
 * Hook to track component re-renders in development mode
 * Helps identify unnecessary re-renders and validate optimization effectiveness
 */
export const useRenderTracker = (componentName: string, props?: Record<string, any>) => {
  const renderCount = useRef(0);
  const prevProps = useRef(props);

  useEffect(() => {
    if (!defaultConfig.enabled || !defaultConfig.trackRerenders) return;

    renderCount.current += 1;

    if (defaultConfig.logToConsole) {
      // eslint-disable-next-line no-console
      console.group(`🔄 ${componentName} - Render #${renderCount.current}`);

      if (props && prevProps.current) {
        const changedProps = Object.keys(props).filter(
          key => props[key] !== prevProps.current?.[key]
        );

        if (changedProps.length > 0) {
          // eslint-disable-next-line no-console
          console.log('📝 Changed props:', changedProps);
          changedProps.forEach(key => {
            // eslint-disable-next-line no-console
            console.log(`  ${key}:`, prevProps.current?.[key], '→', props[key]);
          });
        } else {
          // eslint-disable-next-line no-console
          console.log('⚠️ No props changed - potential unnecessary re-render');
        }
      }

      // eslint-disable-next-line no-console
      console.groupEnd();
    }

    prevProps.current = props;
  });

  return renderCount.current;
};

/**
 * Hook to track page load performance
 */
export const useLoadTimeTracker = (pageName: string) => {
  useEffect(() => {
    if (!defaultConfig.enabled || !defaultConfig.trackLoadTimes) return;

    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const loadTime = endTime - startTime;

      if (defaultConfig.logToConsole) {
        // eslint-disable-next-line no-console
        console.log(`⏱️ ${pageName} load time: ${loadTime.toFixed(2)}ms`);
      }
    };
  }, [pageName]);
};

/**
 * Hook to track pagination performance
 */
export const usePaginationTracker = () => {
  const paginationStartTime = useRef<number | null>(null);

  const startPaginationTimer = () => {
    if (!defaultConfig.enabled) return;
    paginationStartTime.current = performance.now();
  };

  const endPaginationTimer = (page: number) => {
    if (!defaultConfig.enabled || !paginationStartTime.current) return;

    const endTime = performance.now();
    const paginationTime = endTime - paginationStartTime.current;

    if (defaultConfig.logToConsole) {
      // eslint-disable-next-line no-console
      console.log(`📄 Pagination to page ${page}: ${paginationTime.toFixed(2)}ms`);
    }

    paginationStartTime.current = null;
  };

  return { startPaginationTimer, endPaginationTimer };
};

/**
 * Performance metrics collector
 */
class PerformanceMetrics {
  private static instance: PerformanceMetrics;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMetrics {
    if (!PerformanceMetrics.instance) {
      PerformanceMetrics.instance = new PerformanceMetrics();
    }
    return PerformanceMetrics.instance;
  }

  recordMetric(name: string, value: number) {
    if (!defaultConfig.enabled) return;

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  getMetrics(name: string) {
    return this.metrics.get(name) || [];
  }

  getAverageMetric(name: string) {
    const values = this.getMetrics(name);
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  logSummary() {
    if (!defaultConfig.enabled || !defaultConfig.logToConsole) return;

    // eslint-disable-next-line no-console
    console.group('📊 Performance Summary');
    this.metrics.forEach((values, name) => {
      const avg = this.getAverageMetric(name);
      const min = Math.min(...values);
      const max = Math.max(...values);
      // eslint-disable-next-line no-console
      console.log(`${name}: avg=${avg.toFixed(2)}ms, min=${min.toFixed(2)}ms, max=${max.toFixed(2)}ms, samples=${values.length}`);
    });
    // eslint-disable-next-line no-console
    console.groupEnd();
  }

  clearMetrics() {
    this.metrics.clear();
  }
}

export const performanceMetrics = PerformanceMetrics.getInstance();

/**
 * Performance tracking utility for components
 * Note: Use this sparingly as it can impact performance if overused
 */
export const createPerformanceTracker = (componentName: string) => {
  return {
    logRender: (props?: Record<string, any>) => {
      if (defaultConfig.enabled && defaultConfig.logToConsole) {
        // eslint-disable-next-line no-console
        console.log(`🔄 ${componentName} rendered with props:`, props);
      }
    }
  };
};

/**
 * Utility to measure function execution time
 */
export const measureExecutionTime = async <T>(
  fn: () => Promise<T> | T,
  name: string
): Promise<T> => {
  if (!defaultConfig.enabled) {
    return await fn();
  }

  const startTime = performance.now();
  const result = await fn();
  const endTime = performance.now();
  const executionTime = endTime - startTime;

  performanceMetrics.recordMetric(name, executionTime);

  if (defaultConfig.logToConsole) {
    // eslint-disable-next-line no-console
    console.log(`⚡ ${name}: ${executionTime.toFixed(2)}ms`);
  }

  return result;
};

/**
 * Simple profiler hook for tracking component performance
 * Use this instead of ProfilerWrapper for simpler implementation
 */
export const useProfiler = (id: string) => {
  const renderCount = useRef(0);
  const startTime = useRef<number>(0);

  useEffect(() => {
    if (!defaultConfig.enabled) return;

    renderCount.current += 1;
    startTime.current = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime.current;

      performanceMetrics.recordMetric(`${id}-render`, duration);

      if (defaultConfig.logToConsole) {
        // eslint-disable-next-line no-console
        console.log(`🎯 ${id} render #${renderCount.current}: ${duration.toFixed(2)}ms`);
      }
    };
  });

  return renderCount.current;
};

const PerformanceMonitoring = {
  useRenderTracker,
  useLoadTimeTracker,
  usePaginationTracker,
  createPerformanceTracker,
  measureExecutionTime,
  useProfiler,
  performanceMetrics,
};

export default PerformanceMonitoring;
