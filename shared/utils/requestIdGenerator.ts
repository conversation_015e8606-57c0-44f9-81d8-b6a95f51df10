// shared/utils/requestIdGenerator.ts
// Utility for generating and recreating request IDs

/**
 * Generate a UUID v4 format request ID
 * This matches the format of the lost request IDs: 5e9c2865-c215-4a73-b035-7667108a2722
 */
export function generateRequestId(): string {
  // Use crypto.randomUUID if available (modern browsers and Node.js 16+)
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // Fallback UUID v4 generation for older environments
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Validate if a string is a valid UUID format
 */
export function isValidRequestId(id: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * Lost request IDs that need to be recreated
 */
export const LOST_REQUEST_IDS = [
  '5e9c2865-c215-4a73-b035-7667108a2722',
  '998adec6-75db-423f-a6a0-cdf812db562b',
  '3308de12-03ce-4b47-9c63-7946e3105df9'
] as const;

/**
 * Request ID recreation data structure
 */
export interface RequestIdRecreationData {
  originalId: string;
  newId: string;
  requestType: 'transaction' | 'bet' | 'user_creation' | 'wallet_transaction' | 'game_launch' | 'unknown';
  timestamp: string;
  status: 'pending' | 'recreated' | 'failed';
  metadata?: {
    userId?: string;
    amount?: number;
    transactionType?: string;
    description?: string;
  };
}

/**
 * Generate new request IDs to replace the lost ones
 */
export function generateReplacementRequestIds(): RequestIdRecreationData[] {
  return LOST_REQUEST_IDS.map(originalId => ({
    originalId,
    newId: generateRequestId(),
    requestType: 'unknown', // Will need to be determined based on context
    timestamp: new Date().toISOString(),
    status: 'pending' as const,
    metadata: {
      description: `Replacement for lost request ID: ${originalId}`
    }
  }));
}

/**
 * Create a mapping of old to new request IDs
 */
export function createRequestIdMapping(): Record<string, string> {
  const replacements = generateReplacementRequestIds();
  return replacements.reduce((acc, item) => {
    acc[item.originalId] = item.newId;
    return acc;
  }, {} as Record<string, string>);
}

/**
 * Request recreation payload for API calls
 */
export interface RequestRecreationPayload {
  requestIds: RequestIdRecreationData[];
  recreationType: 'generate_new' | 'restore_original' | 'map_existing';
  context?: {
    userId?: string;
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    requestType?: string;
  };
}

/**
 * Generate a complete request recreation payload
 */
export function generateRequestRecreationPayload(
  context?: RequestRecreationPayload['context']
): RequestRecreationPayload {
  return {
    requestIds: generateReplacementRequestIds(),
    recreationType: 'generate_new',
    context: {
      dateRange: {
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        endDate: new Date().toISOString()
      },
      ...context
    }
  };
}
