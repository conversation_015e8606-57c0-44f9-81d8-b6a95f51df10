import { create } from 'zustand';

interface SessionTimeoutState {
    tenantId?: number;
    betshopSettings?: any;
    setTenantId: (tenantId: number) => void;
    setBetshopSettings: (settings: any) => void;
    clearSession: () => void;
}

export const useSessionTimeoutStore = create<SessionTimeoutState>((set) => ({
    tenantId: undefined,
    betshopSettings: undefined,
    setTenantId: (tenantId) => set({ tenantId }),
    setBetshopSettings: (settings) => set({ betshopSettings: settings }),
    clearSession: () => set({ tenantId: undefined, betshopSettings: undefined }),
}));
