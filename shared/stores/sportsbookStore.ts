// shared/stores/sportsbookStore.ts
import { create } from 'zustand';
import {
  TurboStarsGameLaunchResponse,
  CashierTurboPlaceBetDetails
} from '@/shared/types/user-management-types';
import { useAuthStore } from './authStore';

export interface SportsbookState {
  // Sportsbook visibility and state
  isOpen: boolean;
  isLoading: boolean;
  error: string | null;

  // Sportsbook data
  url: string | null;
  userName: string | null;
  response: TurboStarsGameLaunchResponse | null;

  // WebSocket and bet notifications
  isWebSocketConnected: boolean;
  lastBetNotification: CashierTurboPlaceBetDetails | null;
  betNotificationCount: number;

  // Bet details popup state
  isBetPopupOpen: boolean;
  currentBetDetails: CashierTurboPlaceBetDetails | null;
  currentUserId: number | null;

  // Actions
  openSportsbook: (_response: TurboStarsGameLaunchResponse, _userName: string, _userId?: number) => void;
  closeSportsbook: () => void;
  setLoading: (_loading: boolean) => void;
  setError: (_error: string | null) => void;
  clearError: () => void;

  // WebSocket actions
  setWebSocketConnected: (_connected: boolean) => void;
  addBetNotification: (_notification: CashierTurboPlaceBetDetails) => void;
  clearBetNotifications: () => void;

  // Bet popup actions
  /** Show the bet details popup with the provided bet information */
  showBetPopup: (_betDetails: CashierTurboPlaceBetDetails) => void;
  /** Hide the bet details popup and clear current bet details */
  hideBetPopup: () => void;
  /** Set the current user ID for bet notification filtering */
  setCurrentUserId: (_userId: number | null) => void;
}

export const useSportsbookStore = create<SportsbookState>((set, _get) => ({
  // Initial state
  isOpen: false,
  isLoading: false,
  error: null,
  url: null,
  userName: null,
  response: null,

  // WebSocket state
  isWebSocketConnected: false,
  lastBetNotification: null,
  betNotificationCount: 0,

  // Bet popup state
  isBetPopupOpen: false,
  currentBetDetails: null,
  currentUserId: null,

  // Actions
  openSportsbook: (response: TurboStarsGameLaunchResponse, userName: string, userId?: number) => {
    // Get userId from parameter or auth store
    const currentUserId = userId || useAuthStore.getState().user?.id || null;

    set({
      isOpen: true,
      url: response.data.url,
      userName,
      response,
      error: null,
      isLoading: false,
      currentUserId, // Set the userId for WebSocket connection
    });
  },

  closeSportsbook: () => {
    set({
      isOpen: false,
      url: null,
      userName: null,
      response: null,
      error: null,
      isLoading: false,
      // Clear popup state when sportsbook closes
      isBetPopupOpen: false,
      currentBetDetails: null,
      currentUserId: null, // Clear the userId when sportsbook closes
    });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({
      error,
      isLoading: false,
    });
  },

  clearError: () => {
    set({ error: null });
  },

  // WebSocket actions
  setWebSocketConnected: (connected: boolean) => {
    set({ isWebSocketConnected: connected });
  },

  addBetNotification: (notification: CashierTurboPlaceBetDetails) => {
    set(state => ({
      lastBetNotification: notification,
      betNotificationCount: state.betNotificationCount + 1
    }));
  },

  clearBetNotifications: () => {
    set({
      lastBetNotification: null,
      betNotificationCount: 0
    });
  },

  // Bet popup actions
  /**
   * Show the bet details popup with the provided bet information
   * @param betDetails - The bet details to display in the popup
   */
  showBetPopup: (betDetails: CashierTurboPlaceBetDetails) => {
    set({
      isBetPopupOpen: true,
      currentBetDetails: betDetails,
    });
  },

  /**
   * Hide the bet details popup and clear current bet details
   */
  hideBetPopup: () => {
    set({
      isBetPopupOpen: false,
      currentBetDetails: null,
    });
  },

  /**
   * Set the current user ID for bet notification filtering
   * @param userId - The current user's ID or null to clear
   */
  setCurrentUserId: (userId: number | null) => {
    set({
      currentUserId: userId,
    });
  },
}));
