'use client';
import React, { useState, useCallback } from 'react';
import { useAbsoluteSessionTimeout } from './useBetshopSettingsTimeout';
import { useAuthStore } from './authStore';
import { useRouter } from 'next/navigation';
import { AuthErrorModal } from '@/shared/components/ui-elements/alerts';
import { useBetshopSettingsLoader } from '@/shared/hooks/business/useBetshopSettingsLoader';

/**
 * Component that handles absolute session timeout based on login timestamp.
 * Shows a modal when the session expires and redirects to login page.
 */
export function SessionTimeoutHandler() {
    const [modalOpen, setModalOpen] = useState(false);
    const router = useRouter();

    // Ensure betshop settings are loaded for session timeout functionality
    useBetshopSettingsLoader({
        initialDelay: 1000,
        maxRetries: 3,
        autoLoad: true
    });

    // Use stable callbacks to prevent unnecessary re-renders
    const handleTimeout = useCallback(() => {
        setModalOpen(true);
    }, []);

    const handleClose = useCallback(() => {
        setModalOpen(false);
        useAuthStore.getState().clearAuth();
        router.replace('/authentication/sign-in');
    }, [router]);

    // Use the new absolute timeout hook that doesn't reset on activity
    useAbsoluteSessionTimeout(handleTimeout, modalOpen);

    return (
        <AuthErrorModal
            isOpen={modalOpen}
            onClose={handleClose}
        />
    );
}
