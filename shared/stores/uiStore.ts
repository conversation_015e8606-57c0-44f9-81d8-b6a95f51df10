// shared/stores/uiStore.ts
import { create } from 'zustand';

export interface UIState {
  // Essential properties for simplified theme system
  lang: string;
  dir: string;
  class: string; // light or dark theme

  // Layout properties (forced to horizontal)
  dataMenuStyles: string;
  dataNavLayout: string;
  dataHeaderStyles: string;
  dataVerticalStyle: string;
  toggled: string;
  dataNavStyle: string;
  dataPageStyle: string;
  dataWidth: string;
  dataMenuPosition: string;
  dataHeaderPosition: string;

  // System properties
  loader: string;
  iconOverlay: string;

  // Background colors (simplified)
  bodyBg: string;
  darkBg: string;
  lightRgb: string;
  inputBorder: string;
  gray: string;

  // Removed color customization properties
  colorPrimaryRgb: string;
  PrimaryRgb: string;
  bgImg: string;
  iconText: string;

  body: {
    class: string;
  };
  ThemeChanger: (value: Partial<UIState>) => void;
}

export const useUIStore = create<UIState>((set) => ({
  // Essential properties for simplified theme system
  lang: "en",
  dir: "ltr",
  class: "dark", // Default to dark theme

  // Layout properties (forced to horizontal)
  dataMenuStyles: "dark",
  dataNavLayout: "horizontal", // Force horizontal layout permanently
  dataHeaderStyles: "dark",
  dataVerticalStyle: "overlay",
  toggled: "",
  dataNavStyle: "",
  dataPageStyle: "regular",
  dataWidth: "fullwidth",
  dataMenuPosition: "fixed",
  dataHeaderPosition: "fixed",

  // System properties
  loader: "disable",
  iconOverlay: "",

  // Background colors (simplified dark theme)
  bodyBg: "29 29 29",              // Layer 1: Main body background (#1D1D1D)
  darkBg: "15 15 15",              // Layer 2: Section background (#0F0F0F)
  lightRgb: "15 15 15",            // Layer 2: Navigation background (#0F0F0F)
  inputBorder: "rgba(255,255,255,0.1)",
  gray: "rgba(255,255,255,0.1)",

  // Simplified color properties (no customization)
  colorPrimaryRgb: "",
  PrimaryRgb: "",
  bgImg: "",
  iconText: "",

  body: {
    class: ""
  },
  ThemeChanger: (value) => set((state) => ({ ...state, ...value })),
}));
