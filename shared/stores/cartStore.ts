// shared/stores/cartStore.ts
import { create } from 'zustand';

interface ProductData {
  [key: string]: string | number | boolean;
}

interface Product {
  id: number;
  productpicture: string;
  title: string;
  status: string;
  class: string;
  rating: string;
  price: string;
  discount: string;
  views?: string;
  discount1: string;
  size: string;
  color: string;
  stock: string;
  data?: ProductData;
  stockColor: string;
  images: string;
  badge?: string;
  badgeColor?: string;
  quantity?: number;
}

interface CartState {
  selectedItem: Product | null;
  wishlist: Product[];
  cart: Product[];
  count: number;
  checkoutItems: Product[];
  products: Product[]; // Static product list, if from API, move to TanStack Query
  setSelectedItem: (item: Product | null) => void;
  addToWishlist: (item: Product, id: number) => void;
  removeFromWishlist: (id: number) => void;
  addToCart: (item: Product) => void;
  removeFromCart: (id: number) => void;
  updateCartQuantity: (id: number, quantity: number) => void;
  addToCheckout: (items: Product[]) => void;
  buyNow: (item: Product) => void;
}

export const useCartStore = create<CartState>((set, _get) => ({
  selectedItem: null,
  wishlist: [],
  cart: [],
  count: 0,
  checkoutItems: [],
  products: [
    // Your provided product data goes here
    {
      id: 1,
      productpicture: "../../../assets/images/ecommerce/png/28.png",
      title: "Lightweight Sneakers",
      status: "Stealth Series",
      class: "primarytint1color",
      rating: "(874)",
      price: "$771",
      discount: "$880",
      discount1: "12%",
      size: "M",
      color: "primarytint3color",
      stock: "In Stock",
      stockColor: "success",
      images: "../../../assets/img/ecommerce/products/1.png",
      badge: 'Trending',
      badgeColor: 'info'
    },
    {
      id: 2,
      productpicture: "../../../assets/images/ecommerce/png/12.png",
      title: " Kids' Party Wear Frock",
      status: "Twinkle Twirl",
      class: "danger",
      rating: "(231)",
      views: "23,123",
      price: "$236",
      discount: "$267",
      discount1: "15%",
      size: "L",
      color: "info",
      stock: "Out Of Stock",
      stockColor: "danger",
      images: "../../../assets/img/ecommerce/products/2.png"
    },
    {
      id: 3,
      productpicture: "../../../assets/images/ecommerce/png/29.png",
      title: "Ladies' Slim Bag",
      status: "Sleek Elegance",
      class: "success",
      rating: "(110)",
      views: "35,586",
      price: "$124",
      discount: "$214",
      discount1: "24%",
      size: "M",
      color: "primarytint1color",
      stock: "In Stock",
      stockColor: "success",
      images: "../../../assets/img/ecommerce/products/3.png"
    },
    {
      id: 4,
      productpicture: "../../../assets/images/ecommerce/png/14.png",
      title: "Elegant Flower Pot",
      status: "Serene Garden",
      class: "danger",
      rating: "(211)",
      views: "15,253",
      price: "$314",
      discount: "$547",
      discount1: "60%",
      size: "Bluetooth",
      color: "success",
      stock: "Out Of Stock",
      stockColor: "danger",
      images: "../../../assets/img/ecommerce/products/4.png"
    },
    {
      id: 5,
      productpicture: "../../../assets/images/ecommerce/png/11.png",
      title: "Trendy Sunglasses",
      status: "Crystal Clear",
      class: "success",
      rating: "(514)",
      views: "20,989",
      price: "$251",
      discount: "$399",
      discount1: "10%",
      size: "500ML",
      color: "primarytint2color",
      stock: "In Stock",
      stockColor: "success",
      images: "../../../assets/img/ecommerce/products/5.png"
    },
    {
      id: 6,
      productpicture: "../../../assets/images/ecommerce/png/13.png",
      title: "Sleek Modern Chair",
      status: "Artisanal Comfort",
      class: "success",
      rating: "(211)",
      views: "22,989",
      price: "$314",
      discount: "$547",
      discount1: "60%",
      size: "Adjustable",
      color: "success",
      stock: "Out Of Stock",
      stockColor: "danger",
      images: "../../../assets/img/ecommerce/products/6.png"
    },
    {
      id: 7,
      productpicture: "../../../assets/images/ecommerce/png/23.png",
      title: " Advanced Smartwatch",
      status: "SmartSync 2024",
      class: "success",
      rating: "(255)",
      views: "10,252",
      price: "$354",
      discount: "$455",
      discount1: "15%",
      size: "Adjustable",
      color: "warning",
      stock: "In Stock",
      stockColor: "success",
      images: "../../../assets/img/ecommerce/products/7.png",
      badge: 'Trending',
      badgeColor: 'danger'
    },
    {
      id: 8,
      productpicture: "../../../assets/images/ecommerce/png/10.png",
      title: "Classic T-Shirt ",
      status: "Casual Everyday",
      class: "success",
      rating: "(142)",
      views: "10,989",
      price: "$251",
      discount: "$399",
      discount1: "50%",
      size: "M",
      color: "danger",
      stock: "Out Of Stock",
      stockColor: "danger",
      images: "../../../assets/img/ecommerce/products/8.png"
    },
    {
      id: 9,
      productpicture: "../../../assets/images/ecommerce/png/9.png",
      title: " Versatile Hoodie",
      status: "Urban Flex",
      class: "danger",
      rating: "(142)",
      views: "50,989",
      price: "$251",
      discount: "$399",
      discount1: "15%",
      size: "M",
      color: "info",
      stock: "In Stock",
      stockColor: "success",
      images: "../../../assets/img/ecommerce/products/9.png"
    },
    {
      id: 10,
      productpicture: "../../../assets/images/ecommerce/png/30.png",
      title: "Wireless Headphones",
      status: "SoundWave",
      class: "success",
      rating: "(142)",
      views: "45,989",
      price: "$251",
      discount: "$399",
      discount1: "21%",
      size: "Small",
      color: "primarytint1color",
      stock: "Out Of Stock",
      stockColor: "danger",
      images: "../../../assets/img/ecommerce/products/10.png"
    },
    {
      id: 11,
      productpicture: "../../../assets/images/ecommerce/png/31.png",
      title: " Wireless Earbuds",
      status: "AirPods Max",
      class: "danger",
      rating: "(211)",
      views: "45,989",
      price: "$314",
      discount: "$547",
      discount1: "60%",
      size: "XL",
      color: "success",
      stock: "Out of Stock",
      stockColor: "success",
      images: "../../../assets/img/ecommerce/products/11.png",

    },
    {
      id: 12,
      productpicture: "../../../assets/images/ecommerce/png/11.png",
      title: "Trendy Sunglasses",
      status: "Crystal Clear",
      class: "success",
      rating: "(514)",
      views: "45,989",
      price: "$251",
      discount: "$399",
      discount1: "10%",
      size: "M",
      color: "primarytint2color",
      stock: "Out Of Stock",
      stockColor: "danger",
      images: "../../../assets/img/ecommerce/products/12.png"
    },
  ],
  setSelectedItem: (item) => set({ selectedItem: item }),
  addToWishlist: (item, id) => set((state) => ({ wishlist: [...state.wishlist, { ...item, id }] })),
  removeFromWishlist: (id) => set((state) => ({ wishlist: state.wishlist.filter((item) => item.id !== id) })),
  addToCart: (item) => set((state) => {
    const existingItem = state.cart.find(cartItem => cartItem.id === item.id);
    if (existingItem) {
      return {
        cart: state.cart.map(cartItem =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: (cartItem.quantity || 1) + 1 }
            : cartItem
        )
      };
    }
    return { cart: [...state.cart, { ...item, quantity: 1 }] };
  }),
  removeFromCart: (id) => set((state) => ({ cart: state.cart.filter((item) => item.id !== id) })),
  updateCartQuantity: (id, quantity) => set((state) => ({
    cart: state.cart.map((item) =>
      item.id === id
        ? { ...item, quantity: Math.max(0, quantity) }
        : item
    )
  })),
  addToCheckout: (items) => set({ checkoutItems: items }),
  buyNow: (item) => set({ cart: [{ ...item, quantity: 1 }], checkoutItems: [{ ...item, quantity: 1 }] }),
}));
