import { UIState } from '@/shared/stores/uiStore';

export const htmlAttributeMap = {
    dir: 'dir',
    class: 'class',
    dataHeaderStyles: 'data-header-styles',
    dataVerticalStyle: 'data-vertical-style',
    dataNavLayout: 'data-nav-layout',
    dataMenuStyles: 'data-menu-styles',
    toggled: 'data-toggled',
    dataNavStyle: 'data-nav-style',
    dataPageStyle: 'data-page-style',
    dataWidth: 'data-width',
    dataMenuPosition: 'data-menu-position',
    dataHeaderPosition: 'data-header-position',
    iconOverlay: 'data-icon-overlay',
    bgImg: 'bg-img',
    iconText: 'icon-text',
} as const;

type HtmlAttributeMapKeys = keyof typeof htmlAttributeMap;
type UiStateForApply = Pick<UIState, HtmlAttributeMapKeys>; // Only picks the relevant keys
type CustomStyles = Record<string, string | number>;

const isEmpty = (styles: CustomStyles): boolean => {
    return Object.keys(styles).length === 0;
};
export const applyToHtml = ({
    uiState,
    customstyles,
}: {
    uiState: UiStateForApply;
    customstyles: CustomStyles;
}): void => {
    const html = document.documentElement;

    Object.entries(htmlAttributeMap).forEach(([uiStateKey, htmlAttr]) => {
        const value = uiState[uiStateKey as HtmlAttributeMapKeys]; // Cast using the specific keys
        if (value !== undefined && value !== null && value !== '') {
            html.setAttribute(htmlAttr, String(value));
        } else {
            html.removeAttribute(htmlAttr);
        }
    });
    if (isEmpty(customstyles)) {
        html.removeAttribute('style');
    } else {

        for (const cssVar in customstyles) {
            if (Object.prototype.hasOwnProperty.call(customstyles, cssVar)) {
                const value = customstyles[cssVar];
                if (value) {
                    html.style.setProperty(cssVar, String(value));
                } else {
                    html.style.removeProperty(cssVar);
                }
            }
        }
    }
};
