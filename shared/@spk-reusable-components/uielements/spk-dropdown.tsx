import React, { Fragment } from 'react';
import SpkButton from './spk-button';
import { SpkBadge } from '@/shared/UI/components';
import Link from 'next/link';
import NextImage from 'next/image';

interface SpkDropdownProps {
  children: React.ReactNode;
  Customclass?: string;
  Custommenuclass?: string;
  Icon?: boolean;
  Arrowicon?: boolean;
  SvgClass?: string;
  Svg?: boolean;
  Svgaftertext?: string;
  Badgetext?: string | number;
  Badgeclass?: string;
  Badgeid?: string;
  Badgecolor?: string;
  Notificationbadge?: boolean;
  Svgicon?: string;
  Badgetag?: boolean;
  Image?: string;
  Imagetag?: boolean;
  Linktag?: boolean;
  Menulabel?: string;
  Imageclass?: string;
  Imagename?: string;
  Size?: string;
  Linkclass?: string;
  Svgheight?: string | number;
  Svvgviewbox?: string;
  Strokewidth?: string | number;
  SvgStroke?: string;
  Svgwidth?: string | number;
  CustomToggleclass?: string;
  Toggletext?: string;
  iconPosition?: 'before' | 'after';
  buttonid?: string;
  color?: string;
  arialexpand?: boolean;
  IconClass?: string;
  Navigate?: string;
  Customtoggleclass?: string;
}

const SpkDropdown: React.FC<SpkDropdownProps> = ({
  Customclass,
  Custommenuclass,
  Icon = false,
  Arrowicon = false,
  SvgClass,
  Svg,
  Svgaftertext,
  Badgetext,
  Badgeclass,
  Badgeid,
  Badgecolor,
  Notificationbadge,
  Svgicon,
  Badgetag = false,
  Image,
  Imagetag = false,
  Linktag = false,
  Menulabel,
  Imageclass,
  Imagename,
  Size,
  Linkclass,
  Svgheight,
  Svvgviewbox,
  Strokewidth,
  SvgStroke,
  Svgwidth,
  children,
  CustomToggleclass,
  Toggletext,
  iconPosition,
  buttonid,
  color,
  arialexpand,
  IconClass
}) => {
  return (
    <Fragment>
      <div className={`hs-dropdown ti-dropdown  ${Customclass}`}>
        {Linktag ?
          <Link scroll={false} href="#!" className={Linkclass} aria-expanded={arialexpand} aria-label="anchor" id={buttonid}>
            {iconPosition === 'before' ?
              (
                <>
                  {Imagetag ? (<NextImage src={Image || ''} alt={Imagename || 'Image'} className={Imageclass} width={20} height={20} />) : <>{Icon ? (<i className={IconClass}></i>) : ""}</>}
                  {Toggletext}
                </>
              )
              : (
                <>
                  {Toggletext}
                  {Imagetag ? (<NextImage src={Image || ''} alt={Imagename || 'Image'} className={Imageclass} width={20} height={20} />) : <>{Icon ? (<i className={IconClass}></i>) : ""}</>}

                </>
              )}

            {Arrowicon ? <i className="ri-arrow-down-s-line align-middle ms-1"></i> : ""}

            {Svg ?
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className={SvgClass} fill="none" width={Svgwidth} height={Svgheight} viewBox={Svvgviewbox} strokeWidth={Strokewidth} stroke={SvgStroke} strokeLinejoin="round">
                  <path strokeLinecap="round" d={Svgicon} strokeWidth={Strokewidth} />
                </svg>
                {Svgaftertext}
              </>
              : ""}
            {Badgetag ?
              <SpkBadge variant={Badgecolor} className={Badgeclass} id={Badgeid}>{Badgetext}</SpkBadge>
              : ""}
          </Link>
          :
          <SpkButton Size={Size} customClass={`ti-btn ${CustomToggleclass}`} variant={color} type="button" Id={buttonid} Expand={arialexpand}>

            {iconPosition === 'before' ?
              (
                <>
                  {Imagetag ? (<NextImage src={Image || ''} alt={Imagename || 'Image'} className={Imageclass} width={20} height={20} />) : <>{Icon ? (<i className={IconClass}></i>) : ""}</>}
                  {Toggletext}
                </>
              )
              : (
                <>
                  {Toggletext}
                  {Imagetag ? (<NextImage src={Image || ''} alt={Imagename || 'Image'} className={Imageclass} width={20} height={20} />) : <>{Icon ? (<i className={IconClass}></i>) : ""}</>}

                </>
              )}

            {Arrowicon ? <i className="ri-arrow-down-s-line align-middle ms-1 inline-block"></i> : ""}

            {Svg ?
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className={SvgClass} fill="none" width={Svgwidth} height={Svgheight} viewBox={Svvgviewbox} strokeWidth={Strokewidth} stroke={SvgStroke}>
                  <path strokeLinecap="round" d={Svgicon} strokeWidth={Strokewidth} />
                </svg>
                {Svgaftertext}
              </>
              : ""}
            {Badgetag ?
              <span className={`flex absolute h-5 w-5 -top-[0.25rem] end-0 -me-[0.6rem] ${Notificationbadge ? "animate-slow-ping absolute inline-flex -top-[2px] -start-[2px] h-full w-full rounded-full bg-secondary/40 opacity-75" : ''}`}>
                <SpkBadge variant={Badgecolor} className={Badgeclass} id={Badgeid}>{Badgetext}</SpkBadge>
              </span>
              : ""}
          </SpkButton>
        }
        <ul className={`hs-dropdown-menu ti-dropdown-menu hidden ${Custommenuclass}`} aria-labelledby={Menulabel} role="menu">
          {children}
        </ul>
      </div>
    </Fragment>
  );
};

export default SpkDropdown;
