import { Fragment } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ark<PERSON>, <PERSON><PERSON>, <PERSON>yline, Rectangle, Polygon, Circle } from "react-leaflet";
import { LatLngExpression, LatLngBoundsExpression, PathOptions } from "leaflet";
import "leaflet/dist/leaflet.css";

interface SpkLeafletProps {
  position: LatLngExpression;
  Circlepostion?: LatLngExpression;
  Zoom: number;
  scrollWheel: boolean;
  Customclass?: string;
  Id?: string;
  CirclepathOptions?: PathOptions;
  MarkerpathOptions?: PathOptions;
  PolylinepathOptions?: PathOptions;
  Polyllinepositions?: LatLngExpression[];
  PolygonpathOptions?: PathOptions;
  Polygonposition?: LatLngExpression[];
  ReactanglepathOptions?: PathOptions;
  Bounds?: LatLngBoundsExpression;
  MUltipolygonposition?: LatLngExpression[][];
  Multipolyineposition?: LatLngExpression[];
}

const Spk<PERSON><PERSON><PERSON>t = ({ position,
  Circlepostion, Zoom, scrollWheel, Customclass, Id, CirclepathOptions, MarkerpathOptions, PolylinepathOptions,
  Polyllinepositions, PolygonpathOptions, Polygonposition, ReactanglepathOptions, Bounds, MUltipolygonposition, Multipolyineposition
}: SpkLeafletProps) => {
  return (
    <Fragment>
      <MapContainer center={position} zoom={Zoom} scrollWheelZoom={scrollWheel} className={Customclass} id={Id} style={{ height: "300px" }}>

        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        {Id === "leaflet3" && Circlepostion ?
          <Circle center={Circlepostion} pathOptions={CirclepathOptions} radius={200} />
          : ''
        }
        {(Id === "leaflet3" || Id === "leaflet2") ?
          <CircleMarker
            center={[51.51, -0.12]}
            pathOptions={MarkerpathOptions}
            radius={20}
          >
            <Popup>Popup in CircleMarker</Popup>
          </CircleMarker>
          : ''
        }

        {Id === "leaflet3" ?
          <>
            {Polyllinepositions && <Polyline pathOptions={PolylinepathOptions} positions={Polyllinepositions} />}
            {Multipolyineposition && <Polyline pathOptions={PolylinepathOptions} positions={Multipolyineposition} />}
            {Polygonposition && <Polygon pathOptions={PolygonpathOptions} positions={Polygonposition} />}
            {MUltipolygonposition && <Polygon pathOptions={PolygonpathOptions} positions={MUltipolygonposition} />}
            {Bounds && <Rectangle bounds={Bounds} pathOptions={ReactanglepathOptions} />}
          </>
          : ''
        }

      </MapContainer>
    </Fragment>
  );
};

export default SpkLeaflet;
