// shared/hooks/useHeaderLogic.ts
'use client';

import React from 'react';
import nextConfig from "@/next.config";
import { useSidebarToggle } from './useSidebarToggle';

interface UseHeaderLogicReturn {
    basePath: string;
    overlayRef: React.RefObject<HTMLDivElement | null>;
    menuClose: () => void;
}

/**
 * Custom hook for header business logic
 * 
 * Centralizes header-related business logic including:
 * - Base path configuration
 * - Sidebar toggle functionality
 * - Any other header-specific logic
 */
export const useHeaderLogic = (): UseHeaderLogicReturn => {
    const basePath: string = (nextConfig as any).basePath || '';
    const { overlayRef, menuClose } = useSidebarToggle();

    return {
        basePath,
        overlayRef,
        menuClose,
    };
};
