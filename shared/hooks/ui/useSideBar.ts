'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useUIStore, UIState } from '../../stores/uiStore';
import nextConfig from '@/next.config';
import { MENUITEMS } from '../../layouts-components/sidebar/nav';
import { usePathname } from 'next/navigation';

interface UseSidebarLogicReturn {
    menuitems: typeof MENUITEMS;
    isSticky: boolean;
    overlayRef: React.RefObject<HTMLDivElement | null>;
    sidebarRef: React.RefObject<HTMLElement | null>;
    slideRef: React.RefObject<HTMLLIElement | null>;
    slideMenuRef: React.RefObject<HTMLElement | null>;
    menuNavRef: React.RefObject<HTMLUListElement | null>;
    mainContainerRef: React.RefObject<any | null>;
    menuClose: () => void; // Exposed if UI buttons call it
    Onhover: () => void;
    Outhover: () => void;
    toggleSidemenu: (event: React.MouseEvent, targetObject: any, menuItems?: any[]) => void;
    slideRight: () => void;
    slideLeft: () => void;
    handleClick: (event: React.MouseEvent) => void;
    HoverToggleInnerMenuFn: (event: React.MouseEvent, item: any) => void;
    uiState: UIState;
    basePath: string;
}

export const useSideBar = (): UseSidebarLogicReturn => {
    const uiState = useUIStore();
    // Access state and actions from Zustand stores
    const ThemeChanger = useUIStore((state) => state.ThemeChanger); // Get the ThemeChanger action

    let { basePath }: any = nextConfig; // IMPORTANT: Accessing nextConfig directly in a client component is not recommended for production.
    // Consider passing `basePath` as a prop from a Server Component or using a public env var.
    // State to track previous URL for re-evaluation
    const [previousUrl, setPreviousUrl] = useState("/");
    const [menuitems, setMenuitems] = useState(MENUITEMS);
    const path = usePathname(); // For current path to highlight menu items

    // Ref for the responsive overlay, passed to the div
    const overlayRef = useRef<HTMLDivElement | null>(null);

    // Ref for the main sidebar element for sticky behavior
    const sidebarRef = useRef<HTMLElement | null>(null);
    const [isSticky, setIsSticky] = useState(false);

    // Refs for menu sliding functionality
    const slideRef = useRef<HTMLLIElement | null>(null); // Ref for <li> elements
    const slideMenuRef = useRef<HTMLElement | null>(null); // Ref for slide menu (child component)
    const menuNavRef = useRef<HTMLUListElement | null>(null);
    const mainContainerRef = useRef<any | null>(null); // Ref for the main-sidebar div around SimpleBar
    const didSidebarInit = useRef(false); // Flag to ensure sidebar initialization runs once

    // Helper to safely get DOM elements on client
    const slidesArrow = (selector: any) => {
        if (typeof document !== 'undefined') {
            return document.querySelector(selector);
        }
        return null;
    };

    // Helper function to close all menu items
    const closeMenu = () => {
        const closeMenudata = (items: any[]) => {
            items?.forEach((item: any) => {
                item.active = false;
                closeMenudata(item.children);
            });
        };
        closeMenudata(MENUITEMS); // Operate on original MENUITEMS to reset
        setMenuitems([...MENUITEMS]); // Update local state with reset items
    };

    // Function to handle opening/closing sidebar for mobile
    const menuClose = useCallback(() => {
        const currentTheme = useUIStore.getState(); // Get latest state
        if (window.innerWidth <= 992) {
            ThemeChanger({ toggled: "close" });
        }
        if (overlayRef.current) {
            overlayRef.current.classList.remove("active");
        }
        if (currentTheme.dataNavLayout === "horizontal" || currentTheme.dataNavStyle === "menu-click" || currentTheme.dataNavStyle === "icon-click") {
            closeMenu();
        }
    }, [ThemeChanger]);

    // Handles sidebar resizing logic for desktop/mobile toggling
    const WindowPreSize = useRef<number[]>([]); // Use useRef for mutable array across renders

    const menuResizeFn = useCallback(() => {
        if (typeof window === 'undefined') return;

        WindowPreSize.current.push(window.innerWidth);
        if (WindowPreSize.current.length > 2) { WindowPreSize.current.shift(); }

        const currentTheme = useUIStore.getState(); // Get latest state
        const currentWidth = WindowPreSize.current[WindowPreSize.current.length - 1];
        const prevWidth = WindowPreSize.current[WindowPreSize.current.length - 2];

        if (WindowPreSize.current.length > 1) {
            if (currentWidth < 992 && prevWidth >= 992) {
                ThemeChanger({ toggled: "close" });
            }
            if (currentWidth >= 992 && prevWidth < 992) {
                ThemeChanger({ toggled: currentTheme.dataVerticalStyle === "doublemenu" ? "double-menu-open" : "" });
            }
        }
    }, [ThemeChanger]);

    // Handles hover effects for icon-overlay/detached menu styles
    const Onhover = () => {
        const currentTheme = useUIStore.getState();
        if ((currentTheme.toggled === 'icon-overlay-close' || currentTheme.toggled === 'detached-close') && currentTheme.iconOverlay !== 'open') {
            ThemeChanger({ iconOverlay: "open" });
        }
    };
    const Outhover = () => {
        const currentTheme = useUIStore.getState();
        if ((currentTheme.toggled === 'icon-overlay-close' || currentTheme.toggled === 'detached-close') && currentTheme.iconOverlay === 'open') {
            ThemeChanger({ iconOverlay: "" });
        }
    };

    // Handles click on menu items to activate/deactivate
    const hasParent = useRef(false);
    const hasParentLevel = useRef(0);

    // Helper to find the parent object of a given child object in the menu structure
    const getParentObject = useCallback((obj: any, childObject: any, menuArray: any[] = MENUITEMS): any => {
        for (const item of menuArray) {
            if (item.children && item.children.includes(childObject)) {
                return item;
            }
            if (item.children && item.children.length > 0) {
                const parent = getParentObject(obj, childObject, item.children);
                if (parent !== null) {
                    return parent;
                }
            }
        }
        return null;
    }, []);

    // Removes active/selected state from other menus
    const removeActiveOtherMenus = useCallback((item: any) => {
        if (item) {
            if (Array.isArray(item)) {
                for (const val of item) {
                    val.active = false;
                    val.selected = false;
                    if (val.children && val.children.length > 0) {
                        removeActiveOtherMenus(val.children);
                    }
                }
            } else {
                item.active = false;
                item.selected = false;
                if (item.children && item.children.length > 0) {
                    removeActiveOtherMenus(item.children);
                }
            }
        }
    }, []);

    // Sets active/selected state for parent menus
    const setMenuAncestorsActive = useCallback((targetObject: any) => {
        const parent = getParentObject(MENUITEMS, targetObject); // Always search from original MENUITEMS
        const currentTheme = useUIStore.getState();

        if (parent) {
            if (hasParentLevel.current > 2) {
                hasParent.current = true;
            }
            parent.active = true;
            parent.selected = true;
            hasParentLevel.current += 1;
            setMenuAncestorsActive(parent); // Recursive call
        } else if (!hasParent.current) {
            hasParentLevel.current = 0;
            if (currentTheme.dataVerticalStyle === 'doublemenu') {
                ThemeChanger({ toggled: "double-menu-close" });
            } else {
                hasParentLevel.current = 0;
                hasParent.current = false;
            }
        }
    }, [ThemeChanger, getParentObject]);

    const setSubmenu = useCallback((event: any, targetObject: any, MenuItems: any[] = menuitems) => {
        const _currentTheme = useUIStore.getState();

        if (!event?.ctrlKey) {
            for (const item of MenuItems) {
                if (item === targetObject) {
                    item.active = true;
                    item.selected = true;
                    setMenuAncestorsActive(item);
                } else if (!item.active && !item.selected) {
                    // Only deactivate if not the target and not already active/selected
                    item.active = false;
                    item.selected = false;
                } else {
                    removeActiveOtherMenus(item);
                }
                if (item.children && item.children.length > 0) {
                    setSubmenu(event, targetObject, item.children);
                }
            }
        }
        setMenuitems((arr: any) => [...arr]); // Trigger re-render
    }, [menuitems, setMenuitems, removeActiveOtherMenus, setMenuAncestorsActive]);

    // Activates menu items based on current URL
    const setMenuUsingUrl = useCallback((currentPath: string) => {
        hasParent.current = false;
        hasParentLevel.current = 1;
        const setSubmenuRecursively = (items: any[]) => {
            items?.forEach((item: any) => {
                if (item.path && item.path === currentPath) {
                    setSubmenu(null, item); // Pass null for event as it's not a direct click
                }
                setSubmenuRecursively(item.children);
            });
        };
        setSubmenuRecursively(MENUITEMS); // Start recursion from original MENUITEMS
    }, [setSubmenu]);

    // Handles mutation of HTML attributes (e.g., data-nav-layout)
    const handleAttributeChange = useCallback((mutationsList: MutationRecord[]) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'data-nav-layout') {
                const newValue = (mutation.target as HTMLElement).getAttribute('data-nav-layout');
                if (newValue === 'vertical') {
                    // Re-activate menu on vertical layout change
                    let currentPath = location.pathname.endsWith('/') ? location.pathname.slice(0, -1).replace(basePath, '') : location.pathname.replace(basePath, '');
                    currentPath = !currentPath ? '/dashboard' : currentPath;
                    setMenuUsingUrl(currentPath);
                } else {
                    closeMenu(); // Close menus on horizontal layout change
                }
            }
        }
    }, [basePath, setMenuUsingUrl]);

    // Toggles sidemenu on click (doublemenu, menu-click, icon-click)
    const toggleSidemenu = (event: React.MouseEvent, targetObject: any, MenuItems: any[] = menuitems) => {
        const currentTheme = useUIStore.getState();
        let element = event.target as HTMLElement;

        if ((currentTheme.dataNavStyle !== "icon-hover" && currentTheme.dataNavStyle !== "menu-hover") ||
            (window.innerWidth < 992) ||
            (currentTheme.dataNavLayout !== "horizontal") &&
            (currentTheme.toggled !== "icon-hover-closed" && currentTheme.toggled !== "menu-hover-closed")) {

            for (const item of MenuItems) {
                if (item === targetObject) {
                    if (currentTheme.dataVerticalStyle === 'doublemenu' && item.active) { return; }
                    item.active = !item.active;

                    if (item.active) {
                        closeOtherMenus(MenuItems, item);
                    } else {
                        if (currentTheme.dataVerticalStyle === 'doublemenu') {
                            ThemeChanger({ toggled: "double-menu-close" });
                        }
                    }
                    setAncestorsActive(MenuItems, item);
                } else if (!item.active) {
                    if (currentTheme.dataVerticalStyle !== 'doublemenu') {
                        item.active = false;
                    }
                }
                if (item.children && item.children.length > 0) {
                    toggleSidemenu(event, targetObject, item.children);
                }
            }

            if (targetObject?.children && targetObject.active) {
                if (currentTheme.dataVerticalStyle === 'doublemenu' && currentTheme.toggled !== 'double-menu-open') {
                    ThemeChanger({ toggled: "double-menu-open" });
                }
            }

            if (element && currentTheme.dataNavLayout === 'horizontal' &&
                (currentTheme.dataNavStyle === 'menu-click' || currentTheme.dataNavStyle === 'icon-click')) {
                const listItem = element.closest("li");
                if (listItem) {
                    const siblingUL = listItem.querySelector("ul");
                    let outterUlWidth = 0;
                    let listItemUL = listItem.closest('ul:not(.main-menu)');
                    while (listItemUL) {
                        listItemUL = listItemUL.parentElement?.closest('ul:not(.main-menu)') ?? null;
                        if (listItemUL) {
                            outterUlWidth += listItemUL.clientWidth;
                        }
                    }
                    if (siblingUL) {
                        let siblingULRect = listItem.getBoundingClientRect();
                        if (currentTheme.dir === 'rtl') {
                            if ((siblingULRect.left - siblingULRect.width - outterUlWidth + 150 < 0 && outterUlWidth < window.innerWidth) &&
                                (outterUlWidth + siblingULRect.width + siblingULRect.width < window.innerWidth)) {
                                targetObject.dirchange = true;
                            } else {
                                targetObject.dirchange = false;
                            }
                        } else {
                            if ((outterUlWidth + siblingULRect.right + siblingULRect.width + 50 > window.innerWidth && siblingULRect.right >= 0) &&
                                (outterUlWidth + siblingULRect.width + siblingULRect.width < window.innerWidth)) {
                                targetObject.dirchange = true;
                            } else {
                                targetObject.dirchange = false;
                            }
                        }
                    }
                }
            }
        }
        setMenuitems((arr: any) => [...arr]); // Trigger re-render
    };

    // Helper function to set ancestors active (for doublemenu, etc.)
    const setAncestorsActive = (menuItems: any[], targetObject: any) => {
        const currentTheme = useUIStore.getState();
        const parent = findParent(menuItems, targetObject);
        if (parent) {
            parent.active = true;
            if (parent.active) {
                ThemeChanger({ toggled: "double-menu-open" });
            }
            setAncestorsActive(menuItems, parent);
        } else {
            if (currentTheme.dataVerticalStyle === "doublemenu") {
                ThemeChanger({ toggled: "double-menu-close" });
            }
        }
    };

    function closeOtherMenus(MenuItems: any, targetObject: any) {
        for (const item of MenuItems) {
            if (item !== targetObject) {
                item.active = false;
                if (item.children && item.children.length > 0) {
                    closeOtherMenus(item.children, targetObject);
                }
            }
        }
    }
    // Helper to find the immediate parent of a target object
    const findParent = (menuItems: any[], targetObject: any): any | null => {
        for (const item of menuItems) {
            if (item.children && item.children.includes(targetObject)) {
                return item;
            }
            if (item.children && item.children.length > 0) {
                const parent: any = findParent(item.children, targetObject);
                if (parent) {
                    return parent;
                }
            }
        }
        return null;
    };

    // Handles hover for horizontal menu dropdown direction
    const HoverToggleInnerMenuFn = (event: React.MouseEvent, item: any) => {
        const currentTheme = useUIStore.getState();
        let element = event.target as HTMLElement;

        if (element && currentTheme.dataNavLayout === "horizontal" &&
            (currentTheme.dataNavStyle === "menu-hover" || currentTheme.dataNavStyle === "icon-hover")) {

            const listItem = element.closest("li");
            if (listItem) {
                const siblingUL = listItem.querySelector("ul");
                let outterUlWidth = 0;
                let listItemUL = listItem.closest("ul:not(.main-menu)");
                while (listItemUL) {
                    listItemUL = listItemUL.parentElement?.closest("ul:not(.main-menu)") ?? null;
                    if (listItemUL) {
                        outterUlWidth += listItemUL.clientWidth;
                    }
                }
                if (siblingUL) {
                    let siblingULRect = listItem.getBoundingClientRect();
                    if (currentTheme.dir === "rtl") {
                        if ((siblingULRect.left - siblingULRect.width - outterUlWidth + 150 < 0 && outterUlWidth < window.innerWidth) &&
                            (outterUlWidth + siblingULRect.width + siblingULRect.width < window.innerWidth)) {
                            item.dirchange = true;
                        } else {
                            item.dirchange = false;
                        }
                    } else {
                        if ((outterUlWidth + siblingULRect.right + siblingULRect.width + 50 > window.innerWidth && siblingULRect.right >= 0) &&
                            (outterUlWidth + siblingULRect.width + siblingULRect.width < window.innerWidth)) {
                            item.dirchange = true;
                        } else {
                            item.dirchange = false;
                        }
                    }
                }
            }
        }
    };

    // Horizontal Menu Arrows (Slide Right/Left) - heavily relies on DOM manipulation and theme state
    const switcherArrowFn = () => {
        // Implementation for toggling 'is-expanded' and 'open' classes
        const slide = slideRef.current;
        const slideMenu = slideMenuRef.current;

        if (slide && slideMenu) {
            Array.from(slide.children).forEach((element) => {
                if (element instanceof HTMLElement) {
                    element.classList.toggle("is-expanded");
                }
            });
            Array.from(slideMenu.children).forEach((element) => {
                if (element instanceof HTMLElement) {
                    element.classList.toggle("open");
                    element.style.display = element.classList.contains("open") ? "block" : "none";
                }
            });
        }
    };

    const checkHoriMenu = useCallback(() => {
        const menuNav = menuNavRef.current || slidesArrow(".main-menu");
        const mainContainer1 = mainContainerRef.current || slidesArrow(".main-sidebar");

        if (!menuNav || !mainContainer1) return;

        const marginLeftValue = Math.ceil(
            Number(window.getComputedStyle(menuNav).marginLeft.split("px")[0])
        );
        const marginRightValue = Math.ceil(
            Number(window.getComputedStyle(menuNav).marginRight.split("px")[0])
        );
        const check = menuNav.scrollWidth - mainContainer1.offsetWidth;

        if (menuNav.scrollWidth > mainContainer1.offsetWidth) {
            // Logic to show/hide arrows, if needed
        } else {
            menuNav.style.marginLeft = "0px";
            menuNav.style.marginRight = "0px";
            menuNav.style.marginInlineStart = "0px";
        }
        const isRtl = document.documentElement.getAttribute("dir") === "rtl";
        if (!isRtl) {
            if (menuNav.scrollWidth > mainContainer1.offsetWidth) {
                if (Math.abs(check) < Math.abs(marginLeftValue)) {
                    menuNav.style.marginLeft = -check + "px";
                }
            }
        } else {
            if (menuNav.scrollWidth > mainContainer1.offsetWidth) {
                if (Math.abs(check) < Math.abs(marginRightValue)) {
                    menuNav.style.marginRight = -check + "px";
                }
            }
        }
    }, []);

    function slideRight(): void {
        const currentTheme = useUIStore.getState(); // Get current state for dir
        const menuNav = slidesArrow(".main-menu");
        const mainContainer1 = slidesArrow(".main-sidebar");
        const slideRightButton = slidesArrow("#slide-right");
        const slideLeftButton = slidesArrow("#slide-left");
        const element = slidesArrow(".main-menu > .slide.open");
        const element1 = slidesArrow(".main-menu > .slide.open > ul");

        if (menuNav && mainContainer1) {
            const marginLeftValue = Math.ceil(
                Number(window.getComputedStyle(menuNav).marginInlineStart.split("px")[0])
            );
            const marginRightValue = Math.ceil(
                Number(window.getComputedStyle(menuNav).marginInlineEnd.split("px")[0])
            );
            const check = menuNav.scrollWidth - mainContainer1.offsetWidth;
            let mainContainer1Width = mainContainer1.offsetWidth;

            if (menuNav.scrollWidth > mainContainer1.offsetWidth) {
                if (!(currentTheme.dir === "rtl")) { // Use currentTheme.dir
                    if (Math.abs(check) > Math.abs(marginLeftValue)) {
                        menuNav.style.marginInlineEnd = "0";

                        if (!(Math.abs(check) > Math.abs(marginLeftValue) + mainContainer1Width)) {
                            mainContainer1Width = Math.abs(check) - Math.abs(marginLeftValue);
                            if (slideRightButton) {
                                slideRightButton.classList.add("hidden");
                            }
                        }

                        menuNav.style.marginInlineStart =
                            (Number(menuNav.style.marginInlineStart.split("px")[0]) -
                                Math.abs(mainContainer1Width)) +
                            "px";

                        if (slideLeftButton) { // Changed to slideLeftButton here based on common logic
                            slideLeftButton.classList.remove("hidden");
                        }
                    }
                } else { // RTL logic
                    if (Math.abs(check) > Math.abs(marginRightValue)) {
                        menuNav.style.marginInlineEnd = "0";

                        if (!(Math.abs(check) > Math.abs(marginRightValue) + mainContainer1Width)) {
                            mainContainer1Width = Math.abs(check) - Math.abs(marginRightValue);
                            if (slideRightButton) {
                                slideRightButton.classList.add("hidden");
                            }
                        }

                        menuNav.style.marginInlineStart =
                            (Number(menuNav.style.marginInlineStart.split("px")[0]) -
                                Math.abs(mainContainer1Width)) +
                            "px";

                        if (slideLeftButton) {
                            slideLeftButton.classList.remove("hidden");
                        }
                    }
                }
            }

            if (element) {
                element.classList.remove("active");
            }
            if (element1) {
                element1.style.display = "none";
            }
        }
        switcherArrowFn();
        checkHoriMenu();
    }

    function slideLeft(): void {
        const currentTheme = useUIStore.getState(); // Get current state for dir
        const menuNav = slidesArrow(".main-menu");
        const mainContainer1 = slidesArrow(".main-sidebar");
        const slideRightButton = slidesArrow("#slide-right");
        const slideLeftButton = slidesArrow("#slide-left");
        const element = slidesArrow(".main-menu > .slide.open");
        const element1 = slidesArrow(".main-menu > .slide.open > ul");

        if (menuNav && mainContainer1) {
            const marginLeftValue = Math.ceil(
                Number(window.getComputedStyle(menuNav).marginInlineStart.split("px")[0])
            );
            const marginRightValue = Math.ceil(
                Number(window.getComputedStyle(menuNav).marginInlineEnd.split("px")[0])
            );
            const check = menuNav.scrollWidth - mainContainer1.offsetWidth;
            let mainContainer1Width = mainContainer1.offsetWidth;

            if (menuNav.scrollWidth > mainContainer1.offsetWidth) {
                if (!(currentTheme.dir === "rtl")) { // Use currentTheme.dir
                    if (Math.abs(check) <= Math.abs(marginLeftValue)) { // Corrected logic for <=
                        menuNav.style.marginInlineStart = "0px";
                        if (slideLeftButton) {
                            slideLeftButton.classList.add("hidden");
                        }
                    } else { // If still can move left
                        menuNav.style.marginInlineStart =
                            (Number(menuNav.style.marginInlineStart.split("px")[0]) +
                                Math.abs(mainContainer1Width)) +
                            "px";
                    }
                    if (slideRightButton) {
                        slideRightButton.classList.remove("hidden");
                    }
                } else { // RTL logic
                    if (Math.abs(check) <= Math.abs(marginRightValue)) { // Corrected logic for <=
                        menuNav.style.marginInlineEnd = "0px";
                        if (slideLeftButton) {
                            slideLeftButton.classList.add("hidden");
                        }
                    } else { // If still can move right (slide left effect in RTL)
                        menuNav.style.marginInlineEnd =
                            (Number(menuNav.style.marginInlineEnd.split("px")[0]) +
                                Math.abs(mainContainer1Width)) +
                            "px";
                    }
                    if (slideRightButton) {
                        slideRightButton.classList.remove("hidden");
                    }
                }
            }

            if (element) {
                element.classList.remove("active");
            }
            if (element1) {
                element1.style.display = "none";
            }
        }
        switcherArrowFn();
    }

    const handleClick = (event: React.MouseEvent) => {
        event.preventDefault(); // Prevents the default anchor behavior (navigation)
    };

    // Effect to handle URL changes and attribute changes (like data-nav-layout)
    useEffect(() => {
        const targetElement = document.documentElement;
        const observer = new MutationObserver(handleAttributeChange);
        const config = { attributes: true };
        observer.observe(targetElement, config);

        let currentPath = path.endsWith("/") ? path.slice(0, -1) : path;
        // Adjust currentPath based on basePath for correct matching if needed
        currentPath = currentPath.replace(basePath, '');
        currentPath = !currentPath ? '/dashboard' : currentPath; // Default to a valid path if empty

        if (currentPath !== previousUrl) {
            setMenuUsingUrl(currentPath);
            setPreviousUrl(currentPath);
        }

        return () => observer.disconnect(); // Cleanup observer
    }, [path, basePath, previousUrl, handleAttributeChange, setMenuUsingUrl]); // Add path and basePath to dependencies

    // Sidebar Sticky functionality
    useEffect(() => {
        const handleScroll = () => {
            setIsSticky(window.scrollY > 30);
        };
        window.addEventListener("scroll", handleScroll);
        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);

    // Initial load effect for menu and resize listeners
    useEffect(() => {
        if (typeof window !== 'undefined') {
            const resizeEventListeners = [
                { event: 'resize', handler: menuResizeFn },
                { event: 'resize', handler: checkHoriMenu },
            ];

            resizeEventListeners.forEach(({ event, handler }) => {
                window.addEventListener(event, handler);
            });

            const mainContent = slidesArrow(".main-content");
            if (!didSidebarInit.current && window.innerWidth <= 992) {
                if (mainContent) {
                    if (useUIStore.getState().toggled !== "close") { // Check current state to avoid redundant update
                        ThemeChanger({ toggled: "close" });
                    }
                } else if (document.documentElement.getAttribute('data-nav-layout') === 'horizontal') {
                    closeMenu(); // Use memoized closeMenu
                }
                didSidebarInit.current = true; // Mark as initialized
            }

            if (mainContent) {
                mainContent.addEventListener('click', menuClose);
            }

            return () => {
                resizeEventListeners.forEach(({ event, handler }) => {
                    window.removeEventListener(event, handler);
                });
                if (mainContent) {
                    mainContent.removeEventListener('click', menuClose);
                }
            };
        }
    }, [ThemeChanger, menuClose, menuResizeFn, checkHoriMenu]); // Dependencies for useEffect
    return {
        menuitems,
        isSticky,
        overlayRef,
        sidebarRef,
        slideRef,
        slideMenuRef,
        menuNavRef,
        mainContainerRef,
        menuClose,
        Onhover,
        Outhover,
        toggleSidemenu,
        slideRight,
        slideLeft,
        uiState,
        handleClick,
        HoverToggleInnerMenuFn,
        basePath,
    };
};
