// shared/hooks/usePerformanceOptimization.ts - Performance optimization hooks

import { useEffect, useCallback, useMemo, useRef, useState } from 'react';
import { caches, } from '@/shared/utils/cache';
import { performanceUtils } from '@/shared/utils/performance';

/**
 * Hook for optimizing component performance with caching and memoization
 */
export function usePerformanceOptimization(_componentName: string) {
	const renderStartTime = useRef(performance.now());

	useEffect(() => {
		// Reset for next render
		renderStartTime.current = performance.now();
	});

	return {
		measureRenderTime: useCallback(() => {
			return performance.now() - renderStartTime.current;
		}, []),
	};
}

/**
 * Hook for caching API responses with automatic cache management
 */
export function useApiCache<T>(
	cacheKey: string,
	fetchFn: () => Promise<T>,
	options: {
		ttl?: number;
		staleWhileRevalidate?: number;
		enabled?: boolean;
	} = {}
) {
	const { ttl = 5 * 60 * 1000, enabled = true } = options;

	const getCachedData = useCallback(async (): Promise<T | null> => {
		if (!enabled) return null;

		return await caches.api.get(cacheKey, fetchFn);
	}, [cacheKey, fetchFn, enabled]);

	const setCachedData = useCallback((data: T) => {
		if (!enabled) return;

		caches.api.set(cacheKey, data, ttl);
	}, [cacheKey, ttl, enabled]);

	const invalidateCache = useCallback(() => {
		caches.api.delete(cacheKey);
	}, [cacheKey]);

	return {
		getCachedData,
		setCachedData,
		invalidateCache,
		cacheStats: caches.api.getStats(),
	};
}

/**
 * Hook for optimizing expensive computations with memoization
 */
export function useExpensiveComputation<T>(
	computeFn: () => T,
	dependencies: any[],
	cacheKey?: string
) {
	// Use React's useMemo for component-level memoization
	const memoizedValue = useMemo(() => {
		const result = computeFn();

		// Optionally cache the result globally
		if (cacheKey) {
			caches.static.set(cacheKey, result);
		}

		return result;
	}, [cacheKey, computeFn]);

	return memoizedValue;
}

/**
 * Hook for debouncing user input to improve performance
 */
export function useDebounce<T>(value: T, delay: number): T {
	const [debouncedValue, setDebouncedValue] = useState<T>(value);

	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedValue(value);
		}, delay);

		return () => {
			clearTimeout(handler);
		};
	}, [value, delay]);

	return debouncedValue;
}

/**
 * Hook for throttling function calls to improve performance
 */
export function useThrottle<T extends (..._args: any[]) => any>(
	callback: T,
	delay: number
): T {
	const throttledCallback = useMemo(() => {
		return performanceUtils.throttle(callback, delay);
	}, [callback, delay]);

	return throttledCallback;
}

/**
 * Hook for lazy loading components with intersection observer
 */
export function useLazyLoad(options: {
	threshold?: number;
	rootMargin?: string;
	triggerOnce?: boolean;
} = {}) {
	const { threshold = 0.1, rootMargin = '50px', triggerOnce = true } = options;
	const [isVisible, setIsVisible] = useState(false);
	const [hasTriggered, setHasTriggered] = useState(false);
	const elementRef = useRef<HTMLElement>(null);

	useEffect(() => {
		const element = elementRef.current;
		if (!element || (triggerOnce && hasTriggered)) return;

		const observer = new IntersectionObserver(
			([entry]) => {
				if (entry.isIntersecting) {
					setIsVisible(true);
					if (triggerOnce) {
						setHasTriggered(true);
						observer.unobserve(element);
					}
				} else if (!triggerOnce) {
					setIsVisible(false);
				}
			},
			{ threshold, rootMargin }
		);

		observer.observe(element);

		return () => {
			observer.unobserve(element);
		};
	}, [threshold, rootMargin, triggerOnce, hasTriggered]);

	return { isVisible, elementRef };
}

/**
 * Hook for preloading resources
 */
export function useResourcePreloader() {
	const preloadedResources = useRef(new Set<string>());

	const preloadResource = useCallback((href: string, as: string, type?: string) => {
		if (preloadedResources.current.has(href)) return;

		performanceUtils.preloadResource(href, as, type);
		preloadedResources.current.add(href);
	}, []);

	const prefetchResource = useCallback((href: string) => {
		if (preloadedResources.current.has(href)) return;

		performanceUtils.prefetchResource(href);
		preloadedResources.current.add(href);
	}, []);

	return { preloadResource, prefetchResource };
}

/**
 * Hook for monitoring component performance
 */
export function usePerformanceMonitor(componentName: string) {
	const renderCount = useRef(0);
	const mountTime = useRef(performance.now());
	const lastRenderTime = useRef(performance.now());

	useEffect(() => {
		renderCount.current += 1;
		const now = performance.now();
		lastRenderTime.current = now;
	});

	const getPerformanceStats = useCallback(() => {
		return {
			componentName,
			renderCount: renderCount.current,
			timeSinceMount: performance.now() - mountTime.current,
			timeSinceLastRender: performance.now() - lastRenderTime.current,
		};
	}, [componentName]);

	return { getPerformanceStats };
}

/**
 * Hook for optimizing list rendering with virtualization hints
 */
export function useListOptimization<T>(
	items: T[],
	options: {
		pageSize?: number;
		estimatedItemHeight?: number;
		overscan?: number;
	} = {}
) {
	const { pageSize = 50, estimatedItemHeight = 50, overscan = 5 } = options;

	const getVisibleRange = useCallback((scrollTop: number, containerHeight: number) => {
		const startIndex = Math.max(0, Math.floor(scrollTop / estimatedItemHeight) - overscan);
		const endIndex = Math.min(
			items.length - 1,
			Math.ceil((scrollTop + containerHeight) / estimatedItemHeight) + overscan
		);

		return { startIndex, endIndex };
	}, [items.length, estimatedItemHeight, overscan]);

	const getItemStyle = useCallback((index: number) => {
		return {
			position: 'absolute' as const,
			top: index * estimatedItemHeight,
			height: estimatedItemHeight,
			width: '100%',
		};
	}, [estimatedItemHeight]);

	return {
		getVisibleRange,
		getItemStyle,
		totalHeight: items.length * estimatedItemHeight,
		pageSize,
	};
}
