// shared/hooks/useThemeModeToggle.ts
'use client';

import { useCallback } from 'react';
import { useUIStore } from '@/shared/stores/uiStore'; // Assuming path

export const useThemeModeToggle = () => {
    const ThemeChanger = useUIStore((state) => state.ThemeChanger);

    const toggledark = useCallback(() => {
        const currentClass = useUIStore.getState().class; // Get latest state
        const currentDataNavLayout = useUIStore.getState().dataNavLayout;

        const newClass = currentClass === 'dark' ? 'light' : 'dark';
        const newDataHeaderStyles = newClass;
        const newDataMenuStyles = currentDataNavLayout === 'horizontal' ? newClass : "dark";

        ThemeChanger({
            class: newClass,
            dataHeaderStyles: newDataHeaderStyles,
            dataMenuStyles: newDataMenuStyles,
            bodyBg: '', lightRgb: '', darkBg: '', inputBorder: '', gray: '',
        });

        // Update localStorage
        if (newClass === 'dark') {
            localStorage.setItem("xintradarktheme", "dark");
            localStorage.removeItem("xintralighttheme");
        } else {
            localStorage.setItem("xintralighttheme", "light");
            localStorage.removeItem("xintradarktheme");
        }
        localStorage.removeItem("xintraMenu");
        localStorage.removeItem("xintraHeader");
        localStorage.removeItem("hs_theme");
    }, [ThemeChanger]); // Depends on ThemeChanger action

    return {
        toggledark,
    };
};
