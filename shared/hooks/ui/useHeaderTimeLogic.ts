// shared/hooks/useHeaderTimeLogic.ts
'use client';

import { useMemo } from 'react';
import { useAuthStore } from '@/shared/stores/authStore';

interface UseHeaderTimeLogicReturn {
    timezone: string;
}

/**
 * Custom hook for header timezone display logic
 *
 * Gets the user's timezone from their profile data and formats it as GMT offset
 * Displays only the timezone information, not the current time
 */
export const useHeaderTimeLogic = (): UseHeaderTimeLogicReturn => {
    const { user } = useAuthStore();

    const timezone = useMemo(() => {
        /**
         * Get timezone offset in minutes from UTC
         * @param timezoneString - Timezone string (e.g., "Asia/Kolkata")
         * @returns Offset in minutes from UTC
         */
        const getTimezoneOffset = (timezoneString: string): number => {
            try {
                const now = new Date();
                const utcTime = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
                const targetTime = new Date(now.toLocaleString('en-US', { timeZone: timezoneString }));

                return (targetTime.getTime() - utcTime.getTime()) / (1000 * 60);
            } catch {
                return 0; // Default to UTC offset
            }
        };

        /**
         * Convert timezone string to GMT format
         * @param timezoneString - Timezone string (e.g., "Asia/Kolkata", "America/New_York")
         * @returns GMT formatted string (e.g., "GMT+05:30", "GMT-05:00")
         */
        const convertToGMTFormat = (timezoneString: string): string => {
            try {
                const offset = getTimezoneOffset(timezoneString);

                // Convert offset to hours and minutes
                const hours = Math.floor(Math.abs(offset) / 60);
                const minutes = Math.abs(offset) % 60;
                const sign = offset >= 0 ? '+' : '-';

                return `GMT${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
            } catch {
                // Fallback to UTC if conversion fails
                return 'GMT+00:00';
            }
        };

        if (user?.timezone) {
            return convertToGMTFormat(user.timezone);
        }
        // Default fallback timezone
        return 'GMT+00:00';
    }, [user?.timezone]);

    return {
        timezone,
    };
};
