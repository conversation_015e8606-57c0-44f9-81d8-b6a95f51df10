import { useMemo } from "react";

export interface UseAuthenticationBackgroundReturn {
  backgroundImages: string[];
}

/**
 * Custom hook for authentication background images
 * Provides the background image array used for cycling animations in authentication pages
 * Extracted from useSignIn to be reusable across authentication components
 */
export const useAuthenticationBackground = (): UseAuthenticationBackgroundReturn => {
  // Background images for authentication pages
  const backgroundImages = useMemo(() => [
    '/assets/login/loginbgVariant1.svg',
    '/assets/login/loginbgVariant2.svg',
    '/assets/login/loginbgVariant3.svg',
    '/assets/login/loginbgVariant4.svg'
  ], []);

  return {
    backgroundImages,
  };
};

export default useAuthenticationBackground;
