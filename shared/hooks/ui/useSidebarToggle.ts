// shared/hooks/useSidebarToggle.ts
'use client';

import { useRef, useEffect, useCallback } from 'react';
import { useUIStore } from '@/shared/stores/uiStore';

// Assuming closeMenu utility is separate if needed for horizontal nav styles
// import { closeMenu as closeSidebarMenu } from '@/shared/layouts-components/sidebar/some-utility-file';

export const useSidebarToggle = () => {
    const ThemeChanger = useUIStore((state) => state.ThemeChanger);
    const overlayRef = useRef<HTMLDivElement | null>(null);

    // Memoized menuClose function
    const menuClose = useCallback(() => {
        const _currentTheme = useUIStore.getState(); // Get latest state

        // Close sidebar for smaller screens
        if (window.innerWidth <= 992) {
            ThemeChanger({ toggled: "close" });
        }
        // Remove active class from overlay regardless of screen size
        if (overlayRef.current) {
            overlayRef.current.classList.remove("active");
        }

        // Logic for closing horizontal menu items (if `closeMenu` utility is external)
        // You need to decide if you want this hook to manage specific menu item active states
        // or if that logic stays entirely within the Sidebar component itself.
        // If it's an external utility like `closeMenu`, ensure it's imported correctly.
        // Example: if (currentTheme.dataNavLayout === "horizontal" || currentTheme.dataNavStyle === "menu-click" || currentTheme.dataNavStyle === "icon-click") {
        //     closeSidebarMenu(MENUITEMS); // Assuming MENUITEMS is accessible
        // }
    }, [ThemeChanger]); // Depends on ThemeChanger

    // Memoized toggleSidebar function
    const toggleSidebar = useCallback(() => {
        const currentTheme = useUIStore.getState(); // Get current UI state for logic
        const sidemenuType = currentTheme.dataNavLayout;
        const navStyle = currentTheme.dataNavStyle; // Get navStyle here

        if (window.innerWidth >= 992) { // Desktop View
            if (sidemenuType === "vertical") {
                const verticalStyle = currentTheme.dataVerticalStyle;

                // Console logs from your original code
                // console.log('currentTheme.toggled: ', currentTheme.toggled);
                // console.log(verticalStyle, 'verticalStyle');

                switch (verticalStyle) {
                    case "closed":
                        ThemeChanger({ dataNavStyle: "" });
                        if (currentTheme.toggled === "close-menu-close") {
                            ThemeChanger({ toggled: "" });
                        } else {
                            ThemeChanger({ toggled: "close-menu-close" });
                        }
                        break;
                    case "overlay":
                        ThemeChanger({ dataNavStyle: "" });
                        if (currentTheme.toggled === "icon-overlay-close") {
                            ThemeChanger({ toggled: "", iconOverlay: "" });
                        } else {
                            ThemeChanger({ toggled: "icon-overlay-close", iconOverlay: "" }); // Removed window.innerWidth check here as it's already >= 992
                        }
                        break;
                    case "icontext":
                        ThemeChanger({ dataNavStyle: "" });
                        if (currentTheme.toggled === "icon-text-close") {
                            ThemeChanger({ toggled: "" });
                        } else {
                            ThemeChanger({ toggled: "icon-text-close" });
                        }
                        break;
                    case "doublemenu":
                        // Removed redundant ThemeChanger({ dataNavStyle: "" }); line
                        if (currentTheme.toggled === "double-menu-open") {
                            ThemeChanger({ toggled: "double-menu-close" });
                        } else {
                            let sidemenu = document.querySelector(".side-menu__item.active");
                            if (sidemenu) {
                                ThemeChanger({ toggled: "double-menu-open" });
                                if (sidemenu.nextElementSibling) {
                                    sidemenu.nextElementSibling.classList.add("double-menu-active");
                                } else {
                                    ThemeChanger({ toggled: "double-menu-close" });
                                }
                            }
                        }
                        break;
                    case "detached":
                        if (currentTheme.toggled === "detached-close") {
                            ThemeChanger({ toggled: "", iconOverlay: "" });
                        } else {
                            ThemeChanger({ toggled: "detached-close", iconOverlay: "" });
                        }
                        break;
                    case "default":
                        ThemeChanger({ toggled: "" });
                        break;
                }

                // Handle navStyle (menu-click, menu-hover, icon-click, icon-hover)
                switch (navStyle) {
                    case "menu-click":
                        if (currentTheme.toggled === "menu-click-closed") {
                            ThemeChanger({ toggled: "" });
                        } else {
                            ThemeChanger({ toggled: "menu-click-closed" });
                        }
                        break;
                    case "menu-hover":
                        if (currentTheme.toggled === "menu-hover-closed") {
                            ThemeChanger({ toggled: "" });
                        } else {
                            ThemeChanger({ toggled: "menu-hover-closed" });
                        }
                        break;
                    case "icon-click":
                        if (currentTheme.toggled === "icon-click-closed") {
                            ThemeChanger({ toggled: "" });
                        } else {
                            ThemeChanger({ toggled: "icon-click-closed" });
                        }
                        break;
                    case "icon-hover":
                        if (currentTheme.toggled === "icon-hover-closed") {
                            ThemeChanger({ toggled: "" });
                        } else {
                            ThemeChanger({ toggled: "icon-hover-closed" });
                        }
                        break;
                }
            }
        } else { // Mobile View (window.innerWidth < 992)
            if (currentTheme.toggled === "close") {
                ThemeChanger({ toggled: "open" });
                setTimeout(() => {
                    // Check for overlayRef.current BEFORE trying to modify its classList
                    if (overlayRef.current) {
                        overlayRef.current.classList.remove("active"); // Ensure it's clean before adding
                        overlayRef.current.classList.add("active");

                        // Add event listener to overlay for closing sidebar on click
                        // Use { once: true } to prevent multiple listeners
                        overlayRef.current.addEventListener("click", () => {
                            if (overlayRef.current) { // Check again inside the listener
                                overlayRef.current.classList.remove("active");
                                menuClose();
                            }
                        }, { once: true });
                    }
                }, 100);
            } else { // If sidebar is open, close it
                ThemeChanger({ toggled: "close" });
                // Also remove active class from overlay immediately if closing
                if (overlayRef.current) {
                    overlayRef.current.classList.remove("active");
                }
            }
        }
    }, [ThemeChanger, menuClose]); // Dependencies

    // Effect for responsive sidebar overlay (attaches/removes resize listener)
    useEffect(() => {
        const resizeHandler = () => {
            if (window.innerWidth >= 992) {
                if (overlayRef.current) {
                    overlayRef.current.classList.remove("active");
                }
            }
        };
        window.addEventListener("resize", resizeHandler);
        return () => {
            window.removeEventListener("resize", resizeHandler);
        };
    }, []); // Runs once on mount, cleans up on unmount

    return {
        overlayRef, // Pass the ref back to the component to attach to JSX
        toggleSidebar,
        menuClose, // Pass menuClose back if the overlay uses it
    };
};
