import React, { useState, useRef, useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { HORIZONTAL_MENUITEMS } from '@/shared/layouts-components/sidebar/nav';

interface MenuItem {
  title: string;
  icon: React.ReactNode;
  type: 'link' | 'sub' | 'modal';
  path?: string;
  active?: boolean;
  selected?: boolean;
  children?: MenuItem[];
}

interface UseHorizontalNavigationReturn {
  pathname: string;
  activeDropdown: string | null;
  isMobileMenuOpen: boolean;
  dropdownRef: React.RefObject<HTMLDivElement | null>;
  mobileMenuRef: React.RefObject<HTMLDivElement | null>;
  menuItems: typeof HORIZONTAL_MENUITEMS;
  isMenuItemActive: (item: MenuItem) => boolean;
  toggleDropdown: (title: string) => void;
  setActiveDropdown: (title: string | null) => void;
  setIsMobileMenuOpen: (open: boolean) => void;
  closeMobileMenu: () => void;
}

export const useHorizontalNavigation = (): UseHorizontalNavigationReturn => {
  const pathname = usePathname();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Close dropdown and mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Check if a menu item is active based on current path
  const isMenuItemActive = useCallback((item: MenuItem): boolean => {
    if (item.type === 'link' && item.path) {
      return pathname === item.path || pathname.startsWith(item.path + '/');
    }
    if (item.type === 'sub' && item.children) {
      return item.children.some(child =>
        child.path && (pathname === child.path || pathname.startsWith(child.path + '/'))
      );
    }
    if (item.type === 'modal') {
      // Modal items are not considered "active" in the traditional sense
      // since they open modals rather than navigate to pages
      return false;
    }
    return false;
  }, [pathname]);

  // Toggle dropdown for sub-menu items
  const toggleDropdown = useCallback((title: string) => {
    setActiveDropdown(activeDropdown === title ? null : title);
  }, [activeDropdown]);

  // Close mobile menu
  const closeMobileMenu = useCallback(() => {
    setIsMobileMenuOpen(false);
  }, []);

  return {
    pathname,
    activeDropdown,
    isMobileMenuOpen,
    dropdownRef,
    mobileMenuRef,
    menuItems: HORIZONTAL_MENUITEMS,
    isMenuItemActive,
    toggleDropdown,
    setActiveDropdown,
    setIsMobileMenuOpen,
    closeMobileMenu,
  };
};
