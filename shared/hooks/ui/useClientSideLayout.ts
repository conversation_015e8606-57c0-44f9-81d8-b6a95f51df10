// shared/hooks/useClientSideLayout.ts - Hook to handle client-side layout detection
"use client";

import { useState, useEffect } from "react";

interface UseClientSideLayoutReturn {
	isHydrated: boolean;
	isLargeScreen: boolean;
	shouldUseHorizontalLayout: boolean;
}

/**
 * Custom hook to handle client-side layout detection and prevent hydration mismatches
 * This ensures that responsive layouts work correctly on initial page load
 */
export function useClientSideLayout(): UseClientSideLayoutReturn {
	const [isHydrated, setIsHydrated] = useState(false);
	const [isLargeScreen, setIsLargeScreen] = useState(false);

	useEffect(() => {
		// Mark as hydrated
		setIsHydrated(true);

		// Check initial screen size
		const checkScreenSize = () => {
			if (typeof window !== "undefined") {
				// Using 1280px as xl breakpoint (Tailwind's xl breakpoint)
				setIsLargeScreen(window.innerWidth >= 1280);
			}
		};

		// Check initial size
		checkScreenSize();

		// Add resize listener
		const handleResize = () => {
			checkScreenSize();
		};

		window.addEventListener("resize", handleResize);

		// Cleanup
		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, []);

	return {
		isHydrated,
		isLargeScreen,
		shouldUseHorizontalLayout: isHydrated && isLargeScreen,
	};
}
