// shared/hooks/ui/index.ts
// UI-related hooks

export { useFullScreenToggle } from './useFullScreenToggle';
export { useHeaderLogic } from './useHeaderLogic';
export { useHeaderBalanceLogic } from './useHeaderBalanceLogic';
export { useHeaderTimeLogic } from './useHeaderTimeLogic';
export { useSideBar } from './useSideBar';
export { useSidebarToggle } from './useSidebarToggle';
export { useThemeModeToggle } from './useThemeModeToggle';
export { useClientSideLayout } from './useClientSideLayout';
export { usePerformanceOptimization } from './usePerformanceOptimization';
export { useAuthenticationBackground } from './useAuthenticationBackground';
export { useHorizontalNavigation } from './useHorizontalNavigation';
