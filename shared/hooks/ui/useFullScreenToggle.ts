// shared/hooks/useFullScreenToggle.ts
'use client';

import { useState, useCallback } from 'react';

export const useFullScreenToggle = () => {
    const [fullScreen, setFullScreen] = useState(false);

    const toggleFullScreen = useCallback(() => {
        const elem = document.documentElement;

        if (!document.fullscreenElement) {
            elem.requestFullscreen().then(() => setFullScreen(true));
        } else {
            document.exitFullscreen().then(() => setFullScreen(false));
        }
    }, []); // No dependencies, as it only interacts with DOM APIs

    return {
        fullScreen,
        toggleFullScreen,
    };
};
