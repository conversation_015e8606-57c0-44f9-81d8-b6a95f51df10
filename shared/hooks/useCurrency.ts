// shared/hooks/useCurrency.ts
// Enhanced Currency Hook with additional utilities
// Provides easy access to currency functionality throughout the app

"use client";

import { useCurrency as useBaseCurrency } from '@/shared/contexts/CurrencyContext';
import { convertLegacyCurrencyId } from '@/shared/config/currencyConfig';

/**
 * Enhanced Currency Hook
 * 
 * Extends the base currency context with additional utility functions
 * for common currency operations throughout the application.
 * 
 * Usage:
 * ```tsx
 * const { 
 *   formatCurrency, 
 *   formatCurrencyAmount, 
 *   getCurrencySymbol,
 *   renderCurrencyWithIcon 
 * } = useCurrency();
 * ```
 */
export const useCurrency = () => {
  const baseCurrency = useBaseCurrency();

  /**
   * Format currency with full display (amount + symbol/icon)
   * Replaces the old formatCurrency utility function
   * 
   * @param amount - Amount to format
   * @param options - Formatting options
   * @returns Formatted currency string (for text-based) or just number (for icon-based)
   */
  const formatCurrency = (
    amount: number | string | null | undefined,
    options?: {
      decimalPlaces?: number;
      locale?: string;
      hideZero?: boolean;
    }
  ): string => {
    return baseCurrency.formatAmount(amount, {
      includeSymbol: !baseCurrency.hasCustomIcon, // Include symbol only for non-icon currencies
      ...options
    });
  };

  /**
   * Format currency amount only (no symbol/icon)
   * Replaces the old formatCurrencyAmount utility function
   * 
   * @param amount - Amount to format
   * @param options - Formatting options
   * @returns Formatted number string
   */
  const formatCurrencyAmount = (
    amount: number | string | null | undefined,
    options?: {
      decimalPlaces?: number;
      locale?: string;
      hideZero?: boolean;
    }
  ): string => {
    return baseCurrency.formatAmount(amount, {
      includeSymbol: false,
      ...options
    });
  };

  /**
   * Get currency symbol (text-based currencies only)
   * For icon-based currencies, use renderCurrencyWithIcon instead
   * 
   * @returns Currency symbol string
   */
  const getCurrencySymbol = (): string => {
    return baseCurrency.getSymbol();
  };

  /**
   * Handle legacy currency ID conversion
   * Maintains backward compatibility with old currency ID system
   * 
   * @param currencyId - Legacy currency ID (string or number)
   * @returns Current currency code
   */
  const convertLegacyId = (currencyId: string | number): string => {
    return convertLegacyCurrencyId(currencyId);
  };

  /**
   * Check if amount should be displayed with custom icon
   * Useful for conditional rendering logic
   * 
   * @returns boolean indicating if custom icon should be used
   */
  const shouldUseCustomIcon = (): boolean => {
    return baseCurrency.hasCustomIcon;
  };

  /**
   * Get formatted currency display info
   * Returns all the information needed to render currency properly
   * 
   * @param amount - Amount to format
   * @param context - UI context for icon variant
   * @returns Object with formatted amount and display info
   */
  const getCurrencyDisplayInfo = (
    amount: number | string | null | undefined,
    context?: 'header' | 'card' | 'modal' | 'table' | 'form' | 'default'
  ) => {
    const formattedAmount = formatCurrencyAmount(amount);
    
    return {
      formattedAmount,
      hasCustomIcon: baseCurrency.hasCustomIcon,
      iconVariant: baseCurrency.getIconVariant(context),
      symbol: baseCurrency.getSymbol(),
      currencyCode: baseCurrency.getCode(),
      currencyName: baseCurrency.getName(),
      shouldShowIcon: baseCurrency.hasCustomIcon,
      shouldShowSymbol: !baseCurrency.hasCustomIcon
    };
  };

  /**
   * Format currency for table display
   * Optimized for table cell rendering
   * 
   * @param amount - Amount to format
   * @returns Formatted amount (number only for icon currencies)
   */
  const formatForTable = (amount: number | string | null | undefined): string => {
    return formatCurrencyAmount(amount);
  };

  /**
   * Format currency for card display
   * Optimized for summary card rendering
   * 
   * @param amount - Amount to format
   * @returns Formatted amount (number only for icon currencies)
   */
  const formatForCard = (amount: number | string | null | undefined): string => {
    return formatCurrencyAmount(amount);
  };

  /**
   * Format currency for header display
   * Optimized for header balance display
   * 
   * @param amount - Amount to format
   * @returns Formatted amount (number only for icon currencies)
   */
  const formatForHeader = (amount: number | string | null | undefined): string => {
    return formatCurrencyAmount(amount);
  };

  /**
   * Format large amounts with K/M suffixes
   * Useful for compact displays
   * 
   * @param amount - Amount to format
   * @returns Formatted amount with suffix (e.g., "1.2K", "5.5M")
   */
  const formatCompact = (amount: number | string | null | undefined): string => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (!numAmount || isNaN(numAmount)) return '0';

    if (numAmount >= 1000000) {
      return `${(numAmount / 1000000).toFixed(1)}M`;
    } else if (numAmount >= 1000) {
      return `${(numAmount / 1000).toFixed(1)}K`;
    }
    return formatCurrencyAmount(numAmount);
  };

  // Return all currency functionality
  return {
    // Base currency context
    ...baseCurrency,
    
    // Enhanced formatting functions
    formatCurrency,
    formatCurrencyAmount,
    getCurrencySymbol,
    
    // Utility functions
    convertLegacyId,
    shouldUseCustomIcon,
    getCurrencyDisplayInfo,
    
    // Context-specific formatters
    formatForTable,
    formatForCard,
    formatForHeader,
    formatCompact,
  };
};

export default useCurrency;
