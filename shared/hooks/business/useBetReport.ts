// shared/hooks/business/useBetReport.ts - Custom hook for bet report business logic
"use client";

import { useBetWinReportQuery } from "@/shared/query/useBetWinReportQuery";
import { useAuthStore } from "@/shared/stores/authStore";
import { BetReportFilters, BetReportResponse, DEFAULT_BET_REPORT_FILTERS } from "@/shared/types/report-types";
import { calculateBetReportCount } from "@/shared/utils/countApiUtils";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

interface UseBetReportReturn {
  // State
  filters: BetReportFilters;

  // Data
  betReportResponse: BetReportResponse | undefined;
  isLoading: boolean;
  isError: boolean;
  error: any;
  isFetching: boolean;

  // Computed values
  totalBets: number;
  totalBetAmount: number;
  totalWinAmount: number;
  ggr: number;

  // Actions
  handleFilterChange: (newFilters: Partial<BetReportFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

interface UseBetReportOptions {
  initialBetReportResponse?: BetReportResponse | null;
  initialFilters?: BetReportFilters;
}

/**
 * Custom hook that encapsulates all bet report business logic
 * Handles authentication, data fetching, filtering, and pagination
 *
 * Features:
 * - Accepts initial server-side data for SSR optimization
 * - Optimized re-rendering with useCallback for handlers
 * - Graceful fallback to client-side fetching
 */
export const useBetReport = (options: UseBetReportOptions = {}): UseBetReportReturn => {
  const { initialBetReportResponse = null, initialFilters } = options;
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // State for filters - use initial filters if provided, otherwise use defaults
  const [filters, setFilters] = useState<BetReportFilters>(initialFilters || DEFAULT_BET_REPORT_FILTERS);

  // Fetch bet report using the query hook with initial data
  const {
    data: betReportResponse,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useBetWinReportQuery(filters, initialBetReportResponse);

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<BetReportFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }));
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page
    }));
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Computed values
  // For bet reports (POST API), calculate count from response data length
  const totalBets = betReportResponse?.count || calculateBetReportCount(betReportResponse?.data || []);
  const totalBetAmount = betReportResponse?.totalBetAmount || 0;
  const totalWinAmount = betReportResponse?.totalWinAmount || 0;
  const ggr = betReportResponse?.ggr || 0;

  return {
    // State
    filters,

    // Data
    betReportResponse,
    isLoading,
    isError,
    error,
    isFetching,

    // Computed values
    totalBets,
    totalBetAmount,
    totalWinAmount,
    ggr,

    // Actions
    handleFilterChange,
    handlePageChange,
    handleRefresh,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};
