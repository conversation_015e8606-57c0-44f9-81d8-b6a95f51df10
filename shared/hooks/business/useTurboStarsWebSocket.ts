// shared/hooks/useTurboStarsWebSocket.ts
import { useEffect, useRef, useState, useCallback } from "react";
import {
  turboStarsWebSocketService,
  WebSocketEventHandler
} from "@/shared/services/webSocketService";
import {
  CashierTurboPlaceBetDetails,
  WebSocketConnectionConfig
} from "@/shared/types/user-management-types";

interface UseTurboStarsWebSocketOptions {
  autoConnect?: boolean;
  onBetPlaced?: (_notification: CashierTurboPlaceBetDetails) => void;
}

interface UseTurboStarsWebSocketReturn {
  isConnected: boolean;
  connectionState: string;
  lastNotification: CashierTurboPlaceBetDetails | null;
  connect: (_config: WebSocketConnectionConfig) => Promise<void>;
  disconnect: () => void;
  error: string | null;
}

export const useTurboStarsWebSocket = (
  options: UseTurboStarsWebSocketOptions = {}
): UseTurboStarsWebSocketReturn => {
  const { autoConnect = false, onBetPlaced } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState("DISCONNECTED");
  const [lastNotification, setLastNotification] = useState<CashierTurboPlaceBetDetails | null>(null);
  const [error, setError] = useState<string | null>(null);

  const unsubscribeRef = useRef<(() => void) | null>(null);
  const configRef = useRef<WebSocketConnectionConfig | null>(null);

  // Handle bet placement notifications
  const handleBetPlaced = useCallback<WebSocketEventHandler>((notification) => {
    setLastNotification(notification);
    setError(null);

    // Call the optional callback
    if (onBetPlaced) {
      onBetPlaced(notification);
    }
  }, [onBetPlaced]);

  // Connect to WebSocket
  const connect = useCallback(async (config: WebSocketConnectionConfig) => {
    try {
      setError(null);
      configRef.current = config;

      // Subscribe to bet notifications
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
      unsubscribeRef.current = turboStarsWebSocketService.onBetPlaced(handleBetPlaced);

      // Connect to WebSocket
      await turboStarsWebSocketService.connect(config);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to connect to WebSocket";
      setError(errorMessage);

      throw err;
    }
  }, [handleBetPlaced]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    turboStarsWebSocketService.disconnect();
    setError(null);

  }, []);

  // Auto-connect with configuration if enabled
  // Note: Auto-connect is disabled in production - connections are managed by TurboStarsWebSocketProvider
  useEffect(() => {
    if (autoConnect) {

      // Auto-connect functionality is intentionally disabled to prevent hardcoded values
      // Use TurboStarsWebSocketProvider for proper connection management
    }
  }, [autoConnect, connect]);

  // Monitor connection state with event-driven updates instead of polling
  useEffect(() => {
    const updateConnectionState = () => {
      const connected = turboStarsWebSocketService.isConnected;
      const state = turboStarsWebSocketService.connectionState;

      setIsConnected(connected);
      setConnectionState(state);
    };

    // Check initial state
    updateConnectionState();

    // Subscribe to state changes instead of polling
    const unsubscribe = turboStarsWebSocketService.addStateChangeListener(updateConnectionState);

    return () => {
      unsubscribe();
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  return {
    isConnected,
    connectionState,
    lastNotification,
    connect,
    disconnect,
    error
  };
};

// Hook for components that only need to listen to bet notifications
export const useTurboStarsBetNotifications = (
  onBetPlaced?: (_notification: CashierTurboPlaceBetDetails) => void
) => {
  const [lastNotification, setLastNotification] = useState<CashierTurboPlaceBetDetails | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  const handleBetPlaced = useCallback<WebSocketEventHandler>((notification) => {
    setLastNotification(notification);

    if (onBetPlaced) {
      onBetPlaced(notification);
    }
  }, [onBetPlaced]);

  useEffect(() => {
    // Subscribe to bet notifications
    unsubscribeRef.current = turboStarsWebSocketService.onBetPlaced(handleBetPlaced);

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [handleBetPlaced]);

  return {
    lastNotification,
    isConnected: turboStarsWebSocketService.isConnected,
    connectionState: turboStarsWebSocketService.connectionState
  };
};
