'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useSportsbookStore } from '@/shared/stores/sportsbookStore';

interface UseInlineSportsbookLogicReturn {
  isOpen: boolean;
  url: string | null;
  userName: string | null;
  error: string | null;
  isIframeLoading: boolean;
  iframeError: string | null;
  iframeRef: React.RefObject<HTMLIFrameElement | null>;
  handleIframeLoad: () => void;
  handleIframeError: () => void;
  handleClose: () => void;
}

/**
 * Custom hook for InlineSportsbook business logic
 * 
 * Handles:
 * - Sportsbook state management
 * - Iframe loading and error states
 * - Keyboard event handling (ESC key)
 * - Body scroll prevention
 * - URL change handling
 */
export const useInlineSportsbookLogic = (): UseInlineSportsbookLogicReturn => {
  const {
    isOpen,
    url,
    userName,
    error,
    closeSportsbook,
    clearError
  } = useSportsbookStore();

  const [isIframeLoading, setIsIframeLoading] = useState(true);
  const [iframeError, setIframeError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Handle iframe load events
  const handleIframeLoad = () => {
    setIsIframeLoading(false);
    setIframeError(null);
  };

  const handleIframeError = () => {
    setIsIframeLoading(false);
    setIframeError("Failed to load sportsbook content");
  };

  // Handle close action
  const handleClose = () => {
    clearError();
    closeSportsbook();
  };

  // Handle escape key to close sportsbook
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        closeSportsbook();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscapeKey);
      // Prevent body scroll when sportsbook is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, closeSportsbook]);

  // Reset iframe loading state when URL changes
  useEffect(() => {
    if (url) {
      setIsIframeLoading(true);
      setIframeError(null);
    }
  }, [url]);

  return {
    isOpen,
    url,
    userName,
    error,
    isIframeLoading,
    iframeError,
    iframeRef,
    handleIframeLoad,
    handleIframeError,
    handleClose,
  };
};
