"use client";

import React, { useRef, useState, useCallback, useEffect } from 'react';
import { Html5QrcodeScanner } from 'html5-qrcode';

interface UseQRScannerOptions {
  onScanSuccess?: (result: string) => void;
  onScanError?: (error: string) => void;
}

interface UseQRScannerReturn {
  isScanning: boolean;
  error: string | null;
  scannerRef: React.RefObject<HTMLDivElement | null>;
  startScanning: () => Promise<void>;
  stopScanning: () => void;
  hasCamera: () => Promise<boolean>;
}

/**
 * Custom hook for QR code scanning functionality
 *
 * Provides camera access, QR code scanning, and error handling
 * for web-based QR code reading using the html5-qrcode library.
 */
export const useQRScanner = (options: UseQRScannerOptions = {}): UseQRScannerReturn => {
  const { onScanSuccess, onScanError } = options;

  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const scannerRef = useRef<HTMLDivElement>(null);
  const html5QrcodeScannerRef = useRef<Html5QrcodeScanner | null>(null);

  /**
   * Check if camera is available
   */
  const hasCamera = useCallback(async (): Promise<boolean> => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.some(device => device.kind === 'videoinput');
    } catch {
      return false;
    }
  }, []);

  /**
   * Start QR code scanning
   */
  const startScanning = useCallback(async (): Promise<void> => {
    if (isScanning) return;

    try {
      setError(null);
      setIsScanning(true);

      // Check camera availability
      const cameraAvailable = await hasCamera();
      if (!cameraAvailable) {
        throw new Error('No camera found. Please ensure camera permissions are granted.');
      }

      // Wait for the next tick to ensure the DOM element is rendered
      setTimeout(() => {
        if (!scannerRef.current) {
          // eslint-disable-next-line no-console
          console.error('Scanner container not found');
          setError('Scanner container not available');
          setIsScanning(false);
          return;
        }

        try {
          // Create Html5QrcodeScanner instance
          html5QrcodeScannerRef.current = new Html5QrcodeScanner(
            'qr-reader', // Use the fixed ID
            {
              qrbox: {
                width: 250,
                height: 250,
              },
              fps: 5,
              rememberLastUsedCamera: true,
              aspectRatio: 1.0,
            },
            false // verbose
          );

          // Define success callback
          const onScanSuccessCallback = (decodedText: string) => {
            if (onScanSuccess) {
              onScanSuccess(decodedText);
            }
          };

          // Define error callback
          const onScanFailureCallback = (error: string) => {
            // Handle scan failure silently - this is called frequently during scanning
            // Only log actual errors, not normal scanning attempts
            if (error && !error.includes('No QR code found')) {
              // eslint-disable-next-line no-console
              console.warn('QR scan error:', error);
            }
          };

          // Start scanning
          html5QrcodeScannerRef.current.render(onScanSuccessCallback, onScanFailureCallback);
        } catch (scannerError) {
          const errorMessage = scannerError instanceof Error ? scannerError.message : 'Failed to initialize scanner';
          setError(errorMessage);
          setIsScanning(false);
          if (onScanError) {
            onScanError(errorMessage);
          }
        }
      }, 100); // Small delay to ensure DOM is ready

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start camera';
      setError(errorMessage);
      setIsScanning(false);

      if (onScanError) {
        onScanError(errorMessage);
      }
    }
  }, [isScanning, hasCamera, onScanSuccess, onScanError]);

  /**
   * Stop QR code scanning
   */
  const stopScanning = useCallback((): void => {
    if (html5QrcodeScannerRef.current) {
      html5QrcodeScannerRef.current.clear().catch(error => {
        // eslint-disable-next-line no-console
        console.error('Failed to clear QR scanner:', error);
      });
      html5QrcodeScannerRef.current = null;
    }
    setIsScanning(false);
    setError(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, [stopScanning]);

  return {
    isScanning,
    error,
    scannerRef,
    startScanning,
    stopScanning,
    hasCamera,
  };
};

/**
 * Utility function to extract bet ID from QR code data
 * 
 * Supports various formats:
 * - Full betslip URLs: https://domain.com/betslip/bet-id
 * - Direct bet IDs: UUID format
 * - Custom bet ID formats
 */
export const extractBetIdFromQR = (qrData: string): string | null => {
  try {
    // Remove whitespace
    const cleanData = qrData.trim();

    // Check for betslip URL pattern
    const urlPatterns = [
      /\/betslip\/([a-zA-Z0-9-]+)/i,
      /\/bet\/([a-zA-Z0-9-]+)/i,
      /betId=([a-zA-Z0-9-]+)/i,
      /bet_id=([a-zA-Z0-9-]+)/i,
    ];

    for (const pattern of urlPatterns) {
      const match = cleanData.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    // Check if it's a direct UUID format
    const uuidPattern = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
    if (uuidPattern.test(cleanData)) {
      return cleanData;
    }

    // Check for other common bet ID formats
    const betIdPatterns = [
      /^[A-Z0-9]{6,20}$/i, // Alphanumeric bet IDs
      /^BET[0-9]{6,15}$/i, // BET prefix format
      /^[0-9]{8,20}$/,     // Numeric bet IDs
    ];

    for (const pattern of betIdPatterns) {
      if (pattern.test(cleanData)) {
        return cleanData;
      }
    }

    return null;
  } catch {
    return null;
  }
};

/**
 * Utility function to validate bet ID format
 */
export const isValidBetId = (betId: string): boolean => {
  if (!betId || typeof betId !== 'string') return false;

  const cleanBetId = betId.trim();
  if (cleanBetId.length < 6) return false;

  // Check common bet ID patterns
  const validPatterns = [
    /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i, // UUID
    /^[A-Z0-9]{6,20}$/i, // Alphanumeric
    /^BET[0-9]{6,15}$/i, // BET prefix
    /^[0-9]{8,20}$/,     // Numeric
  ];

  return validPatterns.some(pattern => pattern.test(cleanBetId));
};
