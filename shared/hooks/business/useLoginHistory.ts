// shared/hooks/business/useLoginHistory.ts - Custom hook for login history business logic
"use client";

import { useLoginHistoryQuery } from "@/shared/query/useLoginHistoryQuery";
import { useAuthStore } from "@/shared/stores/authStore";
import {
  DEFAULT_LOGIN_HISTORY_FILTERS,
  LoginHistoryFilters,
  LoginHistoryResponse
} from "@/shared/types/user-management-types";
import { fetchLoginHistoryCount } from "@/shared/utils/countApiUtils";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

interface UseLoginHistoryReturn {
  // State
  filters: LoginHistoryFilters;

  // Data
  loginHistoryResponse: LoginHistoryResponse | undefined;
  isLoading: boolean;
  isError: boolean;
  error: any;
  isFetching: boolean;

  // Computed values
  totalRecords: number;

  // Actions
  handleFilterChange: (newFilters: Partial<LoginHistoryFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

interface UseLoginHistoryOptions {
  initialLoginHistoryResponse?: LoginHistoryResponse | null;
  initialFilters?: LoginHistoryFilters;
  playerId?: string; // For user-specific login history
}

/**
 * Custom hook that encapsulates all login history business logic
 * Handles authentication, data fetching, filtering, and pagination
 *
 * Features:
 * - Accepts initial server-side data for SSR optimization
 * - Optimized re-rendering with useCallback for handlers
 * - Graceful fallback to client-side fetching
 * - Support for user-specific login history via playerId
 */
export const useLoginHistory = (options: UseLoginHistoryOptions = {}): UseLoginHistoryReturn => {
  const { initialLoginHistoryResponse = null, initialFilters, playerId } = options;
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // State for filters - use initial filters if provided, otherwise use defaults
  const [filters, setFilters] = useState<LoginHistoryFilters>(() => {
    const baseFilters = initialFilters || DEFAULT_LOGIN_HISTORY_FILTERS;
    // Add playerId if provided for user-specific history
    return playerId ? { ...baseFilters, playerId } : baseFilters;
  });

  // Fetch login history using the query hook with initial data
  const {
    data: loginHistoryResponse,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useLoginHistoryQuery(filters, initialLoginHistoryResponse);

  // Fetch total count separately using count API
  const {
    data: totalCount,
    isLoading: isCountLoading,
    refetch: refetchCount
  } = useQuery({
    queryKey: ['loginHistoryCount', filters],
    queryFn: () => fetchLoginHistoryCount(filters),
    enabled: !!useAuthStore.getState().token,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    placeholderData: keepPreviousData, // Maintain previous data during filter changes
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<LoginHistoryFilters>) => {
    const updatedFilters = { ...newFilters };

    // Convert dateRange to startDate and endDate for API compatibility
    if (newFilters.dateRange) {
      updatedFilters.startDate = newFilters.dateRange.startDate;
      updatedFilters.endDate = newFilters.dateRange.endDate;
    }

    setFilters(prev => ({
      ...prev,
      ...updatedFilters,
      page: "1" // Reset to first page when filters change
    }));
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page: page.toString()
    }));
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
    refetchCount();
  }, [refetch, refetchCount]);

  // Computed values
  // Use count API result if available, otherwise fall back to response count
  const totalRecords = totalCount || loginHistoryResponse?.count || 0;

  return {
    // State
    filters,

    // Data
    loginHistoryResponse,
    isLoading: isLoading || isCountLoading,
    isError,
    error,
    isFetching,

    // Computed values
    totalRecords,

    // Actions
    handleFilterChange,
    handlePageChange,
    handleRefresh,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};
