// shared/hooks/business/useFinancialReport.ts - Custom hook for financial report business logic
"use client";

import { useFinancialReportQuery } from "@/shared/query/useFinancialReportQuery";
import { useAuthStore } from "@/shared/stores/authStore";
import { DEFAULT_FINANCIAL_REPORT_FILTERS, FinancialReportFilters, FinancialReportResponse } from "@/shared/types/report-types";
import { fetchFinancialReportCount } from "@/shared/utils/countApiUtils";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

interface UseFinancialReportReturn {
  // State
  filters: FinancialReportFilters;

  // Data
  financialReportResponse: FinancialReportResponse | null;
  isLoading: boolean;
  isError: boolean;
  error: any;
  isFetching: boolean;

  // Computed values
  totalTransactions: number;
  totalAmount: number;
  totalDeposits: number;
  totalWithdrawals: number;

  // Actions
  handleFilterChange: (newFilters: Partial<FinancialReportFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

interface UseFinancialReportOptions {
  initialFinancialReportResponse?: FinancialReportResponse | null;
  initialFilters?: FinancialReportFilters;
  userId?: string; // Optional userId for user-specific filtering
}

/**
 * Custom hook that encapsulates all financial report business logic
 * Handles authentication, data fetching, filtering, and pagination
 *
 * Features:
 * - Accepts initial server-side data for SSR optimization
 * - Optimized re-rendering with useCallback for handlers
 * - Graceful fallback to client-side fetching
 */
export const useFinancialReport = (options: UseFinancialReportOptions = {}): UseFinancialReportReturn => {
  const { initialFinancialReportResponse = null, initialFilters, userId } = options;
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // State for filters - merge initial filters with defaults
  // If userId is provided, automatically set playerId for user-specific filtering
  const [filters, setFilters] = useState<FinancialReportFilters>(() => {
    const baseFilters = { ...DEFAULT_FINANCIAL_REPORT_FILTERS, ...initialFilters };
    return userId ? { ...baseFilters, playerId: userId } : baseFilters;
  });

  // Fetch financial report using the query hook with initial data
  const {
    data: financialReportResponse,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useFinancialReportQuery(filters, initialFinancialReportResponse);

  // Fetch total count separately using count API
  const {
    data: totalCount,
    isLoading: isCountLoading,
    refetch: refetchCount
  } = useQuery({
    queryKey: ['financialReportCount', filters],
    queryFn: () => fetchFinancialReportCount(filters),
    enabled: !!useAuthStore.getState().token,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    placeholderData: keepPreviousData, // Maintain previous data during filter changes
  });
  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<FinancialReportFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }));
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page
    }));
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
    refetchCount();
  }, [refetch, refetchCount]);

  // Computed values
  // Use count API result if available, otherwise fall back to response count
  const totalTransactions = totalCount || financialReportResponse?.count || 0;
  const totalAmount = financialReportResponse?.totalAmount || 0;
  const totalDeposits = financialReportResponse?.totalDeposits || 0;
  const totalWithdrawals = financialReportResponse?.totalWithdrawals || 0;

  return {
    // State
    filters,

    // Data
    financialReportResponse: financialReportResponse ?? null,
    isLoading: isLoading || isCountLoading,
    isError,
    error,
    isFetching,

    // Computed values
    totalTransactions,
    totalAmount,
    totalDeposits,
    totalWithdrawals,

    // Actions
    handleFilterChange,
    handlePageChange,
    handleRefresh,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};
