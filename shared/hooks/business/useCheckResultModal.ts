import React, { useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '@/shared/stores/authStore';
import { useQRScanner, extractBetIdFromQR, isValidBetId } from '@/shared/hooks/business/useQRScanner';
import { useBetSettlement } from '@/shared/hooks/business/useBetSettlement';
import { BetReportData } from '@/shared/types/report-types';
import { BetSettlementData } from '@/shared/UI/modals/BetSettlementModal';

export type ModalState = 'input' | 'results';

export interface UseCheckResultModalReturn {
  // State
  currentState: ModalState;
  betId: string;
  searchBetId: string;
  error: string | null;
  noData: string;
  copyMessage: string;
  shareMessage: string;
  rawBetData: any;
  isLoading: boolean;
  selectedBetData: BetReportData | null;
  isSettlementModalOpen: boolean;

  // QR Scanner
  isScanning: boolean;
  scanError: string | null;
  scannerRef: React.RefObject<HTMLDivElement | null>;

  // Settlement
  isSettling: boolean;

  // Actions
  setBetId: (betId: string) => void;
  setCurrentState: (state: ModalState) => void;
  handleQRScannerClick: () => Promise<void>;
  handleSearch: () => Promise<void>;
  handleSettlementClick: () => void;
  handleSettlementSubmit: (settlementData: BetSettlementData) => Promise<void>;
  handleCopy: () => void;
  handleShare: () => void;
  resetModalState: () => void;
  setIsSettlementModalOpen: (isOpen: boolean) => void;
}

/**
 * Custom hook for managing CheckResultModal state and business logic
 * Handles error state management, success state, loading states, and other modal state
 */
export const useCheckResultModal = (): UseCheckResultModalReturn => {
  // Basic state
  const [currentState, setCurrentState] = useState<ModalState>('input');
  const [betId, setBetId] = useState('');
  const [searchBetId, setSearchBetId] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [noData, setNoData] = useState('');
  const [copyMessage, setCopyMessage] = useState('');
  const [shareMessage, setShareMessage] = useState('');

  // Data state
  const [rawBetData, setRawBetData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Settlement state
  const [isSettlementModalOpen, setIsSettlementModalOpen] = useState(false);
  const [selectedBetData, setSelectedBetData] = useState<BetReportData | null>(null);

  // QR Scanner hook
  const { isScanning, error: scanError, scannerRef, startScanning, stopScanning } = useQRScanner({
    onScanSuccess: (qrData) => {
      const extractedBetId = extractBetIdFromQR(qrData);
      if (extractedBetId && isValidBetId(extractedBetId)) {
        setBetId(extractedBetId);
        setSearchBetId(extractedBetId);
        stopScanning();
        // Clear any previous errors when successful scan occurs
        setError(null);
        setNoData('');
      } else {
        setError('Invalid QR code format. Please scan a valid bet QR code.');
      }
    },
    onScanError: (scanErrorMsg) => {
      setError(scanErrorMsg);
    },
  });

  // Bet settlement hook
  const { settleBet, isSettling } = useBetSettlement({
    onSuccess: () => {
      setIsSettlementModalOpen(false);
      // Refetch the bet data to update the modal betslip
      if (searchBetId) {
        setCurrentState('input');
        setTimeout(() => {
          setCurrentState('results');
          setSearchBetId(searchBetId); // triggers useEffect to refetch
        }, 0);
      }
    },
    onError: (error) => {
      setError(error.message);
    }
  });

  // Fetch raw bet data when searchBetId changes
  useEffect(() => {
    if (!searchBetId || currentState !== 'results') {
      setRawBetData(null);
      return;
    }

    const fetchRawBetData = async () => {
      setIsLoading(true);
      setNoData('');
      setError(null); // Clear any previous errors when starting new fetch

      try {
        const token = useAuthStore.getState().token;
        if (!token) {
          throw new Error('Authentication token is required');
        }

        const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;
        if (!baseUrl) {
          throw new Error('Reporting backend URL is not configured');
        }

        const requestBody = {
          page: "1",
          limit: "1",
          transactionId: searchBetId
        };

        const response = await fetch(`${baseUrl}/api/v2/cashier/betWinReport`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch bet data: ${response.status}`);
        }

        const data = await response.json();
        if (data.success === 1 && data.data?.[0]) {
          setRawBetData(data.data[0]);
          // Clear any previous error states when successful
          setError(null);
          setNoData('');
        } else {
          // This is not an error - it's just no data found
          setNoData('Bet not found');
          setCurrentState('input');
          // Don't throw an error here - this is expected behavior
        }
      } catch (error) {
        // Only set error for actual errors (network issues, auth issues, etc.)
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        setError(errorMessage);
        setCurrentState('input');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRawBetData();
  }, [searchBetId, currentState]);

  // Handle QR scanner initialization
  const handleQRScannerClick = useCallback(async () => {
    setError(null);
    setNoData('');
    try {
      await startScanning();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start QR scanner');
    }
  }, [startScanning]);

  // Handle search for bet details
  const handleSearch = useCallback(async () => {
    if (!betId.trim()) {
      setError('Please enter a bet ID');
      return;
    }

    if (!isValidBetId(betId)) {
      setError('Invalid bet ID format');
      return;
    }

    // Clear previous states when starting new search
    setError(null);
    setNoData('');
    setSearchBetId(betId);
    setCurrentState('results');
  }, [betId]);

  // Handle settlement button click
  const handleSettlementClick = useCallback(() => {
    if (!rawBetData) return;
    setSelectedBetData(rawBetData);
    setIsSettlementModalOpen(true);
  }, [rawBetData]);

  // Handle settlement submission
  const handleSettlementSubmit = useCallback(async (settlementData: BetSettlementData) => {
    if (!selectedBetData?.betId) return;

    try {
      await settleBet(selectedBetData.betId, settlementData);
    } catch {
      // Error is handled by the hook
    }
  }, [selectedBetData, settleBet]);

  // Handle copy button
  const handleCopy = useCallback(() => {
    if (rawBetData?.betId) {
      navigator.clipboard.writeText(`Bet ID: ${rawBetData.betId}`);
      setCopyMessage('Copied');
      setTimeout(() => setCopyMessage(''), 2000);
    }
  }, [rawBetData]);

  // Handle share button
  const handleShare = useCallback(() => {
    if (rawBetData?.betQrCode) {
      navigator.clipboard.writeText(rawBetData.betQrCode);
      setShareMessage('Link Generated');
      setTimeout(() => setShareMessage(''), 2000);
    }
  }, [rawBetData]);

  // Reset modal state
  const resetModalState = useCallback(() => {
    setCurrentState('input');
    setBetId('');
    setSearchBetId('');
    setError(null);
    setNoData('');
    setCopyMessage('');
    setShareMessage('');
    setRawBetData(null);
    setSelectedBetData(null);
    setIsSettlementModalOpen(false);
    stopScanning();
  }, [stopScanning]);

  return {
    // State
    currentState,
    betId,
    searchBetId,
    error,
    noData,
    copyMessage,
    shareMessage,
    rawBetData,
    isLoading,
    selectedBetData,
    isSettlementModalOpen,

    // QR Scanner
    isScanning,
    scanError,
    scannerRef,

    // Settlement
    isSettling,

    // Actions
    setBetId,
    setCurrentState,
    handleQRScannerClick,
    handleSearch,
    handleSettlementClick,
    handleSettlementSubmit,
    handleCopy,
    handleShare,
    resetModalState,
    setIsSettlementModalOpen,
  };
};
