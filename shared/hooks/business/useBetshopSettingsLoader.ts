// shared/hooks/business/useBetshopSettingsLoader.ts
'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useAuthStore } from '@/shared/stores/authStore';
import { BetshopSettingsWithRetry } from '@/shared/query/betshopSettings';

interface UseBetshopSettingsLoaderOptions {
  /** Delay before first attempt in milliseconds */
  initialDelay?: number;
  /** Maximum number of retry attempts */
  maxRetries?: number;
  /** Whether to automatically load settings when authenticated */
  autoLoad?: boolean;
}

interface UseBetshopSettingsLoaderReturn {
  /** Whether settings are currently being loaded */
  isLoading: boolean;
  /** Any error that occurred during loading */
  error: Error | null;
  /** Whether settings have been successfully loaded */
  isLoaded: boolean;
  /** Manually trigger settings loading */
  loadSettings: () => Promise<void>;
}

/**
 * Hook to manage betshop settings loading with proper timing and retry logic
 * Prevents duplicate API calls and provides loading state management
 */
export const useBetshopSettingsLoader = (
  options: UseBetshopSettingsLoaderOptions = {}
): UseBetshopSettingsLoaderReturn => {
  const {
    initialDelay = 1500,
    maxRetries = 3,
    autoLoad = true
  } = options;

  const { token, user, isAuthenticated } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // Track if we've already attempted to load settings for this session
  const hasAttemptedLoad = useRef(false);
  const currentTokenRef = useRef<string | null>(null);
  const loadingRef = useRef(false);

  const loadSettings = useCallback(async (): Promise<void> => {
    if (!token || !user?.tenantId || loadingRef.current) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await BetshopSettingsWithRetry({
        AuthToken: token,
        tenantId: user.tenantId,
        maxRetries,
        initialDelay
      });

      setIsLoaded(true);
      hasAttemptedLoad.current = true;
      currentTokenRef.current = token;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to load betshop settings');
      setError(error);
      // eslint-disable-next-line no-console
      console.warn('Betshop settings loading failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, [token, user?.tenantId, maxRetries, initialDelay]);

  useEffect(() => {
    // Only auto-load if enabled and we haven't attempted for this token yet
    if (
      autoLoad &&
      isAuthenticated &&
      token &&
      user?.tenantId &&
      !isLoading &&
      (!hasAttemptedLoad.current || currentTokenRef.current !== token)
    ) {
      loadSettings();
    }
  }, [isAuthenticated, token, user?.tenantId, autoLoad, loadSettings, isLoading]);

  // Reset state when user logs out
  useEffect(() => {
    if (!isAuthenticated || !token) {
      setIsLoaded(false);
      setError(null);
      setIsLoading(false);
      hasAttemptedLoad.current = false;
      currentTokenRef.current = null;
      loadingRef.current = false;
    }
  }, [isAuthenticated, token]);

  return {
    isLoading,
    error,
    isLoaded,
    loadSettings
  };
};
