// shared/hooks/business/useBetSettlement.ts
import { useAuthStore } from '@/shared/stores/authStore';
import { BetSettlementData } from '@/shared/UI/components';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback, useState } from 'react';

interface SettleBetRequest {
  transactionId: string;
  action: 'Settle' | 'Failed';
  comments: string;
}

interface SettleBetResponse {
  success: boolean;
  message: string;
  data?: any;
  errors?: string[];
}

interface UseBetSettlementOptions {
  onSuccess?: (data: SettleBetResponse) => void;
  onError?: (error: Error) => void;
}

interface UseBetSettlementReturn {
  // State
  isSettling: boolean;
  error: string | null;

  // Actions
  settleBet: (betId: string, settlementData: BetSettlementData) => Promise<void>;
  clearError: () => void;
}

/**
 * Custom hook for bet settlement operations
 * 
 * Handles the API integration for settling bet transactions with proper error handling,
 * success messaging, and automatic data refresh.
 * 
 * Features:
 * - POST request to settlement API endpoint
 * - Bearer token authentication
 * - Error handling with user-friendly messages
 * - Automatic query invalidation for data refresh
 * - Loading states and success callbacks
 */
export const useBetSettlement = (options: UseBetSettlementOptions = {}): UseBetSettlementReturn => {
  const { onSuccess, onError } = options;
  const { token } = useAuthStore();
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);

  /**
   * API function to settle a bet transaction
   */
  const settleBetAPI = async (request: SettleBetRequest): Promise<SettleBetResponse> => {
    if (!token) {
      throw new Error('Authentication token is required');
    }

    // Get the reporting backend URL from environment variables
    const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;

    if (!baseUrl) {
      throw new Error('Reporting backend URL is not configured');
    }

    const response = await fetch(`${baseUrl}/api/v2/cashier/bet/payout/settlement`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(request),
    });

    // Check for 401 errors and handle them globally
    await checkAndHandle401(response);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message ||
        errorData.error ||
        `Failed to settle bet: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data;
  };

  /**
   * Mutation for settling bets
   */
  const settleBetMutation = useMutation<SettleBetResponse, Error, SettleBetRequest>({
    mutationFn: settleBetAPI,
    onSuccess: (data) => {
      // Clear any previous errors
      setError(null);

      // Invalidate bet report queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['betWinReport'] });
      queryClient.invalidateQueries({ queryKey: ['betReport'] });

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(data);
      }
    },
    onError: (error) => {
      // Handle error with user-friendly message
      const errorMessage = error.message || 'Failed to settle bet transaction';
      setError(errorMessage);

      // Handle query error globally
      handleQueryError(error);

      // Call error callback if provided
      if (onError) {
        onError(error);
      }
    },
  });

  /**
   * Main function to settle a bet
   */
  const settleBet = useCallback(async (betId: string, settlementData: BetSettlementData) => {
    try {
      // Clear any previous errors
      setError(null);

      // Prepare the request payload
      const request: SettleBetRequest = {
        transactionId: betId, // Use betId as transactionId as specified in requirements
        action: settlementData.action,
        comments: settlementData.comments.trim(),
      };

      // Execute the mutation
      await settleBetMutation.mutateAsync(request);
    } catch (error) {
      //eslint-disable-next-line no-console
      console.error('Settlement error:', error);
    }
  }, [settleBetMutation]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    isSettling: settleBetMutation.isPending,
    error,

    // Actions
    settleBet,
    clearError,
  };
};

export default useBetSettlement;
