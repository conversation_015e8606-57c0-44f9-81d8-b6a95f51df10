// shared/hooks/useLogoutHandler.ts
'use client';

import { useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';
import { useLogoutMutation } from '@/shared/query/mutations/useLogoutMutation'; // Assuming path
import { useAuthStore } from '@/shared/stores/authStore'; // Assuming path

export const useLogoutHandler = () => {
    const router = useRouter();
    const { mutate: logoutMutate, isPending: isLoggingOut, isSuccess: isLogoutSuccess } = useLogoutMutation();
    const { clearAuth: _clearAuth } = useAuthStore();

    const handleLogout = useCallback(() => {
        logoutMutate(); // Trigger the logout API call and client-side state clearing
    }, [logoutMutate]); // Depends on the mutate function from useLogoutMutation

    // Effect to redirect after successful logout (clearAuth and queryClient.clear)
    // This ensures the user is redirected to the login page after their session is fully ended.
    useEffect(() => {
        if (isLogoutSuccess) {
            router.replace('/authentication/sign-in/basic'); // Redirect to login page
        }
    }, [isLogoutSuccess, router]);

    return {
        handleLogout,
        isLoggingOut,
    };
};
