'use client';

import { useEffect, useMemo, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore } from '@/shared/stores/authStore';
import { useSportsbookStore } from '@/shared/stores/sportsbookStore';

interface UseContentLayoutLogicReturn {
  isAuthenticated: boolean;
  hasHydrated: boolean;
  isBetPopupOpen: boolean;
  currentBetDetails: any;
  hideBetPopup: () => void;
  pathname: string;
}

/**
 * Custom hook for ContentLayout business logic
 * 
 * Handles:
 * - Authentication state management
 * - Route protection and redirection
 * - Sportsbook popup state
 * - Hydration state management
 */
export const useContentLayoutLogic = (): UseContentLayoutLogicReturn => {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // Get bet popup state from sportsbook store
  const { isBetPopupOpen, currentBetDetails, hideBetPopup } = useSportsbookStore();

  // Prevent infinite redirects by tracking if we've already redirected
  const hasRedirected = useRef(false);

  // Define public paths that don't require authentication (memoized for performance)
  const publicPaths = useMemo(() => [
    '/authentication/sign-in',
    '/authentication/sign-up',
    '/authentication/forgot-password',
    '/authentication/reset-password',
    '/authentication/lockscreen',
    '/authentication/under-maintainance',
    '/authentication/coming-soon',
    '/authentication/error/401-error',
    '/authentication/error/404-error',
    '/authentication/error/500-error',
    '/bet-details', // Public bet details page for printing betslips
  ], []);

  // Handle authentication and routing logic
  useEffect(() => {
    // Only run after hydration to prevent SSR/client mismatch
    if (!_hasHydrated) return;

    const isPublicPath = publicPaths.includes(pathname);

    // Redirect unauthenticated users to sign-in (except for public paths)
    if (!isAuthenticated && !isPublicPath && !hasRedirected.current) {
      hasRedirected.current = true;
      router.replace('/authentication/sign-in');
      return;
    }

    // Redirect authenticated users away from auth pages to dashboard
    if (isAuthenticated && isPublicPath && !hasRedirected.current) {
      hasRedirected.current = true;
      router.replace('/dashboard');
      return;
    }

    // Reset redirect flag when we're on the correct page
    if ((isAuthenticated && !isPublicPath) || (!isAuthenticated && isPublicPath)) {
      hasRedirected.current = false;
    }
  }, [isAuthenticated, _hasHydrated, pathname, router, publicPaths]);

  return {
    isAuthenticated,
    hasHydrated: _hasHydrated,
    isBetPopupOpen,
    currentBetDetails,
    hideBetPopup,
    pathname,
  };
};
