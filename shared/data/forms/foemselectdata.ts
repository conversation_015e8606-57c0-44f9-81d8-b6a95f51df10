// Placeholder data for form select components

export const basicselect = [
  { value: "option1", label: "Option 1" },
  { value: "option2", label: "Option 2" },
  { value: "option3", label: "Option 3" },
];

export const defaultselect = [
  { value: "default1", label: "Default 1" },
  { value: "default2", label: "Default 2" },
  { value: "default3", label: "Default 3" },
];

export const multiDropdownSelect = [
  { value: "multi1", label: "Multi 1" },
  { value: "multi2", label: "Multi 2" },
  { value: "multi3", label: "Multi 3" },
];

export const Multipleselectdata = [
  { value: "multiple1", label: "Multiple 1" },
  { value: "multiple2", label: "Multiple 2" },
  { value: "multiple3", label: "Multiple 3" },
];

export const Multipleselectdata1 = [
  { value: "multiple1_1", label: "Multiple 1.1" },
  { value: "multiple1_2", label: "Multiple 1.2" },
  { value: "multiple1_3", label: "Multiple 1.3" },
];

export const Optionwithnosearch = [
  { value: "nosearch1", label: "No Search 1" },
  { value: "nosearch2", label: "No Search 2" },
  { value: "nosearch3", label: "No Search 3" },
];

export const SingleGroup = [
  {
    label: "Group 1",
    options: [
      { value: "group1_1", label: "Group 1 Option 1" },
      { value: "group1_2", label: "Group 1 Option 2" },
    ]
  },
  {
    label: "Group 2",
    options: [
      { value: "group2_1", label: "Group 2 Option 1" },
      { value: "group2_2", label: "Group 2 Option 2" },
    ]
  }
];

export const Tomselect = [
  { value: "tom1", label: "Tom 1" },
  { value: "tom2", label: "Tom 2" },
  { value: "tom3", label: "Tom 3" },
];

const formSelectData = {
  basicselect,
  defaultselect,
  multiDropdownSelect,
  Multipleselectdata,
  Multipleselectdata1,
  Optionwithnosearch,
  SingleGroup,
  Tomselect,
};

export default formSelectData;
