// Placeholder component for form wizard

import React from 'react';

const HorizontalNonLinearStepper: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Form Wizard</h3>
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center">
            1
          </div>
          <span>Step 1: Basic Information</span>
        </div>
        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center">
            2
          </div>
          <span>Step 2: Additional Details</span>
        </div>
        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center">
            3
          </div>
          <span>Step 3: Review & Submit</span>
        </div>
      </div>
    </div>
  );
};

export default HorizontalNonLinearStepper;
