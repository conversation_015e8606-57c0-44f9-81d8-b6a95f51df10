"use client";
import { useUIStore } from "@/shared/stores/uiStore";

// Ensure DOM types are available
declare global {
    interface EventListener {
        (evt: Event): void;
    }
}

// Also, provide direct access to the current state.
const getUIState = useUIStore.getState; // Helper to get current state outside React component lifecycle
const _setUIState = useUIStore.setState; // Helper to directly set state, similar to <PERSON>ustand's `set`

// Define the type for the action function that will be passed around.
// It matches the signature of the ThemeChanger action in your useUIStore.
type ActionFunction = (value: Partial<ReturnType<typeof getUIState>>) => void;

// Color picker interfaces removed - simplified theme system

// Type for page loading setter function
type PageLoadingSetter = (loading: boolean) => void;

// ================================================================================================================
// THEME & LAYOUT CHANGING FUNCTIONS (Updated to use Zustand's ThemeChanger)
// ================================================================================================================

// Simplified theme functions - inline implementations (no external dependencies)

export const Light = (actionFunction: ActionFunction, _clicked?: any): void => {
    actionFunction({
        class: "light",
        dataHeaderStyles: "light",
        dataMenuStyles: "light",
    });
    localStorage.setItem("xintralighttheme", "light");
    localStorage.removeItem("xintradarktheme");
};

export const Dark = (actionFunction: ActionFunction, _clicked?: any): void => {
    actionFunction({
        class: "dark",
        dataHeaderStyles: "dark",
        dataMenuStyles: "dark",
    });
    localStorage.setItem("xintradarktheme", "dark");
    localStorage.removeItem("xintralighttheme");
};

export const Ltr = (actionFunction: ActionFunction): void => {
    actionFunction({ dir: "ltr" });
    localStorage.setItem("xintraltr", "ltr");
    localStorage.removeItem("xintrartl");
};

export const Rtl = (actionFunction: ActionFunction): void => {
    actionFunction({ dir: "rtl" });
    localStorage.setItem("xintrartl", "rtl");
    localStorage.removeItem("xintraltr");
};

export const HorizontalClick = (actionFunction: ActionFunction): void => {
    actionFunction({
        dataNavLayout: "horizontal",
        dataVerticalStyle: "",
        toggled: "",
    });
    localStorage.setItem("xintralayout", "horizontal");
};

// All other theme customization functions removed - only light/dark mode supported

// Simplified menu functions removed - only horizontal layout supported

// Complex menu functions removed - simplified layout system
// Complex menu layout functions removed - simplified to horizontal only

// Color picker components removed - simplified theme system

// Background color picker and bgImage functions removed - simplified theme system

export const Reset = (actionFunction: ActionFunction) => {
    // Simplified reset - only light theme and horizontal layout
    Light(actionFunction);
    HorizontalClick(actionFunction);
    localStorage.clear(); // Clears all localStorage items
};

// Reset1 function removed - simplified theme system

export const LocalStorageBackup = (actionFunction: ActionFunction, setPageLoading: PageLoadingSetter) => {
    // Simplified LocalStorageBackup - only light/dark theme and horizontal layout

    // Default to dark theme if no theme preference exists in localStorage
    if (localStorage.xintradarktheme) {
        Dark(actionFunction);
    } else if (localStorage.xintralighttheme) {
        Light(actionFunction);
    } else {
        // No theme preference found - default to dark theme
        Dark(actionFunction);
    }

    // RTL support
    (localStorage.xintrartl) ? Rtl(actionFunction) : "";

    // Force horizontal layout
    HorizontalClick(actionFunction);

    // All complex theme customization removed - simplified to light/dark only

    setPageLoading(true);
};

// LocalStorageBackup1 function removed - simplified theme system
