"use client";
import React, { useState, useEffect } from 'react';

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

import { Suspense } from 'react';

const DayCounterComponent: React.FC = () => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    // Set target date to 30 days from now
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() + 30);

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = targetDate.getTime() - now;

      if (distance > 0) {
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        clearInterval(timer);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="grid grid-cols-4 gap-4 text-center">
      <div className="bg-primary/10 dark:bg-primary/20 rounded-lg p-4">
        <div className="text-2xl font-bold text-primary dark:text-primary">
          {timeLeft.days.toString().padStart(2, '0')}
        </div>
        <div className="text-sm text-textmuted dark:text-textmuted/50 mt-1">
          Days
        </div>
      </div>
      <div className="bg-primary/10 dark:bg-primary/20 rounded-lg p-4">
        <div className="text-2xl font-bold text-primary dark:text-primary">
          {timeLeft.hours.toString().padStart(2, '0')}
        </div>
        <div className="text-sm text-textmuted dark:text-textmuted/50 mt-1">
          Hours
        </div>
      </div>
      <div className="bg-primary/10 dark:bg-primary/20 rounded-lg p-4">
        <div className="text-2xl font-bold text-primary dark:text-primary">
          {timeLeft.minutes.toString().padStart(2, '0')}
        </div>
        <div className="text-sm text-textmuted dark:text-textmuted/50 mt-1">
          Minutes
        </div>
      </div>
      <div className="bg-primary/10 dark:bg-primary/20 rounded-lg p-4">
        <div className="text-2xl font-bold text-primary dark:text-primary">
          {timeLeft.seconds.toString().padStart(2, '0')}
        </div>
        <div className="text-sm text-textmuted dark:text-textmuted/50 mt-1">
          Seconds
        </div>
      </div>
    </div>
  );

};

export const DayCounter: React.FC = () => (
  <Suspense>
    <DayCounterComponent />
  </Suspense>
);
