import type { BasicTableData as BasicTableDataType, UserTableData, OrderTableData, TransactionTableData as TransactionTableDataType, GroupTableData as GroupTableDataType } from '../types/table-types';

// Basic table data
export const BasicTableData: BasicTableDataType[] = [
  { id: 1, name: "<PERSON>", date: "21,Dec 2023", number: "+1234-12340", status: "Completed", color: "primary" },
  { id: 2, name: "<PERSON><PERSON>", date: "29,April 2023", number: "+1523-12459", status: "Failed", color: "secondary" },
  { id: 3, name: "<PERSON><PERSON>", date: "30,Nov 2023", number: "+1982-16234", status: "Successful", color: "success" },
  { id: 4, name: "<PERSON><PERSON><PERSON><PERSON>", date: "18,Mar 2023", number: "+1526-10729", status: "Pending", color: "warning" },
];

// User table data (requires face imports - will be handled in component)
export const UserTableDataTemplate: Omit<UserTableData, 'src'>[] = [
  { id: 1, name: '<PERSON><PERSON><PERSON>', mail: 'kimosu<PERSON><EMAIL>', color: 'success/10', class: '', text: 'Active', class1: 'online', color1: "success" },
  { id: 2, name: 'Hasimna', mail: '<EMAIL>', color: 'light', class: 'text-default', text: 'Inactive', class1: 'offline', color1: "dark" },
  { id: 3, name: 'Azimo Khan', mail: '<EMAIL>', color: 'success/10', class: '', text: 'Active', class1: 'online', color1: "success" },
  { id: 4, name: 'Samantha Julia', mail: '<EMAIL>', color: 'success/10', class: '', text: 'Active', class1: 'online', color1: "success" },
];

// Order table data template (requires face imports)
export const OrderTableDataTemplate: Omit<OrderTableData, 'src'>[] = [
  { id: 1, order: "#0007", date: "26-04-2022", name: "Mayor Kelly" },
  { id: 2, order: "#0008", date: "15-02-2022", name: "Wicky Kross" },
  { id: 3, order: "#0009", date: "23-05-2022", name: "Julia Cam" }
];

// Transaction table data
export const TransactionTableData: TransactionTableDataType[] = [
  { id: 1, name: "Harshrath", date: "24 May 2022", text: "#5182-3467", transactionid: "#5182-3467", status: "Fixed", color: "primary" },
  { id: 2, name: "Zozo Hadid", date: "02 July 2022", text: "#5182-3412", transactionid: "#5182-3412", status: "In Progress", color: "warning" },
  { id: 3, name: "Martiana", date: "15 April 2022", text: "#5182-3423", transactionid: "#5182-3423", status: "Completed", color: "success" },
  { id: 4, name: "Alex Carey", date: "17 March 2022", text: "#5182-3456", transactionid: "#5182-3456", status: "Pending", color: "danger" }
];

// Group table data
export const GroupTableData: GroupTableDataType[] = [
  { id: 1, product: "Smart Watch", seller: "Slowtrack.inc", percent: "24.23%", sold: "250/1786", icon: "up", color: "success" },
  { id: 2, product: "White Sneakers", seller: "American & Co.inc", percent: "12.45%", sold: "123/985", icon: "down", color: "danger" },
  { id: 3, product: "Baseball Bat", seller: "Sports Company", percent: "06.64%", sold: "124/232", icon: "up", color: "success" },
  { id: 4, product: "Black Hoodie", seller: "Renonds Fabrics", percent: "14.42%", sold: "192/2456", icon: "up", color: "success" },
];

// Bordered table data
export const BorderedTableData: TransactionTableDataType[] = [
  { id: 1, name: "Harshrath", date: "24 May 2022", text: "#5182-3467", transactionid: "#5182-3467", status: "Fixed", color: "primary" },
  { id: 2, name: "Zozo Hadid", date: "02 July 2022", text: "#5182-3412", transactionid: "#5182-3412", status: "In Progress", color: "warning" },
  { id: 3, name: "Martiana", date: "15 April 2022", text: "#5182-3423", transactionid: "#5182-3423", status: "Completed", color: "success" },
  { id: 4, name: "Alex Carey", date: "17 March 2022", text: "#5182-3456", transactionid: "#5182-3456", status: "Pending", color: "danger" }
];
