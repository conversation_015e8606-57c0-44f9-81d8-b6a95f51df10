export interface LanguageOption {
    flag: string;
    name: string;
}

export const LanguageOptions: LanguageOption[] = [
    { flag: '/assets/images/flags/us_flag.jpg', name: 'English' },
    { flag: '/assets/images/flags/spain_flag.jpg', name: 'español' },
    { flag: '/assets/images/flags/french_flag.jpg', name: 'français' },
    { flag: '/assets/images/flags/uae_flag.jpg', name: 'عربي' },
    { flag: '/assets/images/flags/germany_flag.jpg', name: '<PERSON><PERSON><PERSON>' },
    { flag: '/assets/images/flags/china_flag.jpg', name: '中国人' },
    { flag: '/assets/images/flags/italy_flag.jpg', name: 'Italiano' },
    { flag: '/assets/images/flags/russia_flag.jpg', name: 'Русский' },
];
