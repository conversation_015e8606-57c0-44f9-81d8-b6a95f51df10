import React from 'react';
import { AccordionItem } from '../types/accordion-types';

// Basic accordion data
export const BasicAccordionData: AccordionItem[] = [
  {
    Id: 'hs-basic-collapse-one',
    title: 'Accordion #1',
    Customclass: 'active',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the first item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-heading-one",
    Custombodyclass: "",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  },
  {
    Id: 'hs-basic-collapse-two',
    title: 'Accordion #2',
    Customclass: '',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the second item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-heading-two",
    Custombodyclass: "hidden",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  },
  {
    Id: 'hs-basic-collapse-three',
    title: 'Accordion #3',
    Customclass: '',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the third item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-heading-three",
    Custombodyclass: "hidden",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  }
];

// Always open accordion data
export const AlwaysOpenAccordionData: AccordionItem[] = [
  {
    Id: 'hs-basic-always-open-collapse-one',
    title: 'Accordion #1',
    Customclass: 'active',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the first item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-always-open-heading-one",
    Custombodyclass: "",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  },
  {
    Id: 'hs-basic-always-open-collapse-two',
    title: 'Accordion #2',
    Customclass: '',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the second item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-always-open-heading-two",
    Custombodyclass: "hidden",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  },
  {
    Id: 'hs-basic-always-open-collapse-three',
    title: 'Accordion #3',
    Customclass: '',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the third item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-always-open-heading-three",
    Custombodyclass: "hidden",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  }
];

// No arrow accordion data
export const NoArrowAccordionData: AccordionItem[] = [
  {
    Id: 'hs-basic-no-arrow-collapse-one',
    title: 'Accordion #1',
    Customclass: 'active',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the first item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-no-arrow-heading-one",
    Custombodyclass: "",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  },
  {
    Id: 'hs-basic-no-arrow-collapse-two',
    title: 'Accordion #2',
    Customclass: '',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the second item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-no-arrow-heading-two",
    Custombodyclass: "hidden",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  },
  {
    Id: 'hs-basic-no-arrow-collapse-three',
    title: 'Accordion #3',
    Customclass: '',
    content: (
      <p className="text-gray-800 dark:text-gray-200" >
        <em>This is the third item's accordion body.</em> It is
        hidden by default, until the collapse plugin adds the
        appropriate classes that we use to style each element.These
        classes control the overall appearance, as well as the
        showing and hiding via CSS transitions.
      </p>
    ),
    Mainid: "hs-basic-no-arrow-heading-three",
    Custombodyclass: "hidden",
    Svgcontent1: (
      <>
        <path d="M5 12h14" />
        <path d="M12 5v14" />
      </>
    ),
    Svgcontent2: (
      <>
        <path d="M5 12h14" />
      </>
    )
  }
];
