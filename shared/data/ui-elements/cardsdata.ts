// Placeholder data for cards

export const Backgroundcards = [
  {
    id: 1,
    text1: "Primary Card",
    text2: "This is a primary background card with sample content.",
    class: "bg-primary text-white",
    color: "primary",
    src: "/images/placeholder1.jpg"
  },
  {
    id: 2,
    text1: "Secondary Card",
    text2: "This is a secondary background card with sample content.",
    class: "bg-secondary text-white",
    color: "secondary",
    src: "/images/placeholder2.jpg"
  },
  {
    id: 3,
    text1: "Success Card",
    text2: "This is a success background card with sample content.",
    class: "bg-success text-white",
    color: "success",
    src: "/images/placeholder3.jpg"
  },
  {
    id: 4,
    text1: "Warning Card",
    text2: "This is a warning background card with sample content.",
    class: "bg-warning text-white",
    color: "warning",
    src: "/images/placeholder4.jpg"
  },
];

export const Bordercarddata = [
  {
    id: 1,
    title: "Border Card 1",
    content: "This card has a colored border.",
    badges: [{ text: "New", color: "primary" }],
    images: ["/images/placeholder1.jpg"],
    Class: "border-primary border-2",
    cardclass: "card-border-primary"
  },
  {
    id: 2,
    title: "Border Card 2",
    content: "This card has a different colored border.",
    badges: [{ text: "Popular", color: "secondary" }],
    images: ["/images/placeholder2.jpg"],
    Class: "border-secondary border-2",
    cardclass: "card-border-secondary"
  },
  {
    id: 3,
    title: "Border Card 3",
    content: "This card has another colored border.",
    badges: [{ text: "Featured", color: "success" }],
    images: ["/images/placeholder3.jpg"],
    Class: "border-success border-2",
    cardclass: "card-border-success"
  },
];

export const Cardgroupdata = [
  {
    id: 1,
    title: "Group Card 1",
    content: "First card in the group.",
    src: "/images/placeholder1.jpg",
    badge: "New"
  },
  {
    id: 2,
    title: "Group Card 2",
    content: "Second card in the group.",
    src: "/images/placeholder2.jpg",
    badge: "Popular"
  },
  {
    id: 3,
    title: "Group Card 3",
    content: "Third card in the group.",
    src: "/images/placeholder3.jpg",
    badge: "Featured"
  },
];

export const Gridcards = [
  {
    id: 1,
    title: "Grid Card 1",
    content: "This is the first grid card with sample content.",
    icon: "ti-home",
    color: "text-primary"
  },
  {
    id: 2,
    title: "Grid Card 2",
    content: "This is the second grid card with sample content.",
    icon: "ti-user",
    color: "text-secondary"
  },
  {
    id: 3,
    title: "Grid Card 3",
    content: "This is the third grid card with sample content.",
    icon: "ti-settings",
    color: "text-success"
  },
  {
    id: 4,
    title: "Grid Card 4",
    content: "This is the fourth grid card with sample content.",
    icon: "ti-chart",
    color: "text-warning"
  },
  {
    id: 5,
    title: "Grid Card 5",
    content: "This is the fifth grid card with sample content.",
    icon: "ti-bell",
    color: "text-danger"
  },
  {
    id: 6,
    title: "Grid Card 6",
    content: "This is the sixth grid card with sample content.",
    icon: "ti-mail",
    color: "text-info"
  },
];

const cardsData = {
  Backgroundcards,
  Bordercarddata,
  Cardgroupdata,
  Gridcards,
};

export default cardsData;
