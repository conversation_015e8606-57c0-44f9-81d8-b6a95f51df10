// Color spinner data
export interface ColorSpinnerData {
  id: number;
  color: string;
}

export const ColorSpinnerData: ColorSpinnerData[] = [
  { id: 1, color: 'primary' },
  { id: 2, color: 'secondary' },
  { id: 3, color: 'success' },
  { id: 4, color: 'danger' },
  { id: 5, color: 'warning' },
  { id: 6, color: 'info' },
  { id: 7, color: 'light' },
  { id: 8, color: 'dark' }
];

// Growing spinner data
export interface GrowingSpinnerData {
  id: number;
  color: string;
}

export const GrowingSpinnerData: GrowingSpinnerData[] = [
  { id: 1, color: 'primary' },
  { id: 2, color: 'secondary' },
  { id: 3, color: 'warning' },
  { id: 4, color: 'danger' },
  { id: 5, color: 'success' },
  { id: 6, color: 'info' },
  { id: 7, color: 'black/20 dark:!bg-light dark:animate-ping' },
  { id: 8, color: 'gray-400' }
];

// Button spinner data
export interface ButtonSpinnerData {
  id: number;
  color: string;
}

export const ButtonSpinnerData: ButtonSpinnerData[] = [
  { id: 1, color: 'primary-full' },
  { id: 2, color: 'secondary-full' },
  { id: 3, color: 'warning-full' },
  { id: 4, color: 'danger-full' },
  { id: 5, color: 'success-full' },
  { id: 6, color: 'info-full' },
];
