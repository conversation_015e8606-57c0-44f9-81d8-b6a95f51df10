
export interface ProfileMenuItem {
    id: number;
    type: 'link' | 'divider';
    iconClass?: string;
    text?: string;
    badge?: {
        variant: string;
        customClass: string;
        value: string | number;
    };
    isLogout?: boolean;
}

// Separate interface for Link-type items to enforce href
export interface LinkProfileMenuItem extends ProfileMenuItem {
    type: 'link';
    href: string; // <--- Made mandatory for link type
}

// For divider type (no href)
export interface DividerProfileMenuItem extends ProfileMenuItem {
    type: 'divider';
}

export const ProfileMenuItems: (LinkProfileMenuItem | DividerProfileMenuItem)[] = [
    // User Info (often handled separately in the dropdown header, but could be a list item if preferred)
    // Example if you wanted it as a list item:
    // { id: 0, type: 'info', text: 'Mr.<PERSON>', subText: 'UI/UX Designer' },

    { id: 1, type: 'link', href: '/profile', iconClass: 'fe fe-user p-1 rounded-full bg-primary/10 text-primary me-2 text-[1rem]', text: 'Profile' },
    { id: 2, type: 'link', href: '/email/inbox', iconClass: 'fe fe-mail p-1 rounded-full bg-primary/10 text-primary me-2 text-[1rem]', text: 'Mail Inbox' },
    { id: 3, type: 'link', href: '/file-manager', iconClass: 'fe fe-database p-1 rounded-full bg-primary/10 text-primary klist me-2 text-[1rem]', text: 'File Manager', badge: { variant: "primarytint1color", customClass: "text-white ms-auto text-[0.5625rem]", value: 2 } },
    { id: 4, type: 'link', href: '/settings', iconClass: 'fe fe-settings p-1 rounded-full bg-primary/10 text-primary ings me-2 text-[1rem]', text: 'Settings' },

    // A divider example (if SpkDropdown supports it, or handle it in HeaderProfileDropdown.tsx)
    // { id: 5, type: 'divider' },

    { id: 6, type: 'link', href: '/help', iconClass: 'fe fe-help-circle p-1 rounded-full bg-primary/10 text-primary set me-2 text-[1rem]', text: 'Help' },
    { id: 7, type: 'link', href: '/authentication/sign-in/basic', iconClass: 'fe fe-lock p-1 rounded-full bg-primary/10 text-primary ut me-2 text-[1rem]', text: 'Log Out', isLogout: true },
];
