// Placeholder components for data tables

import React from 'react';

const StickyHeadTable: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Sticky Head Table</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-200">
          <thead className="bg-gray-50 sticky top-0 rounded-lg overflow-hidden">
            <tr className="rounded-lg overflow-hidden">
              <th className="px-4 py-2 border-b rounded-tl-lg">ID</th>
              <th className="px-4 py-2 border-b">Name</th>
              <th className="px-4 py-2 border-b">Email</th>
              <th className="px-4 py-2 border-b rounded-tr-lg">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="px-4 py-2 border-b">1</td>
              <td className="px-4 py-2 border-b"><PERSON></td>
              <td className="px-4 py-2 border-b"><EMAIL></td>
              <td className="px-4 py-2 border-b">Active</td>
            </tr>
            <tr>
              <td className="px-4 py-2 border-b">2</td>
              <td className="px-4 py-2 border-b">Jane Smith</td>
              <td className="px-4 py-2 border-b"><EMAIL></td>
              <td className="px-4 py-2 border-b">Inactive</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export const CustomizedTables: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Customized Tables</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-200 rounded-lg">
          <thead className="bg-primary text-white">
            <tr>
              <th className="px-4 py-2">Product</th>
              <th className="px-4 py-2">Price</th>
              <th className="px-4 py-2">Category</th>
              <th className="px-4 py-2">Stock</th>
            </tr>
          </thead>
          <tbody>
            <tr className="hover:bg-gray-50">
              <td className="px-4 py-2 border-b">Product 1</td>
              <td className="px-4 py-2 border-b">$99.99</td>
              <td className="px-4 py-2 border-b">Electronics</td>
              <td className="px-4 py-2 border-b">50</td>
            </tr>
            <tr className="hover:bg-gray-50">
              <td className="px-4 py-2 border-b">Product 2</td>
              <td className="px-4 py-2 border-b">$149.99</td>
              <td className="px-4 py-2 border-b">Clothing</td>
              <td className="px-4 py-2 border-b">25</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export const DataTabless: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Data Tables</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-4 py-2 text-left">Order ID</th>
              <th className="px-4 py-2 text-left">Customer</th>
              <th className="px-4 py-2 text-left">Date</th>
              <th className="px-4 py-2 text-left">Amount</th>
              <th className="px-4 py-2 text-left">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="px-4 py-2 border-b">#001</td>
              <td className="px-4 py-2 border-b">Alice Johnson</td>
              <td className="px-4 py-2 border-b">2024-01-15</td>
              <td className="px-4 py-2 border-b">$250.00</td>
              <td className="px-4 py-2 border-b">
                <span className="px-2 py-1 bg-green-100 text-green-800 rounded">Completed</span>
              </td>
            </tr>
            <tr>
              <td className="px-4 py-2 border-b">#002</td>
              <td className="px-4 py-2 border-b">Bob Wilson</td>
              <td className="px-4 py-2 border-b">2024-01-16</td>
              <td className="px-4 py-2 border-b">$180.00</td>
              <td className="px-4 py-2 border-b">
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">Pending</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export const ExportCSV: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Export CSV</h3>
      <div className="mb-4">
        <button className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark">
          Export to CSV
        </button>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-2 border-b">Name</th>
              <th className="px-4 py-2 border-b">Position</th>
              <th className="px-4 py-2 border-b">Office</th>
              <th className="px-4 py-2 border-b">Age</th>
              <th className="px-4 py-2 border-b">Salary</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="px-4 py-2 border-b">Tiger Nixon</td>
              <td className="px-4 py-2 border-b">System Architect</td>
              <td className="px-4 py-2 border-b">Edinburgh</td>
              <td className="px-4 py-2 border-b">61</td>
              <td className="px-4 py-2 border-b">$320,800</td>
            </tr>
            <tr>
              <td className="px-4 py-2 border-b">Garrett Winters</td>
              <td className="px-4 py-2 border-b">Accountant</td>
              <td className="px-4 py-2 border-b">Tokyo</td>
              <td className="px-4 py-2 border-b">63</td>
              <td className="px-4 py-2 border-b">$170,750</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default StickyHeadTable;
