// Placeholder data for GridJS tables

export const Columns = [
  { name: "ID", key: "id" },
  { name: "Name", key: "name" },
  { name: "<PERSON><PERSON>", key: "email" },
  { name: "<PERSON><PERSON><PERSON>", key: "position" },
  { name: "<PERSON><PERSON>", key: "salary" },
];

export const Data = [
  { id: 1, name: "<PERSON>", email: "<EMAIL>", position: "Developer", salary: "$75,000" },
  { id: 2, name: "<PERSON>", email: "<EMAIL>", position: "Designer", salary: "$65,000" },
  { id: 3, name: "<PERSON>", email: "<EMAIL>", position: "Manager", salary: "$85,000" },
  { id: 4, name: "<PERSON>", email: "<EMAIL>", position: "Analyst", salary: "$60,000" },
  { id: 5, name: "<PERSON>", email: "<EMAIL>", position: "Developer", salary: "$70,000" },
];

export const Data1 = [
  { id: 1, product: "Laptop", category: "Electronics", price: "$999", stock: 25 },
  { id: 2, product: "Phone", category: "Electronics", price: "$599", stock: 50 },
  { id: 3, product: "Tablet", category: "Electronics", price: "$399", stock: 30 },
  { id: 4, product: "Headphones", category: "Audio", price: "$199", stock: 75 },
  { id: 5, product: "Mouse", category: "Accessories", price: "$29", stock: 100 },
];

export const Data2 = [
  { id: 1, order: "#001", customer: "Alice Johnson", date: "2024-01-15", amount: "$250", status: "Completed" },
  { id: 2, order: "#002", customer: "Bob Wilson", date: "2024-01-16", amount: "$180", status: "Pending" },
  { id: 3, order: "#003", customer: "Carol Davis", date: "2024-01-17", amount: "$320", status: "Shipped" },
  { id: 4, order: "#004", customer: "David Miller", date: "2024-01-18", amount: "$150", status: "Processing" },
  { id: 5, order: "#005", customer: "Eva Garcia", date: "2024-01-19", amount: "$275", status: "Completed" },
];

export const Data3 = [
  { id: 1, task: "Design Homepage", assignee: "John Doe", priority: "High", due: "2024-02-01", progress: "80%" },
  { id: 2, task: "Fix Login Bug", assignee: "Jane Smith", priority: "Critical", due: "2024-01-25", progress: "60%" },
  { id: 3, task: "Update Documentation", assignee: "Bob Johnson", priority: "Medium", due: "2024-02-05", progress: "30%" },
  { id: 4, task: "Performance Testing", assignee: "Alice Brown", priority: "High", due: "2024-02-03", progress: "45%" },
  { id: 5, task: "Code Review", assignee: "Charlie Wilson", priority: "Low", due: "2024-02-07", progress: "90%" },
];

const gridJsData = {
  Columns,
  Data,
  Data1,
  Data2,
  Data3,
};

export default gridJsData;
