import { AvatarType, AvatarIconType, AvatarNumberType, AvatarStackType, AvatarInitialType } from '../types/avatar-types';

// Avatar size data template (requires face imports)
export const AvatarSizeDataTemplate: Omit<AvatarType, 'src'>[] = [
  { id: 1, class: "xs" },
  { id: 2, class: "sm" },
  { id: 3, class: "md" },
  { id: 4, class: "lg" },
  { id: 5, class: "xl" },
  { id: 6, class: "xxl" },
];

// Avatar with icon data template (requires face imports)
export const AvatarIconDataTemplate: Omit<AvatarIconType, 'src'>[] = [
  { id: 1, class: "xs", icon: "camera", color: "success" },
  { id: 2, class: "sm", icon: "edit", color: "secondary" },
  { id: 3, class: "md", icon: "plus", color: "warning" },
  { id: 4, class: "lg", icon: "edit", color: "info" },
  { id: 5, class: "xl", icon: "camera", color: "success" },
  { id: 6, class: "xxl", icon: "plus", color: "danger" },
];

// Avatar online status data template (requires face imports)
export const AvatarOnlineDataTemplate: Omit<AvatarType, 'src'>[] = [
  { id: 1, class: "xs online" },
  { id: 2, class: "sm online" },
  { id: 3, class: "md online" },
  { id: 4, class: "lg online" },
  { id: 5, class: "xl online" },
  { id: 6, class: "xxl online" },
];

// Avatar offline status data template (requires face imports)
export const AvatarOfflineDataTemplate: Omit<AvatarType, 'src'>[] = [
  { id: 1, class: "xs offline" },
  { id: 2, class: "sm offline" },
  { id: 3, class: "md offline" },
  { id: 4, class: "lg offline" },
  { id: 5, class: "xl offline" },
  { id: 6, class: "xxl offline" },
];

// Avatar with number badge data template (requires face imports)
export const AvatarNumberDataTemplate: Omit<AvatarNumberType, 'src'>[] = [
  { id: 1, class: "xs", color: "primary", number: "2" },
  { id: 2, class: "sm", color: "secondary", number: "5" },
  { id: 3, class: "md", color: "warning", number: "1" },
  { id: 4, class: "lg", color: "info", number: "7" },
  { id: 5, class: "xl", color: "success", number: "3" },
  { id: 6, class: "xxl", color: "danger", number: "9" },
];

// Avatar stack data template (requires face imports)
export const AvatarStackDataTemplate: Omit<AvatarStackType, 'src'>[] = [
  { id: 1 },
  { id: 2 },
  { id: 3 },
  { id: 4 },
  { id: 5 },
  { id: 6 },
];

// Avatar initials data
export const AvatarInitialData: AvatarInitialType[] = [
  { id: 1, class: "xs", color: "primary", data1: "XS" },
  { id: 2, class: "sm", color: "secondary", data1: "SM" },
  { id: 3, class: "md", color: "warning", data1: "MD" },
  { id: 4, class: "lg", color: "danger", data1: "LG" },
  { id: 5, class: "xl", color: "success", data1: "XL" },
  { id: 6, class: "xxl", color: "info", data1: "XXL" },
];
