// shared/query/useBetResultQuery.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { getReportingBackendUrl } from '@/shared/utils/envValidation';
import { betDetailsCircuitBreaker } from '@/shared/utils/circuitBreaker';

// Types for bet result functionality
export interface BetResultRequest {
  betId: string;
}

export interface BetResultItem {
  marketName: string;
  rate: number;
  stake: number;
  selection?: string;
}

export interface BetResultData {
  betId: string;
  status: 'win' | 'loss' | 'pending' | 'void';
  betAmount: number;
  winningAmount?: number;
  losingAmount?: number;
  createdDate: string;
  settlementDate?: string;
  isSettled: boolean;
  betList: BetResultItem[];
  provider?: string;
  marketDetail?: {
    marketId: string;
    marketName: string;
    marketStatus: string;
  };
}

export interface BetResultResponse {
  success: number;
  message: string;
  data: BetResultData;
  errors?: string[];
}

export interface MarkSettledRequest {
  betId: string;
  settlementStatus: 'settled';
}

export interface MarkSettledResponse {
  success: number;
  message: string;
  data: {
    betId: string;
    isSettled: boolean;
  };
}

/**
 * Fetch bet result details by bet ID
 */
const fetchBetResult = async (betId: string): Promise<BetResultResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  if (!betId || betId.trim().length === 0) {
    throw new Error('Bet ID is required');
  }

  const baseUrl = getReportingBackendUrl();

  const requestData: BetResultRequest = {
    betId: betId.trim(),
  };

  const response = await betDetailsCircuitBreaker.execute(async () => {
    const res = await fetch(`${baseUrl}/api/v1/bet/result`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestData),
    });

    // Handle 401 errors globally
    await checkAndHandle401(res);

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
    }

    return res.json();
  });

  return response;
};

/**
 * Mark bet as settled
 */
const markBetAsSettled = async (betId: string): Promise<MarkSettledResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  if (!betId || betId.trim().length === 0) {
    throw new Error('Bet ID is required');
  }

  const baseUrl = getReportingBackendUrl();

  const requestData: MarkSettledRequest = {
    betId: betId.trim(),
    settlementStatus: 'settled',
  };

  const response = await betDetailsCircuitBreaker.execute(async () => {
    const res = await fetch(`${baseUrl}/api/v1/bet/settle`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestData),
    });

    // Handle 401 errors globally
    await checkAndHandle401(res);

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${res.status}`);
    }

    return res.json();
  });

  return response;
};

/**
 * React Query hook for fetching bet result details
 */
export const useBetResultQuery = (betId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['betResult', betId],
    queryFn: () => fetchBetResult(betId),
    enabled: enabled && !!betId && betId.trim().length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * React Query hook for lazy fetching of bet result details
 */
export const useBetResultLazyQuery = () => {
  return useQuery({
    queryKey: ['betResult', 'lazy'],
    queryFn: () => Promise.resolve(null),
    enabled: false,
  });
};

/**
 * Mutation hook for marking bet as settled
 */
export const useMarkBetAsSettledMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markBetAsSettled,
    onSuccess: (data, betId) => {
      // Invalidate and refetch bet result query
      queryClient.invalidateQueries({ queryKey: ['betResult', betId] });

      // Update the cache optimistically
      queryClient.setQueryData(['betResult', betId], (oldData: BetResultResponse | undefined) => {
        if (oldData) {
          return {
            ...oldData,
            data: {
              ...oldData.data,
              isSettled: true,
              settlementDate: new Date().toISOString(),
            },
          };
        }
        return oldData;
      });
    },
    onError: (error) => {
      handleQueryError(error);
    },
  });
};

/**
 * Utility function to manually fetch bet result (for use outside React components)
 */
export const fetchBetResultManual = async (betId: string): Promise<BetResultData> => {
  try {
    const response = await fetchBetResult(betId);

    if (response.success !== 1) {
      throw new Error(response.message || 'Failed to fetch bet result');
    }

    return response.data;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Unknown error occurred while fetching bet result');
  }
};

/**
 * Utility function to format bet result data for display
 */
export const formatBetResultForDisplay = (betResult: BetResultData) => {
  const date = new Date(betResult.createdDate);
  const formattedDate = date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).replace(/\//g, ' / ');

  const formattedTime = date.toLocaleTimeString('en-GB', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  });

  return {
    ...betResult,
    formattedDate,
    formattedTime,
    displayAmount: betResult.status === 'win' ? betResult.winningAmount : betResult.losingAmount,
  };
};
