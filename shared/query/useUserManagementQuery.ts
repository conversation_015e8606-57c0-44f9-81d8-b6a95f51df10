// shared/query/useUserManagementQuery.ts
import { useAuthStore } from '@/shared/stores/authStore';
import { UserListFilters, UserListResponse, buildUserQuery } from '@/shared/types/user-management-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { useQuery } from '@tanstack/react-query';

export const fetchUserList = async (filters: UserListFilters): Promise<UserListResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the cashier API URL from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL || process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  const response = await fetch(`${baseUrl}/api/v2/cashier/players`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(filters),
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch users: ${response.status}`);
  }

  return response.json();
};

export const useUserListQuery = (
  filters: Partial<UserListFilters> = {},
  initialData?: UserListResponse | null
) => {
  const { token } = useAuthStore();

  // Build clean query with only meaningful filters
  const finalFilters: UserListFilters = buildUserQuery(filters);

  return useQuery<UserListResponse>({
    queryKey: ['userList', finalFilters],
    queryFn: async () => {
      try {
        return await fetchUserList(finalFilters);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token, // Only run query if user is authenticated
    initialData: initialData || undefined, // Use server-side data if available
    staleTime: 0, // Always consider data stale to ensure refetch on filter changes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes after component unmount
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false, // Don't refetch on window focus for better UX
    refetchOnMount: true, // Allow refetch on mount to ensure fresh data
  });
};

// Hook for refetching user list with new filters
export const useUserListRefetch = () => {
  const { token: _token } = useAuthStore();

  return (filters: Partial<UserListFilters> = {}) => {
    const finalFilters: UserListFilters = buildUserQuery(filters);
    return fetchUserList(finalFilters);
  };
};
