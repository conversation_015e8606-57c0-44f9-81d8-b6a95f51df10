// shared/query/useSportsCategoriesQuery.ts
import { useQuery } from '@tanstack/react-query';
import { SportsCategoriesResponse } from '@/shared/types/sportsbook-types';
import { checkAndHandle401 } from '@/shared/utils/globalApiErrorHandler';

/**
 * Fetch sports categories from TurboStars marketing event list API
 * Uses POST method as specified in the requirements
 */
export const fetchSportsCategories = async (): Promise<SportsCategoriesResponse> => {
  const response = await fetch('https://api.ingrandstation.com/turbostars/marketing-event-list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    // Empty body for POST request as per API specification
    body: JSON.stringify({}),
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to fetch sports categories: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

/**
 * React Query hook for fetching sports categories
 * Provides caching, error handling, and loading states
 */
export const useSportsCategoriesQuery = () => {
  return useQuery({
    queryKey: ['sportsCategories'],
    queryFn: fetchSportsCategories,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });
};

export default useSportsCategoriesQuery;
