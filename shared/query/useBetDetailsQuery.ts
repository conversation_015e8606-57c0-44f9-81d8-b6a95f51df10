// shared/query/useBetDetailsQuery.ts
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { BetDetailsRequest, BetDetailsResponse } from '@/shared/types/user-management-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { getReportingBackendUrl } from '@/shared/utils/envValidation';
import { betDetailsCircuitBreaker } from '@/shared/utils/circuitBreaker';

/**
 * Fetch bet details from the reporting backend with circuit breaker protection
 * Uses POST method with JSON body
 */
export const fetchBetDetails = async (request: BetDetailsRequest): Promise<BetDetailsResponse> => {
  // Use circuit breaker to prevent continuous API calls when service is failing
  return betDetailsCircuitBreaker.execute(async () => {
    const token = useAuthStore.getState().token;

    if (!token) {
      throw new Error('Authentication token is required');
    }

    // Use the reporting backend URL from environment variables
    const baseUrl = getReportingBackendUrl();

    const response = await fetch(`${baseUrl}/api/v2/cashier/player/betDetails`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(request),
    });

    // Check for 401 errors and handle them globally
    await checkAndHandle401(response);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to fetch bet details: ${response.status}`);
    }

    const data = await response.json();

    // Check for API-level errors even when HTTP status is 200
    // Handle cases where API returns success: 0 or error codes like 417
    if (data && (data.success === 0 || data.code === 417 || data.code >= 400)) {
      const errorMessage = data.message || `API error: ${data.code || 'Unknown error'}`;
      // Log error details for debugging (only in development)
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error("❌ API returned error response:", {
          success: data.success,
          code: data.code,
          message: data.message,
          transactionId: request.transactionId,
          provider: request.providerName
        });
      }
      throw new Error(errorMessage);
    }

    return data;
  });
};

/**
 * React Query hook for fetching bet details
 * @param transactionId - The transaction ID from WebSocket notification
 * @param providerName - The provider name (e.g., 'turbostars')
 * @param enabled - Whether the query should be enabled (default: true if both params provided)
 */
export const useBetDetailsQuery = (
  transactionId?: string,
  providerName?: string,
  enabled?: boolean
) => {
  const { token } = useAuthStore();

  // Determine if query should be enabled
  const shouldEnable = enabled !== undefined
    ? enabled
    : !!(token && transactionId && providerName);

  return useQuery<BetDetailsResponse>({
    queryKey: ['betDetails', transactionId, providerName],
    queryFn: async () => {
      if (!transactionId || !providerName) {
        throw new Error('Transaction ID and provider name are required');
      }

      try {
        return await fetchBetDetails({
          transactionId,
          providerName,
        });
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: shouldEnable,
    staleTime: 5 * 60 * 1000, // Data considered fresh for 5 minutes (bet details don't change frequently)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for manually triggering bet details fetch
 * Useful for WebSocket-triggered API calls
 */
export const useBetDetailsLazyQuery = () => {
  const { token } = useAuthStore();

  const fetchBetDetailsManually = async (transactionId: string, providerName: string) => {
    if (!token) {
      throw new Error('Authentication token is required');
    }

    if (!transactionId || !providerName) {
      throw new Error('Transaction ID and provider name are required');
    }

    try {
      return await fetchBetDetails({
        transactionId,
        providerName,
      });
    } catch (error) {
      handleQueryError(error);
      throw error;
    }
  };

  return {
    fetchBetDetails: fetchBetDetailsManually,
    isAuthenticated: !!token,
  };
};
