// shared/hooks/business/useBetshopSettings.ts
import { useAuthStore } from '@/shared/stores/authStore';
import { useSessionTimeoutStore } from '@/shared/stores/sessionStore';

interface BetshopSettingsRequest {
    tenantID: number;
}

interface BetshopSettingsItem {
    id: string;
    key: string;
    value: string;
    description?: string;
    tenantId: string;
    createdAt: string;
    updatedAt: string;
}

interface BetshopSettingsResponse {
    success: number;
    message: string;
    data: {
        settingsDetails: BetshopSettingsItem[];
    };
    errors?: string[];
}

interface BetshopSettingsError {
    success: number;
    message: string;
    errors?: string[];
}

/**
 * Fetch betshop settings from the reporting API
 */
export const fetchBetshopSettings = async (tenantId: number, AuthToken?: string): Promise<BetshopSettingsResponse> => {
    const token = useAuthStore.getState().token || AuthToken;

    if (!token && !AuthToken) {
        throw new Error('Authentication token is required');
    }

    if (!tenantId) {
        throw new Error('Tenant ID is required');
    }

    // Use the reporting API URL
    const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || 'https://reporting.ingrandstation.com';

    const requestBody: BetshopSettingsRequest = {
        tenantID: tenantId
    };

    const response = await fetch(`${baseUrl}/api/v2/admin/betshop-settings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${AuthToken || token}`,
            // Add a custom header to identify this as a betshop settings request
            'X-Betshop-Settings-Request': 'true',
        },
        body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
            const errorData: BetshopSettingsError = await response.json();
            errorMessage = errorData.message || errorMessage;
        } catch {
            // If we can't parse the error response, use the default message
        }

        // Create a more specific error for 401s to help with retry logic
        if (response.status === 401) {
            throw new Error(`401: ${errorMessage}`);
        }

        throw new Error(errorMessage);
    }

    const data: BetshopSettingsResponse = await response.json();
    if (data?.data?.settingsDetails?.length <= 0) {
        throw new Error(data.message || 'Failed to fetch betshop settings');
    }

    return data;
};

/**
 * Utility function to fetch betshop settings with retry logic and proper timing
 */
export const BetshopSettingsWithRetry = async ({
    tenantId,
    AuthToken,
    maxRetries = 3,
    initialDelay = 1000
}: {
    tenantId?: number,
    AuthToken?: string,
    maxRetries?: number,
    initialDelay?: number
}): Promise<BetshopSettingsResponse | undefined> => {
    const { user } = useAuthStore.getState();
    const effectiveTenantId = tenantId || user?.tenantId;
    const { setTenantId, setBetshopSettings } = useSessionTimeoutStore.getState();

    if (!effectiveTenantId) {
        // No tenantId, return undefined
        return undefined;
    }

    setTenantId(effectiveTenantId);

    // Retry logic with exponential backoff
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            // Add delay before each attempt (except the first one if initialDelay is 0)
            if (attempt > 0 || initialDelay > 0) {
                const delay = attempt === 0 ? initialDelay : initialDelay * Math.pow(2, attempt - 1);
                await new Promise(resolve => setTimeout(resolve, delay));
            }

            const result = await fetchBetshopSettings(effectiveTenantId, AuthToken);

            if (result?.data?.settingsDetails) {
                const cashierWebTimeout = result.data.settingsDetails.find((setting) => setting.key === 'cashier_web_timeout');
                // Store timeout in minutes, not ms
                let timeoutValue: number = cashierWebTimeout?.value ? Number(cashierWebTimeout.value) : 0;
                setBetshopSettings(timeoutValue);
            }

            return result;
        } catch (error: any) {
            // If it's a 401 error and we have more retries left, continue
            if (error.message?.includes('401') && attempt < maxRetries - 1) {
                continue;
            }

            // If it's the last attempt or not a 401 error, throw
            if (attempt === maxRetries - 1) {
                // Don't throw the error to prevent triggering global 401 handler
                return undefined;
            }

            throw error;
        }
    }

    return undefined;
};

/**
 * Utility function to fetch betshop settings (not a React hook)
 * @deprecated Use BetshopSettingsWithRetry instead for better error handling
 */
export const BetshopSettings = async ({ tenantId, AuthToken }: { tenantId?: number, AuthToken?: string }): Promise<BetshopSettingsResponse | undefined> => {
    const { user } = useAuthStore.getState();
    const effectiveTenantId = tenantId || user?.tenantId;
    const { setTenantId, setBetshopSettings } = useSessionTimeoutStore.getState();

    if (!effectiveTenantId) {
        // No tenantId, return undefined
        return undefined;
    }
    setTenantId(effectiveTenantId);
    const result = await fetchBetshopSettings(effectiveTenantId, AuthToken);
    if (result?.data?.settingsDetails) {
        const cashierWebTimeout = result.data.settingsDetails.find((setting) => setting.key === 'cashier_web_timeout');
        // Store timeout in minutes, not ms
        let timeoutValue: number = cashierWebTimeout?.value ? Number(cashierWebTimeout.value) : 0;
        setBetshopSettings(timeoutValue);
    }
    return result;
};

