// shared/query/index.ts
// Central export file for all TanStack Query related exports

export { default as QueryProvider } from './QueryProvider';

// Queries
export { getAdminWalletId, getPrimaryAdminWallet, useAdminWalletQuery } from './useAdminWalletQuery';
export { fetchBetDetails, useBetDetailsLazyQuery, useBetDetailsQuery } from './useBetDetailsQuery';
export { fetchBetHistory, useBetHistoryQuery, useBetHistoryRefetch } from './useBetHistoryQuery';
export { fetchBetWinReport, useBetWinReportQuery, useBetWinReportRefetch } from './useBetWinReportQuery';
export { fetchCashierReport, useCashierReportQuery, useCashierReportRefetch } from './useCashierReportQuery';
export { fetchFinancialReport, useFinancialReportQuery, useFinancialReportRefetch } from './useFinancialReportQuery';
export { fetchLoginHistory, useLoginHistoryQuery } from './useLoginHistoryQuery';
export { submitExportRequest, fetchExportCenter, useExportRequestMutation, useExportCenterQuery, useExportCenterRefetch } from './useExportQuery';
export { fetchUserDetails, useUserDetailsQuery } from './useUserDetailsQuery';
export { fetchUserList, useUserListQuery, useUserListRefetch } from './useUserManagementQuery';

// Mutations
export { useCreateUserMutation } from './mutations/useCreateUserMutation';
export type { CreateUserData, CreateUserResponse, EditUserData } from './mutations/useCreateUserMutation';
export { useEditUserMutation } from './mutations/useEditUserMutation';
export type { EditUserResponse } from './mutations/useEditUserMutation';
export { useLoginMutation } from './mutations/useLoginMutation';
export { useLogoutMutation } from './mutations/useLogoutMutation';
export { useTurboStarsGameLaunchMutation } from './mutations/useTurboStarsGameLaunchMutation';
export { useValidatePinMutation } from './mutations/useValidatePinMutation';
export { useWalletTransactionMutation, validateWalletTransactionForm } from './mutations/useWalletTransactionMutation';

