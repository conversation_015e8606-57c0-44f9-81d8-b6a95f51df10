// shared/query/mutations/useEditUserMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { EditUserData } from './useCreateUserMutation';
// import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';

export interface EditUserResponse {
  data: any;
  errors: string[];
  success: number;
  message: string;
}

const editUser = async (userData: EditUserData): Promise<EditUserResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the staging backend URL from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  const response = await fetch(`${baseUrl}/api/v2/cashier/player/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(userData),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to update user: ${response.status}`);
  }

  return response.json();
};

export const useEditUserMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<EditUserResponse, Error, EditUserData>({
    mutationFn: editUser,
    onSuccess: (data, variables) => {
      if (data.success === 1) {
        // Invalidate user list queries to refresh the data
        queryClient.invalidateQueries({ queryKey: ['userList'] });
        // Invalidate specific user details query to refresh the data
        queryClient.invalidateQueries({ queryKey: ['userDetails', variables.id.toString()] });
      } else {
        throw new Error(data.message || 'User update failed with unexpected response');
      }
    },
    onError: (_error) => {
      // console.error('Edit user error:', error.message);
    },
  });
};
