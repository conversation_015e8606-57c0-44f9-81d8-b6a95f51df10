// shared/query/mutations/useTurboStarsGameLaunchMutation.ts
import { useMutation } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { 
  TurboStarsGameLaunchRequest, 
  TurboStarsGameLaunchResponse, 
  TurboStarsError 
} from '@/shared/types/user-management-types';

const launchTurboStarsGame = async (
  requestData: TurboStarsGameLaunchRequest
): Promise<TurboStarsGameLaunchResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the reporting backend URL from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Reporting backend URL is not configured');
  }

  const response = await fetch(`${baseUrl}/api/v2/cashier/player/gameLaunch`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(requestData),
  });

  if (!response.ok) {
    const errorData: TurboStarsError = await response.json();
    throw new Error(errorData.message || `Failed to launch TurboStars game: ${response.status}`);
  }

  const responseData: TurboStarsGameLaunchResponse = await response.json();
  
  if (responseData.success !== 1) {
    throw new Error(responseData.message || 'Game launch failed with unexpected response');
  }

  return responseData;
};

export const useTurboStarsGameLaunchMutation = () => {
  return useMutation<TurboStarsGameLaunchResponse, Error, TurboStarsGameLaunchRequest>({
    mutationFn: launchTurboStarsGame,
    onSuccess: (_data) => {
      // console.log('TurboStars game launch successful:', data);
    },
    onError: (_error) => {
      // console.error('TurboStars game launch error:', error.message);
    },
  });
};
