// shared/query/mutations/useWalletTransactionMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import {
  WalletTransactionResponse,
  WalletTransactionFormData
} from '@/shared/types/user-management-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { useAdminWalletQuery, getAdminWalletId } from '../useAdminWalletQuery';

/**
 * API service function for wallet transactions (deposit/withdraw)
 * Uses multipart/form-data as required by the API
 */
const submitWalletTransaction = async (
  formData: WalletTransactionFormData,
  adminWalletId: string
): Promise<WalletTransactionResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  if (!adminWalletId) {
    throw new Error('Admin wallet ID is required but not available. Please ensure you are logged in and your profile is loaded.');
  }

  // Get the admin backend URL
  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Admin backend URL is not configured');
  }

  // Create FormData for multipart/form-data submission
  const formDataPayload = new FormData();

  // Determine wallet IDs based on transaction type
  const isDeposit = formData.transactionType === 'deposit';
  const targetWalletId = isDeposit ? formData.walletId : adminWalletId; // For deposits: user wallet, for withdrawals: admin wallet
  const sourceWalletId = isDeposit ? adminWalletId : formData.walletId; // For deposits: admin wallet, for withdrawals: user wallet

  // Append required form data fields
  formDataPayload.append('target_type', 'User');
  formDataPayload.append('target_email', formData.userId);
  formDataPayload.append('transaction_amount', formData.amount);
  formDataPayload.append('transaction_type', formData.transactionType);
  formDataPayload.append('target_wallete_id', String(targetWalletId));
  formDataPayload.append('source_wallete_id', String(sourceWalletId));

  // Append optional fields
  formDataPayload.append('internal_comment', formData.internalComment || '');
  formDataPayload.append('utr_number', formData.utrNumber || '');
  formDataPayload.append('transaction_comments', formData.transactionComments || '');

  const response = await fetch(`${baseUrl}/api/admin/transactions/transfer-fund/new`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      // Note: Don't set Content-Type header when using FormData - browser will set it automatically with boundary
    },
    body: formDataPayload,
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    let errorMessage = `Failed to process ${formData.transactionType}: ${response.status}`;

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch {
      // If we can't parse the error response, use the default message
    }

    throw new Error(errorMessage);
  }

  const result = await response.json();

  return result;
};

/**
 * React Query mutation hook for wallet transactions
 * Automatically fetches the current admin user's wallet ID for admin/cashier transactions
 */
export const useWalletTransactionMutation = () => {
  const queryClient = useQueryClient();
  const { user } = useAuthStore();

  // Use admin wallet query to get admin wallet ID
  const { data: adminWalletData, error: adminWalletError } = useAdminWalletQuery();

  const currentUserWalletId = getAdminWalletId(adminWalletData as any);

  return useMutation<WalletTransactionResponse, Error, WalletTransactionFormData>({
    mutationFn: async (formData) => {
      if (!currentUserWalletId) {
        // Provide detailed error message with debugging information
        const errorDetails = {
          adminId: user?.id,
          adminEmail: user?.email,
          adminWalletError: adminWalletError?.message,
          hasAdminWalletData: !!adminWalletData,
          walletRecordCount: (adminWalletData as any)?.record?.length || 0,
        };

        throw new Error(
          `Admin wallet ID is not available. Unable to process transaction.\n\n` +
          `Debug Information:\n` +
          `- Admin ID: ${errorDetails.adminId}\n` +
          `- Admin Email: ${errorDetails.adminEmail}\n` +
          `- Wallet Query Error: ${errorDetails.adminWalletError || 'None'}\n` +
          `- Has Wallet Data: ${errorDetails.hasAdminWalletData}\n` +
          `- Wallet Records Found: ${errorDetails.walletRecordCount}\n\n` +
          `This usually means:\n` +
          `1. The admin wallet API endpoint (/api/admin/user/wallet) is not available\n` +
          `2. The admin user account doesn't have wallet information\n` +
          `3. The wallet API doesn't return valid wallet records\n\n` +
          `Please contact system administrator to configure admin wallet access.`
        );
      }
      return await submitWalletTransaction(formData, currentUserWalletId);
    },
    onSuccess: (data, variables) => {
      if (data.success === 1) {
        // Invalidate relevant queries to refresh data
        queryClient.invalidateQueries({ queryKey: ['userDetails', variables.userId] });
        queryClient.invalidateQueries({ queryKey: ['adminWallet'] });
        queryClient.invalidateQueries({ queryKey: ['userList'] });
        queryClient.invalidateQueries({ queryKey: ['betHistory', variables.userId] });
        queryClient.invalidateQueries({ queryKey: ['financialReport', variables.userId] });
      } else {
        throw new Error(data.message || 'Wallet transaction failed with unexpected response');
      }
    },
    onError: (error, _variables) => {
      handleQueryError(error); // Handle 401 errors globally
    },
  });
};

/**
 * Helper function to validate wallet transaction form data
 * Simplified to only validate essential fields (user, transaction type, amount)
 */
export const validateWalletTransactionForm = (formData: WalletTransactionFormData): string[] => {
  const errors: string[] = [];

  if (!formData.userId || formData.userId.trim() === '') {
    errors.push('User selection is required');
  }

  if (!formData.walletId || formData.walletId.trim() === '') {
    errors.push('User wallet ID is required');
  }

  if (!formData.transactionType) {
    errors.push('Transaction type is required');
  }

  if (!formData.amount || formData.amount.trim() === '') {
    errors.push('Transaction amount is required');
  } else {
    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      errors.push('Transaction amount must be a positive number');
    }
    if (amount > 1000000) {
      errors.push('Transaction amount cannot exceed 1,000,000');
    }
  }

  // Note: Optional fields (internalComment, utrNumber, transactionComments) are no longer validated
  // They are always sent as empty strings to the API

  return errors;
};
