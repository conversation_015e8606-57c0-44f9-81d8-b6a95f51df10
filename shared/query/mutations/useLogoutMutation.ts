// shared/query/mutations/useLogoutMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';

const _logoutUserApi = async (_token: string): Promise<any> => {
    // This part remains commented out for future use
    // console.warn("Logout API call is currently disabled but available for future use.");
    /*
    const response = await fetch(`${process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL}/api/logout`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
    });
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Logout API failed');
    }
    return response.json();
    */
    return Promise.resolve({}); // Simulates immediate success
};

export const useLogoutMutation = () => {
    const queryClient = useQueryClient();
    const clearAuth = useAuthStore((state) => state.clearAuth);
    // const token = useAuthStore((state) => state.token);

    return useMutation({
        mutationFn: async () => {
            // Uncomment this line to re-enable API call in the future
            // await logoutUserApi(token || '');
            return Promise.resolve({});
        },
        onMutate: () => {
            // This is the ideal place for IMMEDIATE client-side state clearing
            clearAuth();       // <--- Recommended location for immediate state clear
            queryClient.clear(); // <--- Recommended location for immediate cache clear
        },
        onSuccess: () => {
            // This runs after mutationFn successfully resolves.
            // State is already cleared. Can add success notifications here.
            // console.log("onSuccess: Logout mutation completed.");
        },
        onError: (_error) => {
            // This runs if mutationFn (e.g., API call) fails.
            // State is already cleared by onMutate, but re-clear as a fallback.
            // console.error('onError: Logout failed at API level, ensuring local state is cleared.', error.message);
            clearAuth(); // Fallback to ensure client state is cleared
            queryClient.clear(); // Fallback to ensure cache is cleared
        },
        // Consider retry: 0 for logout mutations if you want immediate failure/redirect
        // retry: 0,
    });
};
