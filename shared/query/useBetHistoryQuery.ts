// shared/query/useBetHistoryQuery.ts
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { BetHistoryFilters, BetHistoryResponse, BetHistoryApiResponse, DEFAULT_BET_HISTORY_FILTERS } from '@/shared/types/user-management-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';

// Helper function to build bet history query parameters - Updated to match exact API requirements
const buildBetHistoryQuery = (filters: BetHistoryFilters): string => {
  const queryParts: string[] = [];

  // Build query in EXACT order from the working example:
  // size=5&page=1&gameProvider=&gameType=&search=&roundId=&id=&amount=&transactionId=&debitTransactionId=&utrNumber=&tenantId=&currencyId=&order=desc&sortBy=created_at&actionType=&actionCategory=sports&timePeriod=%7B%22startDate%22:%222025-06-25%2000:00:00%22,%22endDate%22:%222025-06-25%2023:59:59%22%7D&timeType=today&timeZone=UTC%20+00:00&timeZoneName=UTC%20+00:00&lifetimeRecords=false&playerId=19681&dateTime=%7B%22startDate%22:%222025-06-25%2000:00:00%22,%22endDate%22:%222025-06-25%2023:59:59%22%7D

  // 1. size and page (first)
  queryParts.push(`size=${filters.size}`);
  queryParts.push(`page=${filters.page}`);

  // 2. All filter parameters in exact order (empty strings for unused filters)
  queryParts.push(`gameProvider=${filters.gameProvider || ''}`);
  queryParts.push(`gameType=${filters.gameType || ''}`);
  queryParts.push(`search=${filters.search || ''}`);
  queryParts.push(`roundId=${filters.roundId || ''}`);
  queryParts.push(`id=${filters.id || ''}`);
  queryParts.push(`amount=${filters.amount || ''}`);
  queryParts.push(`transactionId=${filters.transactionId || ''}`);
  queryParts.push(`debitTransactionId=${filters.debitTransactionId || ''}`);
  queryParts.push(`utrNumber=${filters.utrNumber || ''}`);
  queryParts.push(`tenantId=${filters.tenantId || ''}`);
  queryParts.push(`currencyId=${filters.currencyId || ''}`);

  // 3. Sorting parameters
  queryParts.push(`order=${filters.order || 'desc'}`);
  queryParts.push(`sortBy=${filters.sortBy || 'created_at'}`);

  // 4. Action parameters
  queryParts.push(`actionType=${filters.actionType || ''}`);
  queryParts.push(`actionCategory=${filters.actionCategory || 'sports'}`);

  // 5. Time period handling - default to today if not provided
  let timePeriod = filters.timePeriod;
  let dateTime = filters.dateTime;

  if (!timePeriod || !dateTime) {
    const today = new Date();
    const startDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 00:00:00`;
    const endDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 23:59:59`;

    // Use the exact encoding format from the working API example
    // Working example: %7B%22startDate%22:%222025-06-25%2000:00:00%22,%22endDate%22:%222025-06-25%2023:59:59%22%7D
    // Key: Only encode spaces as %20, keep colons as : (not %3A)
    timePeriod = `%7B%22startDate%22:%22${startDate.replace(/ /g, '%20')}%22,%22endDate%22:%22${endDate.replace(/ /g, '%20')}%22%7D`;
    dateTime = `%7B%22startDate%22:%22${startDate.replace(/ /g, '%20')}%22,%22endDate%22:%22${endDate.replace(/ /g, '%20')}%22%7D`;
  }

  queryParts.push(`timePeriod=${timePeriod}`);

  // 6. Time-related parameters
  queryParts.push(`timeType=${filters.timeType || 'today'}`);

  // CRITICAL FIX: Use exact timezone encoding from working example
  // Working example: timeZone=UTC%20+00:00 (space encoded as %20, + and : kept as-is)
  // NOT: UTC%20%2B00%3A00 (which double-encodes + and :)
  const timeZone = filters.timeZone || 'UTC +00:00';
  const timeZoneName = filters.timeZoneName || 'UTC +00:00';
  queryParts.push(`timeZone=${timeZone.replace(/ /g, '%20')}`); // Only encode spaces, keep + and : as-is
  queryParts.push(`timeZoneName=${timeZoneName.replace(/ /g, '%20')}`); // Only encode spaces, keep + and : as-is
  queryParts.push(`lifetimeRecords=${filters.lifetimeRecords || false}`);

  // 7. playerId (AFTER lifetimeRecords, not at the beginning!)
  if (filters.playerId) {
    queryParts.push(`playerId=${filters.playerId}`);
  }

  // 8. dateTime (last parameter)
  queryParts.push(`dateTime=${dateTime}`);

  return queryParts.join('&');
};

export const fetchBetHistory = async (filters: BetHistoryFilters): Promise<BetHistoryResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the staging backend URL from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL || process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  // Build query string
  const queryString = buildBetHistoryQuery(filters);

  const response = await fetch(`${baseUrl}/api/v2/admin/transactions?${queryString}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch bet history: ${response.status}`);
  }

  // Parse the API response and transform it to our expected format
  const apiResponse: BetHistoryApiResponse = await response.json();

  // Transform the nested API response to our flattened format
  const transformedResponse: BetHistoryResponse = {
    data: apiResponse.data.data.result.rows,
    success: apiResponse.success,
    message: apiResponse.data.message,
    errors: apiResponse.errors,
    count: apiResponse.data.data.result.count,
    totalPages: apiResponse.data.data.result.total_pages,
    currentPage: apiResponse.data.data.result.current_page
  };

  return transformedResponse;
};

export const useBetHistoryQuery = (userId?: string, filters: Partial<BetHistoryFilters> = {}) => {
  const { token } = useAuthStore();

  // Build final filters with defaults
  const finalFilters: BetHistoryFilters = {
    ...DEFAULT_BET_HISTORY_FILTERS,
    ...filters,
    // Only set playerId if userId is provided (for user-specific queries)
    ...(userId && { playerId: userId }),
  };

  return useQuery<BetHistoryResponse>({
    queryKey: ['betHistory', userId || 'global', finalFilters],
    queryFn: async () => {
      try {
        return await fetchBetHistory(finalFilters);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token, // Only run query if user is authenticated (userId is now optional)
    staleTime: 2 * 60 * 1000, // Data considered fresh for 2 minutes (betting data changes frequently)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for recent betting activity across all users
export const useRecentBettingActivity = (limit: number = 15) => {
  const { token } = useAuthStore();

  // Build filters for recent activity
  const recentActivityFilters: BetHistoryFilters = {
    ...DEFAULT_BET_HISTORY_FILTERS,
    size: limit,
    page: 1,
    order: 'desc',
    sortBy: 'created_at',
    // No playerId - fetch across all users
  };

  return useQuery<BetHistoryResponse>({
    queryKey: ['recentBettingActivity', limit],
    queryFn: async () => {
      try {
        return await fetchBetHistory(recentActivityFilters);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token,
    staleTime: 1 * 60 * 1000, // Data considered fresh for 1 minute (recent activity changes frequently)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for refetching bet history with new filters
export const useBetHistoryRefetch = () => {
  return (userId?: string, filters: Partial<BetHistoryFilters> = {}) => {
    const finalFilters: BetHistoryFilters = {
      ...DEFAULT_BET_HISTORY_FILTERS,
      ...filters,
      ...(userId && { playerId: userId }),
    };

    return fetchBetHistory(finalFilters);
  };
};

