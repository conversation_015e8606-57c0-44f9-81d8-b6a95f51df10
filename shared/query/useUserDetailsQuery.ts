// shared/query/useUserDetailsQuery.ts
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { getStagingBackendUrl } from '@/shared/utils/envValidation';
import { UserDetailsResponse } from '@/shared/types/user-management-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';

export const fetchUserDetails = async (userId: string): Promise<UserDetailsResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Get validated backend URL (prefer staging, fallback to admin)
  const baseUrl = getStagingBackendUrl();

  const response = await fetch(`${baseUrl}/api/v2/cashier/player/${userId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch user details: ${response.status}`);
  }

  return response.json();
};

export const useUserDetailsQuery = (userId: string) => {
  const { token } = useAuthStore();

  return useQuery<UserDetailsResponse>({
    queryKey: ['userDetails', userId],
    queryFn: async () => {
      try {
        return await fetchUserDetails(userId);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token && !!userId, // Only run query if user is authenticated and userId is provided
    staleTime: 10 * 60 * 1000, // Data considered fresh for 10 minutes (user details change less frequently)
    gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes after component unmount
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: false, // Don't refetch on mount if data is fresh
  });
};
