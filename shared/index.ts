// shared/index.ts
// Central export file for all shared utilities

// Stores
export * from './stores';

// Hooks
export * from './hooks';

// Query utilities
export * from './query';

// Types
export * from './types';

// Data
export { LanguageOptions } from './data/languageOptions';
export { ProfileMenuItems } from './data/profileMenuItems';

// Utils
export { applyToHtml, htmlAttributeMap } from './utils';
export * from './utils/turboStarsUtils';

// TurboStars specific exports
export {
    openTurboStarsSportsbookInline,
    closeTurboStarsSportsbookInline,
    isTurboStarsSportsbookOpen,
    validateTurboStarsResponse,
    getTurboStarsErrorMessage,
    isTurboStarsSupported
} from './utils/turboStarsUtils';

// WebSocket services
export { TurboStarsWebSocketService, turboStarsWebSocketService } from './services/webSocketService';

// WebSocket hooks
export { useTurboStarsWebSocket, useTurboStarsBetNotifications } from './hooks';

// WebSocket providers
export {
    TurboStarsWebSocketProvider,
    useTurboStarsWebSocketContext,
    WebSocketStatusIndicator
} from './providers/TurboStarsWebSocketProvider';

// Notification components
export { BetNotification, BetNotificationToast } from './components/notifications/BetNotification';
export {
    BetNotificationManager,
    useBetNotificationManager,
    BetNotificationBadge
} from './components/notifications/BetNotificationManager';

// UI Elements
export {
    SpkBreadcrumb,
    SpkButton,
    SpkDropdown,
    SpkOverlay
} from './@spk-reusable-components/uielements';

// Global UI Components (includes the main SpkBadge component)
export * from './UI/components';

// Component Data
export * from './data/accordion-data';
// Explicitly re-export table data to resolve naming conflicts with types
export {
    BasicTableData as BasicTableDataArray,
    UserTableDataTemplate,
    OrderTableDataTemplate,
    TransactionTableData as TransactionTableDataArray,
    GroupTableData as GroupTableDataArray,
    BorderedTableData
} from './data/table-data';
export * from './data/avatar-data';
export * from './data/spinner-data';
