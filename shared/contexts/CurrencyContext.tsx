// shared/contexts/CurrencyContext.tsx
// Global Currency Context Provider
// Manages currency state across the entire application

"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  CurrencyConfig,
  getCurrentCurrencyConfig,
  getCurrencyConfig,
  getIconVariantForContext,
  CONTEXT_ICON_VARIANTS
} from '@/shared/config/currencyConfig';
import { LKRCurrencyVariant } from '@/shared/UI/components/icons/LKRCurrencyIcon';

/**
 * Currency Context Interface
 * Defines what the context provides to consuming components
 */
interface CurrencyContextType {
  /** Current currency configuration */
  currency: CurrencyConfig;

  /** Change the current currency (for future multi-currency support) */
  setCurrency: (currencyCode: string) => void;

  /** Get icon variant for specific context */
  getIconVariant: (context?: keyof typeof CONTEXT_ICON_VARIANTS) => LKRCurrencyVariant;

  /** Check if current currency has custom icons */
  hasCustomIcon: boolean;

  /** Format amount with current currency */
  formatAmount: (amount: number | string | null | undefined, options?: FormatOptions) => string;

  /** Format amount for display (number only, no symbol/icon) */
  formatAmountOnly: (amount: number | string | null | undefined) => string;

  /** Get currency symbol/text */
  getSymbol: () => string;

  /** Get currency code */
  getCode: () => string;

  /** Get currency name */
  getName: () => string;
}

/**
 * Format options for currency formatting
 */
interface FormatOptions {
  /** Override decimal places */
  decimalPlaces?: number;
  /** Include currency symbol/icon in output */
  includeSymbol?: boolean;
  /** Override locale */
  locale?: string;
  /** Show zero as empty string */
  hideZero?: boolean;
}

/**
 * Currency Context
 */
const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

/**
 * Currency Provider Props
 */
interface CurrencyProviderProps {
  children: ReactNode;
  /** Override default currency (optional) */
  defaultCurrency?: string;
}

/**
 * Currency Provider Component
 * 
 * Wrap your app with this provider to enable currency context throughout
 * 
 * Usage:
 * ```tsx
 * <CurrencyProvider>
 *   <App />
 * </CurrencyProvider>
 * ```
 */
export const CurrencyProvider: React.FC<CurrencyProviderProps> = ({
  children,
  defaultCurrency
}) => {
  const [currentCurrency, setCurrentCurrency] = useState<CurrencyConfig>(() => {
    if (defaultCurrency) {
      return getCurrencyConfig(defaultCurrency);
    }
    return getCurrentCurrencyConfig();
  });

  // Update currency when defaultCurrency prop changes
  useEffect(() => {
    if (defaultCurrency) {
      setCurrentCurrency(getCurrencyConfig(defaultCurrency));
    }
  }, [defaultCurrency]);

  /**
   * Change currency (for future multi-currency support)
   */
  const setCurrency = (currencyCode: string) => {
    const newCurrency = getCurrencyConfig(currencyCode);
    setCurrentCurrency(newCurrency);
  };

  /**
   * Get icon variant for context
   */
  const getIconVariant = (context?: keyof typeof CONTEXT_ICON_VARIANTS): LKRCurrencyVariant => {
    return getIconVariantForContext(context);
  };

  /**
   * Format amount with current currency settings
   */
  const formatAmount = (
    amount: number | string | null | undefined,
    options: FormatOptions = {}
  ): string => {
    // Handle null/undefined
    if (amount === null || amount === undefined) {
      return options.hideZero ? '' : '0.00';
    }

    // Convert to number
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(numAmount)) {
      return options.hideZero ? '' : '0.00';
    }

    // Handle zero
    if (numAmount === 0 && options.hideZero) {
      return '';
    }

    // Format number
    const decimalPlaces = options.decimalPlaces ?? currentCurrency.decimalPlaces;
    const locale = options.locale ?? currentCurrency.locale;

    try {
      const formatted = new Intl.NumberFormat(locale, {
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces,
      }).format(numAmount);

      // Return with or without symbol based on options
      if (options.includeSymbol === false) {
        return formatted;
      }

      // For currencies with custom icons, return just the number
      // The icon will be handled by the component
      if (currentCurrency.hasCustomIcon) {
        return formatted;
      }

      // For text-based currencies, include the symbol
      return currentCurrency.symbolPosition === 'before'
        ? `${currentCurrency.symbol}${formatted}`
        : `${formatted}${currentCurrency.symbol}`;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Currency formatting error:', error);
      return numAmount.toFixed(decimalPlaces);
    }
  };

  /**
   * Format amount only (no symbol/icon)
   */
  const formatAmountOnly = (amount: number | string | null | undefined): string => {
    return formatAmount(amount, { includeSymbol: false });
  };

  /**
   * Get currency symbol
   */
  const getSymbol = (): string => {
    return currentCurrency.symbol;
  };

  /**
   * Get currency code
   */
  const getCode = (): string => {
    return currentCurrency.code;
  };

  /**
   * Get currency name
   */
  const getName = (): string => {
    return currentCurrency.name;
  };

  const contextValue: CurrencyContextType = {
    currency: currentCurrency,
    setCurrency,
    getIconVariant,
    hasCustomIcon: currentCurrency.hasCustomIcon,
    formatAmount,
    formatAmountOnly,
    getSymbol,
    getCode,
    getName,
  };

  return (
    <CurrencyContext.Provider value={contextValue}>
      {children}
    </CurrencyContext.Provider>
  );
};

/**
 * Hook to use currency context
 * 
 * Usage:
 * ```tsx
 * const { formatAmount, hasCustomIcon, getIconVariant } = useCurrency();
 * ```
 */
export const useCurrency = (): CurrencyContextType => {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};

export default CurrencyProvider;
