"use client";
import React, { useEffect, useRef, useState } from "react";

interface Props {
  // Add any additional props as needed
}

const Backtotop: React.FC<Props> = () => {
  const [isVisible, setIsVisible] = useState(false);
  const scrollToTopButtonRef = useRef<HTMLDivElement | null>(null);

  const screenUp = () => {
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const shouldShow = window.scrollY > 100;
          if (shouldShow !== isVisible) {
            setIsVisible(shouldShow);
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isVisible]);

  useEffect(() => {
    if (scrollToTopButtonRef.current) {
      scrollToTopButtonRef.current.style.display = isVisible ? "flex" : "none";
    }
  }, [isVisible]);

  return (
    <div
      className="scrollToTop"
      onClick={screenUp}
      ref={scrollToTopButtonRef}
      style={{ display: isVisible ? 'flex' : 'none' }} // inline style for visibility
    >
      <span className="arrow">
        <i className="ti ti-arrow-narrow-up text-xl"></i>
      </span>
    </div>
  );
};

export default Backtotop;
