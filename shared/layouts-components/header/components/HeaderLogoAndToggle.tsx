'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSidebarToggle } from '@/shared/hooks'; // Use hook from parent Header context

interface HeaderLogoAndToggleProps {
    basePath: string;
}

export const HeaderLogoAndToggle: React.FC<HeaderLogoAndToggleProps> = ({ basePath }) => {
    const { toggleSidebar } = useSidebarToggle(); // Consume the hook here

    return (
        <div className="header-content-left flex items-center">
            <div className="header-element">
                <div className="horizontal-logo">
                    <Link href="/dashboard/sales" className="header-logo h-[50px] flex items-center">
                        <Image
                            src={`${process.env.NODE_ENV === "production" ? basePath : ""}/assets/images/header-logo.png`}
                            alt="Golden Island Logo"
                            className="w-auto h-full object-contain"
                            width={84}
                            height={43}
                            style={{ height: '100%' }}
                            priority
                        />
                    </Link>
                </div>
            </div>
            <div className="header-element mx-lg-0">
                <Link
                    aria-label="Toggle Sidebar"
                    onClick={toggleSidebar}
                    className="sidemenu-toggle header-link animated-arrow hor-toggle horizontal-navtoggle p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                    data-bs-toggle="sidebar"
                    scroll={false}
                    href="#!"
                >
                    <span></span>
                </Link>
            </div>
        </div>
    );
};
