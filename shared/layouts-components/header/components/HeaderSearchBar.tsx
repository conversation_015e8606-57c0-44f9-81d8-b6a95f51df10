// shared/layouts-components/header/components/HeaderSearchBar.tsx
'use client';

import React from 'react';
import Link from 'next/link';

interface Props {
    // Add any additional props as needed
}

export const HeaderSearchBar: React.FC<Props> = () => {
    return (
        <div className="header-element header-search md:!block !hidden my-auto auto-complete-search" id="search-modal">
            <input type="text" className="header-search-bar form-control" id="header-search" placeholder="Search for Results..." autoComplete="off" />
            <Link scroll={false} href="#!" className="header-search-icon border-0" aria-label="Search">
                <i className="ri-search-line"></i>
            </Link>
        </div>
    );
};
