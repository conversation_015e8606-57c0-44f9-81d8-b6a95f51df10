// shared/layouts-components/header/components/HeaderRightSection.tsx
'use client';

import React from 'react';
import Image from 'next/image';
import { useLogoutHandler } from '@/shared/hooks';

interface HeaderRightSectionProps {
  basePath: string;
}

/**
 * Header Right Section Container Component
 * 
 * Contains GTM, Wallet, and Logout buttons in a unified container
 * with specific dimensions (162px width × 37px height) and 12px gap
 */
export const HeaderRightSection: React.FC<HeaderRightSectionProps> = ({ basePath: _basePath }) => {
  const { handleLogout, isLoggingOut } = useLogoutHandler();

  return (
    <div
      className="flex items-center"
      style={{
        width: '162px',
        height: '37px',
        gap: '12px'
      }}
    >
      {/* GTM Button */}
      <button
        type="button"
        className="flex items-center justify-center text-white text-sm font-medium transition-all duration-200 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        style={{
          width: '162px',
          height: '37px',
          padding: '10px 12px',
          gap: '6px',
          borderRadius: '4px',
          backgroundColor: '#313133',
          border: 'none',
          cursor: 'pointer'
        }}
        title="Google Tag Manager"
        onMouseEnter={(e) => {
          const target = e.target as HTMLButtonElement;
          target.style.backgroundColor = '#404042';
        }}
        onMouseLeave={(e) => {
          const target = e.target as HTMLButtonElement;
          target.style.backgroundColor = '#313133';
        }}
      >
        <i className="ri-bar-chart-line text-base" aria-hidden="true" />
        <span>GTM</span>
      </button>

      {/* Wallet Button */}
      <button
        type="button"
        className="flex items-center justify-center text-white text-sm font-medium transition-all duration-200 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        style={{
          width: '158px',
          height: '38px',
          padding: '6px 8px',
          gap: '8px',
          borderRadius: '4px',
          backgroundColor: '#313133',
          border: 'none',
          cursor: 'pointer'
        }}
        title="Wallet Transactions"
        onMouseEnter={(e) => {
          const target = e.target as HTMLButtonElement;
          target.style.backgroundColor = '#404042';
        }}
        onMouseLeave={(e) => {
          const target = e.target as HTMLButtonElement;
          target.style.backgroundColor = '#313133';
        }}
      >
        <i className="ri-wallet-3-line text-base" aria-hidden="true" />
        <span>Wallet</span>
      </button>

      {/* Logout Button */}
      <button
        type="button"
        onClick={handleLogout}
        disabled={isLoggingOut}
        className="flex items-center justify-center text-white text-sm font-medium transition-all duration-200 hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
        style={{
          width: '93px',
          height: '36px',
          gap: '10px',
          backgroundColor: '#DC2626',
          border: 'none',
          borderRadius: '4px',
          cursor: isLoggingOut ? 'not-allowed' : 'pointer'
        }}
        title={isLoggingOut ? 'Logging out...' : 'Logout'}
        onMouseEnter={(e) => {
          if (!isLoggingOut) {
            const target = e.target as HTMLButtonElement;
            target.style.backgroundColor = '#B91C1C';
          }
        }}
        onMouseLeave={(e) => {
          if (!isLoggingOut) {
            const target = e.target as HTMLButtonElement;
            target.style.backgroundColor = '#DC2626';
          }
        }}
      >
        {isLoggingOut ? (
          <>
            <div
              className="animate-spin rounded-full border-2 border-white/20 border-t-white"
              style={{ width: '14px', height: '14px' }}
            />
            <span>Logging out...</span>
          </>
        ) : (
          <>
            <Image
              src="/assets/images/profile.png"
              alt="Profile"
              width={20}
              height={20}
              className="rounded-full"
            />
            <span>Logout</span>
          </>
        )}
      </button>
    </div>
  );
};
