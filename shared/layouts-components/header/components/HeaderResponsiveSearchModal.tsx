// shared/layouts-components/header/components/HeaderResponsiveSearchModal.tsx
'use client';

import React from 'react';
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

interface Props {
    // Add any additional props as needed
}

export const HeaderResponsiveSearchModal: React.FC<Props> = () => {
    return (
        <div className="hs-overlay ti-modal hidden" id="header-responsive-search" tabIndex={-1} aria-labelledby="header-responsive-search" aria-hidden="true">
            <div className="ti-modal-box">
                <div className="ti-modal-dialog">
                    <div className="ti-modal-content">
                        <div className="ti-modal-body">
                            <div className="input-group">
                                <input type="text" className="form-control border-end-0 !border-s" placeholder="Search Anything ..."
                                    aria-label="Search Anything ..." aria-describedby="button-addon2" />
                                <SpkButton variant="primary" customClass="ti-btn !m-0" type="button"
                                    Id="button-addon2"><i className="bi bi-search"></i></SpkButton>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
