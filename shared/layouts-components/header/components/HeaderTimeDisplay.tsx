// shared/layouts-components/header/components/HeaderTimeDisplay.tsx
'use client';

import React from 'react';
import { useHeaderTimeLogic } from '@/shared/hooks';

interface HeaderTimeDisplayProps {
    // Add any additional props as needed
}

/**
 * Header Time Display Component
 *
 * Displays user's timezone information with specific design specifications:
 * - Container: 162px width × 37px height
 * - Padding: 10px top/bottom, 12px left/right
 * - Gap: 6px between elements
 * - Border radius: 4px
 * - Background: #313133 (using bg-surface from Tailwind config)
 * - Text: Rubik, 400 weight, 14px size, 100% line height, #FFFFFF color
 * Pure UI component - all logic handled by useHeaderTimeLogic hook
 */
export const HeaderTimeDisplay: React.FC<HeaderTimeDisplayProps> = () => {
    const { timezone } = useHeaderTimeLogic();

    return (
        <li className="header-element time-display rounded-[4px] hidden md:block">
            <div className="flex items-center rounded bg-surface text-white font-rubik font-normal text-sm leading-none gap-1.5 w-auto h-[37px] px-3 py-2.5">
                <i className="ri-time-line text-sm" aria-hidden="true" />
                <span>
                    {timezone}
                </span>
            </div>
        </li>
    );
};
