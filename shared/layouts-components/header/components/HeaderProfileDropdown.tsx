// shared/layouts-components/header/components/HeaderProfileDropdown.tsx
'use client';

import React from 'react';
import Link from 'next/link';
import { SpkBadge } from "@/shared/UI/components";
import SpkDropdown from "@/shared/@spk-reusable-components/uielements/spk-dropdown";
// Make sure to import the updated ProfileMenuItems structure
import { ProfileMenuItems } from '@/shared/data/profileMenuItems';
import { useLogoutHandler } from '@/shared/hooks';

interface HeaderProfileDropdownProps {
    userName?: string;
    userRole?: string;
    userAvatarSrc?: string;
    basePath: string;
}

export const HeaderProfileDropdown: React.FC<HeaderProfileDropdownProps> = ({
    userName = "Mr.Henry",
    userRole = "UI/UX Designer",
    userAvatarSrc = "/assets/images/faces/15.jpg",
    basePath
}) => {
    const { handleLogout, isLoggingOut } = useLogoutHandler();

    return (
        <li>
            <SpkDropdown
                Customclass="header-element"
                Linktag={true}
                Navigate='#!'
                Linkclass='header-link hs-dropdown-toggle ti-dropdown-toggle'
                buttonid="mainHeaderProfile"
                Imagetag={true}
                Imageclass='flex items-center avatar avatar-sm mb-0'
                Image={`${process.env.NODE_ENV === 'production' ? basePath : ''}${userAvatarSrc}`}
                Imagename="User Profile Avatar"
                Custommenuclass='main-header-dropdown pt-0 overflow-hidden header-profile-dropdown '
                Menulabel='mainHeaderProfile'
            >
                <li>
                    <div className="ti-dropdown-item text-center border-b border-defaultborder dark:border-defaultborder/10 block">
                        <span>{userName}</span>
                        <span className="block text-xs text-textmuted dark:text-textmuted/50">
                            {userRole}
                        </span>
                    </div>
                </li>
                {ProfileMenuItems.map(item => {
                    // Type guard to narrow the type for 'link' items
                    if (item.type === 'link') {
                        return (
                            <li key={item.id}>
                                {item.isLogout ? (
                                    <Link
                                        scroll={false}
                                        className="ti-dropdown-item flex items-center"
                                        href={item.href} // Now guaranteed to be string by TypeScript
                                        onClick={handleLogout}
                                        aria-disabled={isLoggingOut}
                                    >
                                        <i className={item.iconClass}></i>
                                        {isLoggingOut ? 'Logging Out...' : item.text}
                                    </Link>
                                ) : (
                                    <Link scroll={false} className="ti-dropdown-item flex items-center" href={item.href}>
                                        <i className={item.iconClass}></i>
                                        {item.text}
                                        {item.badge && (
                                            <SpkBadge variant={item.badge.variant} className={item.badge.customClass}>
                                                {item.badge.value}
                                            </SpkBadge>
                                        )}
                                    </Link>
                                )}
                            </li>
                        );
                    } else if (item.type === 'divider') {
                        return <li key={item.id} className="border-t border-defaultborder dark:border-defaultborder/10 bg-light"></li>; // Or whatever your divider JSX is
                    }
                    return null; // Handle other types or fallbacks
                })}
            </SpkDropdown>
        </li>
    );
};
