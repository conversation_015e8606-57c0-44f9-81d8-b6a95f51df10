// shared/layouts-components/header/components/HeaderLanguageSelector.tsx
'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import SpkDropdown from "@/shared/@spk-reusable-components/uielements/spk-dropdown";
import { LanguageOptions } from '@/shared/data/languageOptions'; // Import the data

interface HeaderLanguageSelectorProps {
    basePath: string;
}

export const HeaderLanguageSelector: React.FC<HeaderLanguageSelectorProps> = ({ basePath }) => {
    return (
        <li>
            <SpkDropdown
                Customclass="header-element country-selector hidden sm:block [--placement:bottom-right] rtl:[--placement:bottom-left]"
                Linktag={true}
                Navigate='#!'
                Customtoggleclass='header-link hs-dropdown-toggle ti-dropdown-toggle'
                Svgicon='m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802'
                SvgClass='w-6 h-6 header-link-icon'
                Linkclass='header-link hs-dropdown-toggle ti-dropdown-toggle'
                Svg={true}
                Custommenuclass='main-header-dropdown min-w-[10rem] hidden'
                SvgStroke="currentColor"
                Strokewidth="1.5"
                Svvgviewbox="0 0 24 24"
            >
                {LanguageOptions.map((option, index) => (
                    <li key={index}>
                        <Link scroll={false} className="ti-dropdown-item flex items-center" href="#!">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                    <span className="avatar avatar-rounded avatar-xs leading-none me-2">
                                        <Image src={`${process.env.NODE_ENV === 'production' ? basePath : ''}${option.flag}`} alt={option.name} width={20} height={20} />
                                    </span>
                                    {option.name}
                                </div>
                            </div>
                        </Link>
                    </li>
                ))}
            </SpkDropdown>
        </li>
    );
};
