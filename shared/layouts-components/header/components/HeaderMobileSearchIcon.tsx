// shared/layouts-components/header/components/HeaderMobileSearchIcon.tsx
'use client';

import React from 'react';

interface Props {
    // Add any additional props as needed
}

export const HeaderMobileSearchIcon: React.FC<Props> = () => {
    return (
        <li className="header-element md:!hidden block">
            <a aria-label="anchor" href="#!" className="header-link" data-bs-toggle="modal" data-hs-overlay="#header-responsive-search">
                <i className="bi bi-search header-link-icon"></i>
            </a>
        </li>
    );
};
