// shared/layouts-components/header/components/HeaderBulkUploadButton.tsx
'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { PrimaryButton } from '@/shared/UI/components';

interface HeaderBulkUploadButtonProps {
    // Add any additional props as needed
}

/**
 * Header Bulk Upload Button Component
 *
 * Displays a bulk upload button next to the GTM time display with specific design specifications:
 * - Container: 162px width × 37px height
 * - Padding: 10px top/bottom, 12px left/right
 * - Gap: 6px between elements
 * - Border radius: 4px
 * - Background: Primary color (golden theme)
 * - Text: Rubik, 400 weight, 14px size, 100% line height, #FFFFFF color
 * Opens the global modal component for bulk upload functionality
 */
export const HeaderBulkUploadButton: React.FC<HeaderBulkUploadButtonProps> = () => {
    const router = useRouter();
    const pathname = usePathname();

    const handleBulkUploadClick = () => {
        // Open the bulk upload modal using URL parameters
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('modal', 'bulk-upload');

        // Navigate to URL with modal parameter
        const newUrl = `${pathname}${currentUrl.search}`;
        router.push(newUrl, { scroll: false });
    };

    return (
        <li className="header-element bulk-upload-button rounded-[4px] hidden md:block">
            <PrimaryButton
                size="sm"
                onClick={handleBulkUploadClick}
                icon={{
                    type: 'FONT_ICON',
                    iconClass: 'ri-upload-line',
                    library: 'remix'
                }}
                iconPosition="left"
                className="h-[37px] px-3 py-2.5"
                title="Bulk Upload Users and RFID Details"
            >
                Bulk Upload
            </PrimaryButton>
        </li>
    );
};
