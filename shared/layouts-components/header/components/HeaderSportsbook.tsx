// shared/layouts-components/header/components/HeaderSportsbook.tsx
'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { PrimaryButton } from '@/shared/UI/components';
interface HeaderSportsbookProps {
    basePath: string;
}

export const HeaderSportsbook: React.FC<HeaderSportsbookProps> = () => {
    const router = useRouter();

    const handleSportsbookNavigation = () => {
        router.push('/sportsbook');
    };

    return (
        <div className="header-element sportsbook-selector">
            <PrimaryButton
                size='sm'
                onClick={handleSportsbookNavigation}
                aria-label="Open Sportsbook Category Selection"
                title="Select your sport category"
                className="whitespace-nowrap"
            >
                Sportsbook
            </PrimaryButton>
        </div>
    );
};
