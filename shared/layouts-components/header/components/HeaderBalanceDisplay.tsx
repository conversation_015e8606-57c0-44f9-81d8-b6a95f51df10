// shared/layouts-components/header/components/HeaderBalanceDisplay.tsx
'use client';

import React from 'react';
import { useHeaderBalanceLogic } from '@/shared/hooks';
import { CurrencyDisplay } from '@/shared/UI/components';

interface HeaderBalanceDisplayProps {
    // Add any additional props as needed
}

/**
 * Header Balance Display Component
 *
 * Displays user balance with automatic currency detection and formatting
 * Uses the centralized currency system - change currency in one place!
 * Pure UI component - all logic handled by useHeaderBalanceLogic hook
 */
export const HeaderBalanceDisplay: React.FC<HeaderBalanceDisplayProps> = () => {
    const { balance: _balance, isLoading, formattedBalance } = useHeaderBalanceLogic();

    return (
        <li className="header-element balance-display hidden md:block">
            <div className="flex items-center text-white text-sm font-medium">
                <i className="ri-wallet-3-line text-base mr-2" aria-hidden="true" />
                {isLoading ? (
                    <span className="text-white font-semibold">Loading...</span>
                ) : (
                    <CurrencyDisplay
                        amount={formattedBalance}
                        context="header"
                        size={16}
                        amountClassName="text-white font-semibold"
                        gap="sm"
                    />
                )}
            </div>
        </li>
    );
};
