// shared/layouts-components/header/Header.tsx
'use client';

import React, { Fragment } from "react";

// Import custom hooks
import { useHeaderLogic } from '@/shared/hooks';

// Import UI sub-components
import { HeaderLogoAndToggle } from './components/HeaderLogoAndToggle';
// import { HeaderSearchBar } from './components/HeaderSearchBar';
import { HeaderMobileSearchIcon } from './components/HeaderMobileSearchIcon';
// import { HeaderLanguageSelector } from './components/HeaderLanguageSelector';
import { HeaderSportsbook } from './components/HeaderSportsbook';
import { HeaderThemeToggle } from './components/HeaderThemeToggle';
// import { HeaderFullScreenToggle } from './components/HeaderFullScreenToggle';
// import { HeaderProfileDropdown } from './components/HeaderProfileDropdown';
// import { HeaderSwitcherIcon } from './components/HeaderSwitcherIcon';
import { HeaderResponsiveSearchModal } from './components/HeaderResponsiveSearchModal';
import HeaderWalletTransactions from './components/HeaderWalletTransactions';
import { HeaderTimeDisplay } from './components/HeaderTimeDisplay';
import { HeaderProfileIcon } from './components/HeaderProfileIcon';
import HorizontalNavigation from '../navigation/HorizontalNavigation';

interface Props {
  // Add any additional props as needed
}

const Header: React.FC<Props> = () => {
  const { basePath, overlayRef, menuClose } = useHeaderLogic();

  return (
    <Fragment>
      {/* Overlay for mobile sidebar, uses ref and menuClose from useSidebarToggle */}
      <div id="responsive-overlay" ref={overlayRef} onClick={menuClose}></div>

      <header className="app-header" id="header">
        <div className="main-header-container container-fluid">
          {/* Mobile Layout (< lg) */}
          <div className="lg:hidden flex items-center justify-between w-full">
            {/* Mobile Left: Logo & Sidebar Toggle */}
            <HeaderLogoAndToggle basePath={basePath} />

            {/* Mobile Center: Sportsbook Button */}
            <div className="flex-1 flex justify-center">
              <HeaderSportsbook basePath={basePath} />
            </div>

            {/* Mobile Right: Essential items only */}
            <div className="flex items-center space-x-2">
              <HeaderWalletTransactions />
              <HeaderTimeDisplay />
              <HeaderProfileIcon basePath={basePath} />
            </div>
          </div>

          {/* Desktop Layout (>= lg) */}
          <div className="hidden lg:flex items-center justify-between w-full">
            {/* Desktop Left: Logo & Sidebar Toggle */}
            <div className="flex items-center">
              <HeaderLogoAndToggle basePath={basePath} />
            </div>

            {/* Desktop Center: Sportsbook Component - Properly centered */}
            <div className="flex-1 flex justify-center">
              <HeaderSportsbook basePath={basePath} />
            </div>

            {/* Desktop Right: All items */}
            <div className="flex items-center space-x-4">
              <HeaderTimeDisplay />
              <HeaderWalletTransactions />
              <HeaderThemeToggle />
              <HeaderProfileIcon basePath={basePath} />
            </div>
          </div>

          {/* Hidden mobile/utility components */}
          <ul className="header-content-right hidden">
            {/* Mobile Search Icon */}
            <HeaderMobileSearchIcon />
          </ul>
        </div>
        <HorizontalNavigation />
      </header>
      {/* Responsive Search Modal Component */}
      <HeaderResponsiveSearchModal />

    </Fragment>
  );
};

export default Header;
