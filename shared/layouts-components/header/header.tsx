// shared/layouts-components/header/Header.tsx
'use client';

import React, { Fragment } from "react";

// Import custom hooks
import { useHeaderLogic } from '@/shared/hooks';

// Import UI sub-components
import { HeaderLogoAndToggle } from './components/HeaderLogoAndToggle';
// import { HeaderSearchBar } from './components/HeaderSearchBar';
import { HeaderMobileSearchIcon } from './components/HeaderMobileSearchIcon';
// import { HeaderLanguageSelector } from './components/HeaderLanguageSelector';
import { HeaderSportsbook } from './components/HeaderSportsbook';
import { HeaderThemeToggle } from './components/HeaderThemeToggle';
// import { HeaderFullScreenToggle } from './components/HeaderFullScreenToggle';
// import { HeaderProfileDropdown } from './components/HeaderProfileDropdown';
// import { HeaderSwitcherIcon } from './components/HeaderSwitcherIcon';
import { HeaderResponsiveSearchModal } from './components/HeaderResponsiveSearchModal';
import HeaderWalletTransactions from './components/HeaderWalletTransactions';
import { HeaderTimeDisplay } from './components/HeaderTimeDisplay';
import { HeaderProfileIcon } from './components/HeaderProfileIcon';
import { HeaderBulkUploadButton } from './components/HeaderBulkUploadButton';
import HorizontalNavigation from '../navigation/HorizontalNavigation';

interface Props {
  // Add any additional props as needed
}

const Header: React.FC<Props> = () => {
  const { basePath, overlayRef, menuClose } = useHeaderLogic();

  return (
    <Fragment>
      {/* Overlay for mobile sidebar, uses ref and menuClose from useSidebarToggle */}
      <div id="responsive-overlay" ref={overlayRef} onClick={menuClose}></div>

      <header className="app-header" id="header">
        <div className="main-header-container container-fluid relative">
          {/* Header Left Section: Logo & Sidebar Toggle */}
          <HeaderLogoAndToggle basePath={basePath} />

          {/* Center: Sportsbook Component - Centered in header */}
          <div className="flex absolute inset-0 justify-center items-center z-[0] m-auto w-fit">
            <HeaderSportsbook basePath={basePath} />
          </div>

          {/* Right Section: GMT, Bulk Upload, Wallet, and Logout */}
          <div className="flex items-center space-x-4 ml-[auto]">
            {/* Bulk Upload Button */}
            <HeaderBulkUploadButton />

            {/* GMT Section */}
            <HeaderTimeDisplay />

            {/* Wallet Section */}
            <HeaderWalletTransactions />

            {/* Theme Toggle Component (Hidden on mobile to match design) */}
            <div className="hidden lg:block">
              <HeaderThemeToggle />
            </div>

            {/* Logout Button */}
            <HeaderProfileIcon basePath={basePath} />
          </div>

          {/* Hidden mobile/utility components */}
          <ul className="header-content-right hidden">
            {/* Mobile Search Icon */}
            <HeaderMobileSearchIcon />

            {/* Full screen toggle Component (Hidden on mobile to match design) */}
            {/* <div className="hidden lg:block">
              <HeaderFullScreenToggle />
            </div> */}

            {/* Switcher Icon Component (Hidden on mobile to match design) */}
            {/* <div className="hidden lg:block">
              <HeaderSwitcherIcon />
            </div> */}
          </ul>
        </div>
        <HorizontalNavigation />
      </header>
      {/* Responsive Search Modal Component */}
      <HeaderResponsiveSearchModal />

    </Fragment>
  );
};

export default Header;
