"use client";

import { useHorizontalNavigation } from '@/shared/hooks/ui/useHorizontalNavigation';
import { useModalNavigation } from '@/shared/hooks/ui/useModalNavigation';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import React, { Fragment } from 'react';

interface MenuItem {
  title: string;
  icon: React.ReactNode;
  type: 'link' | 'sub' | 'modal';
  path?: string;
  active?: boolean;
  selected?: boolean;
  children?: MenuItem[];
}

interface HorizontalNavigationProps {
  // Add any additional props as needed
}

const HorizontalNavigation: React.FC<HorizontalNavigationProps> = () => {
  const router = useRouter();
  const pathname = usePathname();

  const {
    activeDropdown,
    isMobileMenuOpen,
    dropdownRef,
    mobileMenuRef,
    menuItems,
    isMenuItemActive,
    toggleDropdown,
    setActiveDropdown,
    setIsMobileMenuOpen,
    closeMobileMenu,
  } = useHorizontalNavigation();

  // Use centralized modal navigation
  const { handleModalClick: openModal } = useModalNavigation();

  // Handle modal navigation - parse path and use centralized function
  const handleModalClick = (path: string) => {
    if (path.startsWith('?')) {
      // Parse query parameters from path
      const params = new URLSearchParams(path);
      const modal = params.get('modal');
      const mode = params.get('mode');
      const userId = params.get('userId');

      if (modal) {
        openModal({
          modal,
          ...(mode && { mode }),
          ...(userId && { entityId: userId })
        });
      }
    } else {
      // Fallback to direct navigation for non-modal paths
      router.push(`${pathname}${path}`, { scroll: false });
    }
  };

  // Render navigation item
  const renderNavItem = (item: MenuItem, section: 'left' | 'right') => {
    const isActive = isMenuItemActive(item);
    const isDropdownOpen = activeDropdown === item.title;

    const baseClasses = `
      flex items-center gap-2 px-2 py-2 rounded-md text-sm font-rubik font-normal
      transition-colors duration-200 cursor-pointer relative
      ${section === 'left'
        ? 'text-[#9FA3B6] hover:text-[#9FA3B6]/80'
        : 'text-[var(--golden)] hover:text-[var(--golden)]/80'
      }
      ${isActive ? (section === 'left' ? 'text-white' : 'text-[var(--golden)]') : ''}
    `;

    if (item.type === 'link' && item.path) {
      return (
        <Link
          key={item.title}
          href={item.path}
          className={baseClasses}
        >
          {item.icon}
          <span className="whitespace-nowrap">{item.title}</span>
        </Link>
      );
    }

    if (item.type === 'modal' && item.path) {
      return (
        <button
          key={item.title}
          onClick={() => handleModalClick(item.path!)}
          className={baseClasses}
        >
          {item.icon}
          <span className="whitespace-nowrap">{item.title}</span>
        </button>
      );
    }

    if (item.type === 'sub' && item.children) {
      return (
        <div key={item.title} className="relative" ref={dropdownRef}>
          <button
            onClick={() => toggleDropdown(item.title)}
            className={`${baseClasses} ${isDropdownOpen ? 'bg-black/10' : ''}`}
          >
            {item.icon}
            <span>{item.title}</span>
            <svg
              className={`w-3 h-3 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 min-w-[200px] bg-[#1D1D1D] border border-gray-700 rounded-md shadow-lg z-50">
              {item.children.map((child, index) => (
                <Link
                  key={index}
                  href={child.path || '#'}
                  className="block px-4 py-2 text-sm text-[#9FA3B6] hover:text-white hover:bg-black/20 first:rounded-t-md last:rounded-b-md"
                  onClick={() => setActiveDropdown(null)}
                >
                  {child.title}
                </Link>
              ))}
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  // Render divider
  const renderDivider = (section: 'left' | 'right', index: number) => (
    <div
      key={`divider-${section}-${index}`}
      className={`w-[2px] h-[70%] mx-[10px] ${section === 'left' ? 'bg-[#626573]' : 'bg-[#40320E]'
        }`}
    />
  );

  return (
    <Fragment>
      {/* Desktop Navigation */}
      <nav className="hidden lg:flex w-full h-[52px] bg-nav justify-between items-center px-3  border-t border-[#3B3B3B]">
        {/* Left Section Navigation */}
        <div className="w-[auto] h-[36px] flex items-center gap-2">
          {menuItems.left.map((item: MenuItem, index: number) => (
            <Fragment key={item.title}>
              {renderNavItem(item, 'left')}
              {index < menuItems.left.length - 1 && renderDivider('left', index)}
            </Fragment>
          ))}
        </div>

        {/* Right Section Navigation */}
        <div className="flex w-[auto] h-[36px] items-center gap-2">
          {menuItems.right.map((item: MenuItem, index: number) => (
            <Fragment key={item.title}>
              {renderNavItem(item, 'right')}
              {index < menuItems.right.length - 1 && renderDivider('right', index)}
            </Fragment>
          ))}
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className="lg:hidden w-full h-[52px] bg-[#1D1D1D] flex justify-between items-center px-3" ref={mobileMenuRef}>
        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="flex items-center gap-2 text-[#9FA3B6] hover:text-white"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <span className="text-sm font-rubik">Menu</span>
        </button>

        {/* Mobile Right Actions */}
        <div className="flex items-center gap-2">
          {menuItems.right.slice(0, 2).map((item: MenuItem) => (
            <Link
              key={item.title}
              href={item.path || '#'}
              className="flex items-center gap-1 px-2 py-1 text-[var(--golden)] hover:text-[var(--golden)]/80"
            >
              {item.icon}
              <span className="hidden sm:inline text-xs">{item.title}</span>
            </Link>
          ))}
        </div>

        {/* Mobile Dropdown Menu with slide-in animation */}
        {isMobileMenuOpen && (
          <div className="fixed top-[104px] left-0 right-0 bg-[#1D1D1D] border-t border-gray-700 shadow-lg z-50 animate-slide-down">
            <div className="p-4 space-y-2 max-h-[calc(100vh-104px)] overflow-y-auto">
              {/* Left Section Items */}
              <div className="space-y-2">
                <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider">Navigation</h3>
                {menuItems.left.map((item: MenuItem) => (
                  <div key={item.title}>
                    {item.type === 'link' && item.path ? (
                      <Link
                        href={item.path}
                        className="flex items-center gap-3 px-3 py-2 text-[#9FA3B6] hover:text-white hover:bg-black/20 rounded-md transition-colors"
                        onClick={closeMobileMenu}
                      >
                        {item.icon}
                        <span>{item.title}</span>
                      </Link>
                    ) : item.type === 'sub' && item.children ? (
                      <div>
                        <div className="flex items-center gap-3 px-3 py-2 text-[#9FA3B6]">
                          {item.icon}
                          <span>{item.title}</span>
                        </div>
                        <div className="ml-6 space-y-1">
                          {item.children.map((child, index) => (
                            child.type === 'modal' ? (
                              <button
                                key={index}
                                onClick={() => {
                                  handleModalClick(child.path || '');
                                  closeMobileMenu();
                                }}
                                className="block w-full text-left px-3 py-1 text-sm text-[#9FA3B6] hover:text-white hover:bg-black/20 rounded-md transition-colors"
                              >
                                {child.title}
                              </button>
                            ) : (
                              <Link
                                key={index}
                                href={child.path || '#'}
                                className="block px-3 py-1 text-sm text-[#9FA3B6] hover:text-white hover:bg-black/20 rounded-md transition-colors"
                                onClick={closeMobileMenu}
                              >
                                {child.title}
                              </Link>
                            )
                          ))}
                        </div>
                      </div>
                    ) : null}
                  </div>
                ))}
              </div>

              {/* Right Section Items */}
              <div className="space-y-2 pt-4 border-t border-gray-700">
                <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider">Actions</h3>
                {menuItems.right.map((item: MenuItem) => (
                  item.type === 'modal' ? (
                    <button
                      key={item.title}
                      onClick={() => {
                        handleModalClick(item.path || '');
                        closeMobileMenu();
                      }}
                      className="flex w-full text-left items-center gap-3 px-3 py-2 text-[var(--golden)] hover:text-[var(--golden)]/80 hover:bg-black/20 rounded-md transition-colors"
                    >
                      {item.icon}
                      <span>{item.title}</span>
                    </button>
                  ) : (
                    <Link
                      key={item.title}
                      href={item.path || '#'}
                      className="flex items-center gap-3 px-3 py-2 text-[var(--golden)] hover:text-[var(--golden)]/80 hover:bg-black/20 rounded-md transition-colors"
                      onClick={closeMobileMenu}
                    >
                      {item.icon}
                      <span>{item.title}</span>
                    </Link>
                  )
                ))}
              </div>
            </div>
          </div>
        )}
      </nav>
    </Fragment>
  );
};

export default HorizontalNavigation;
