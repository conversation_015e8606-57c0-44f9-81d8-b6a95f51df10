"use client";
// import Link from 'next/link';
import React, { Fragment } from 'react';

interface Props {
  // Add any additional props as needed
}

const Footer: React.FC<Props> = () => {
  return (
    <Fragment>
      <footer className="footer mt-auto py-3 bg-white dark:bg-section border-border-primary dark:border-border-secondary text-center">
        <div className="container">
          <span className="text-text-secondary"> Copyright © <span className='text-text-secondary' id="year"> 2025 </span>
            All rights reserved </span>
        </div>
      </footer>
    </Fragment>
  );
};

export default Footer;
