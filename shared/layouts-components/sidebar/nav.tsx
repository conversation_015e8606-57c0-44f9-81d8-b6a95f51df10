
// Navigation Icons for Horizontal Navigation
const DashboardIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path></svg>;

const UsersIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"></path></svg>;

const BetReportIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"></path></svg>;

const FinancialReportIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H6m-3.75 0h3.75m0 0v.375c0 .621.504 1.125 1.125 1.125H9m-6 0h6m0 0v.75c0 .414.336.75.75.75h.75m-9 0v5.625c0 .621.504 1.125 1.125 1.125h2.25c.621 0 1.125-.504 1.125-1.125V15h1.5v-.75a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 1-.75-.75V9.75a.75.75 0 0 1 .75-.75h3c.414 0 .75.336.75.75v.75H9V9a2.25 2.25 0 0 1 2.25-2.25H15a2.25 2.25 0 0 1 2.25 2.25v.75h1.5V9a3.75 3.75 0 0 0-3.75-3.75H11.25A3.75 3.75 0 0 0 7.5 9v.75H6V9a.75.75 0 0 1 .75-.75h.75"></path></svg>;

const CashierReportIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"></path></svg>;

const ExportCenterIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"></path></svg>;

const AddPlayerIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.764Z"></path></svg>;

const CheckResultIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"></path></svg>;

const BulkUploadIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"></path></svg>;

// Legacy icons for backward compatibility
const Dashboardicon = DashboardIcon;
const Erroricon = <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 side-menu__icon" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"> <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"></path> </svg>;
const NestedmenuIcon = <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 side-menu__icon" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor"> <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"></path> </svg>;
// Horizontal Navigation Menu Items Configuration
export const HORIZONTAL_MENUITEMS: any = {
  left: [
    {
      title: "Dashboard",
      icon: DashboardIcon,
      type: "link",
      path: "/dashboard",
      active: false,
      selected: false,
    },
    {
      title: "Users",
      icon: UsersIcon,
      type: "sub",
      active: false,
      selected: false,
      children: [
        { path: "/user-management", type: "link", active: false, selected: false, title: "User Listing" },
        { path: "/login-history", type: "link", active: false, selected: false, title: "Login History" },
      ]
    },
    {
      title: "Bet Report",
      icon: BetReportIcon,
      type: "link",
      path: "/bet-report",
      active: false,
      selected: false,
    },
    {
      title: "Financial Report",
      icon: FinancialReportIcon,
      type: "link",
      path: "/financial-report",
      active: false,
      selected: false,
    },
    {
      title: "Cashier Report",
      icon: CashierReportIcon,
      type: "link",
      path: "/cashier-report",
      active: false,
      selected: false,
    },
  ],
  right: [
    {
      title: "Bulk Upload",
      icon: BulkUploadIcon,
      type: "modal",
      path: "?modal=bulk-upload",
      active: false,
      selected: false,
    },
    {
      title: "Export Center",
      icon: ExportCenterIcon,
      type: "modal",
      path: "?modal=export-center",
      active: false,
      selected: false,
    },
    {
      title: "Add Player",
      icon: AddPlayerIcon,
      type: "modal",
      path: "?modal=user-management&mode=create",
      active: false,
      selected: false,
    },
    {
      title: "Check Result",
      icon: CheckResultIcon,
      type: "modal",
      path: "?modal=check-result",
      active: false,
      selected: false,
    },
  ]
};

// Legacy MENUITEMS for backward compatibility with existing sidebar
export const MENUITEMS: any = [
  {
    menutitle: "MAIN",
  },
  {
    title: "Dashboards", icon: Dashboardicon, type: "sub", active: false, children: [
      { path: "/dashboard", type: "link", active: false, selected: false, title: "Overview" },
    ]
  },
  {
    title: "User Management", icon: Dashboardicon, type: "sub", active: false, children: [
      { path: "/user-management", type: "link", active: false, selected: false, title: "All Users" },
      { path: "/user-management/create", type: "link", active: false, selected: false, title: "Create User" },
    ]
  },
  {
    menutitle: "WEB APPS"
  },
  {
    icon: NestedmenuIcon,
    title: "Nested Menu",
    selected: false,
    active: false,
    type: "sub",
    children: [
      {
        path: "",
        title: "Nested-1",
        type: "empty",
        active: false,
        selected: false,
        dirchange: false,
      },
      {
        title: "Nested-2",
        type: "sub",
        active: false,
        selected: false,
        dirchange: false,
        children: [
          {
            path: "",
            type: "empty",
            active: false,
            selected: false,
            dirchange: false,
            title: "Nested-2-1",
          },
          {
            path: "",
            type: "empty",
            active: false,
            selected: false,
            dirchange: false,
            title: "Nested-2-2",
          },
          {
            path: "",
            type: "empty",
            active: false,
            selected: false,
            dirchange: false,
            title: "Nested-2-3",
          },
        ],
      },
    ],
  },
  {
    menutitle: "PAGES"
  },
  {
    icon: Erroricon, title: "Error", type: "sub", active: false, selected: false, children: [
      { path: "/authentication/error/401-error", type: "link", active: false, selected: false, title: "401-Error" },
      { path: "/authentication/error/404-error", type: "link", active: false, selected: false, title: "404-Error" },
      { path: "/authentication/error/500-error", type: "link", active: false, selected: false, title: "500-Error" },
    ]
  },
];
