
import React, { forwardRef, Fragment } from "react";
import <PERSON> from "next/link";

// Import your Zustand UI store
import { useUIStore } from "@/shared/stores/uiStore";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

interface MenuItem {
  menutitle?: string;
  type: 'link' | 'sub' | 'empty';
  path?: string;
  title: string;
  icon: React.ReactNode;
  selected?: boolean;
  active?: boolean;
  badgetxt?: string;
  class?: string;
  children?: MenuItem[];
  dirchange?: boolean;
  [key: string]: any; // For additional properties
}

interface MenuloopProps {
  MenuItems: MenuItem;
  toggleSidemenu: (event: React.MouseEvent, menuItem: MenuItem, arg3?: any, arg4?: boolean) => void;
  level: number;
  HoverToggleInnerMenuFn: (event: React.MouseEvent, menuItem: MenuItem) => void;
}

const Menuloop = forwardRef<HTMLElement, MenuloopProps>(({ MenuItems, toggleSidemenu, level, HoverToggleInnerMenuFn }, ref) => {
  // Access the UI state from your Zustand store
  const uiState = useUIStore(); // Replaces local_varaiable

  const handleClick = (event: any) => {
    event.preventDefault(); // Prevents default anchor behavior (navigation)
  };

  return (
    <Fragment>
      <Link href="#!" scroll={false} className={`side-menu__item ${MenuItems?.selected ? "active" : ""}`}

        onClick={(event) => { event.preventDefault(); toggleSidemenu(event, MenuItems, undefined, true); }}
        onMouseEnter={(event) => HoverToggleInnerMenuFn(event, MenuItems)}
      >
        <span className={`${uiState.dataVerticalStyle == 'doublemenu' ? '' : 'd-none'}`}> {/* Use uiState */}
          <span className={`hs-tooltip inline-block [--placement:right] leading-none ${uiState.dataVerticalStyle == 'doublemenu' ? '' : 'hidden'}`}> {/* Use uiState */}
            <SpkButton type="button" customClass="hs-tooltip-toggle  inline-flex justify-center items-center">
              {MenuItems.icon}
              <span className="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity inline-block absolute invisible z-10 !py-2 !px-3 !rounded-md bg-black text-xs font-medium text-white shadow-sm dark:bg-black" role="tooltip">
                {MenuItems.title}
              </span>
            </SpkButton>
          </span>
        </span>
        {uiState.dataVerticalStyle != "doublemenu" ? MenuItems.icon : ""} {/* Use uiState */}

        <span className={`${level == 1 ? "side-menu__label" : ""}`}> {MenuItems.title} {MenuItems.badgetxt ? (<span className={MenuItems.class}> {MenuItems.badgetxt} </span>
        ) : (
          ""
        )}
        </span>
        <i className="ri-arrow-down-s-line side-menu__angle"></i>
      </Link>
      <ul ref={ref as React.Ref<HTMLUListElement>} className={`slide-menu child${level}  ${MenuItems.active ? 'double-menu-active' : ''} ${MenuItems?.dirchange ? "force-left" : ""} `} style={MenuItems.active ? { display: "block" } : { display: "none" }}>
        {level <= 1 ? <li className="slide side-menu__label1">
          <Link href="#!" scroll={false}>{MenuItems.title}</Link>
        </li> : ""}
        {MenuItems.children?.map((firstlevel: MenuItem, index: number) =>
          <li className={`${firstlevel.menutitle ? 'slide__category' : ''} ${firstlevel?.type == 'empty' ? 'slide' : ''} ${firstlevel?.type == 'link' ? 'slide' : ''} ${firstlevel?.type == 'sub' ? 'slide has-sub' : ''} ${firstlevel?.active ? 'open' : ''} ${firstlevel?.selected ? 'active' : ''}`} key={index}>
            {firstlevel.type === "link" ?
              <Link href={firstlevel.path || '#!'} className={`side-menu__item ${firstlevel.selected ? 'active' : ''}`}>{firstlevel.icon}
                <span className=""> {firstlevel.title} {firstlevel.badgetxt ? (<span className={firstlevel.class}> {firstlevel.badgetxt}</span>
                ) : (
                  ""
                )}
                </span>
              </Link>
              : ""}
            {firstlevel.type === "empty" ?
              <Link href="#!" className='side-menu__item' onClick={handleClick}> {firstlevel.icon}<span className=""> {firstlevel.title} {firstlevel.badgetxt ? (<span className={firstlevel.class}> {firstlevel.badgetxt} </span>
              ) : (
                ""
              )}
              </span>
              </Link>
              : ""}
            {firstlevel.type === "sub" ?
              <Menuloop // Recursive call to Menuloop
                MenuItems={firstlevel}
                toggleSidemenu={toggleSidemenu}
                HoverToggleInnerMenuFn={HoverToggleInnerMenuFn}
                level={level + 1}

              />
              : ''}
          </li>
        )}
      </ul>
    </Fragment>
  );
});

Menuloop.displayName = 'Menuloop';

export default Menuloop; // Export the component directly
