"use client";

import React from 'react';

interface SidebarProps {
	// Add any additional props as needed
}

const Sidebar: React.FC<SidebarProps> = () => {

	// Import and use the new horizontal navigation component
	const HorizontalNavigation = React.lazy(() => import('../navigation/HorizontalNavigation'));

	return (
		<React.Suspense fallback={<div className="w-full h-[52px] bg-[#1D1D1D]" />}>
			<HorizontalNavigation />
		</React.Suspense>
	);
};

// No mapStateToProps or connect needed anymore!
export default Sidebar;
