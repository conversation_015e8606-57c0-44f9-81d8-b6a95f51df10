import nextConfig from "@/next.config"; // Import your next.config.js file
import React from 'react';
import Image from 'next/image';

interface Props {
  // Add any additional props as needed
}

const Loader: React.FC<Props> = () => {
  let { basePath } = nextConfig;
  return <div id="loader" className="loader " suppressHydrationWarning={true}>
    <Image src={`${process.env.NODE_ENV === 'production' ? basePath : ''}/assets/images/media/loader.svg`} alt="Loading..." width={50} height={50} />
  </div>;

};

export default Loader;
