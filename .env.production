# =============================================================================
# BETSHOP ADMIN PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================

# -----------------------------------------------------------------------------
# API ENDPOINTS - PRODUCTION
# -----------------------------------------------------------------------------

# Admin Backend API - Used for authentication and admin operations
NEXT_PUBLIC_ADMIN_BACKEND_URL=https://adminapi.ingrandstation.com

# Staging Backend API - Used for user management and reporting
NEXT_PUBLIC_STAGING_BACKEND_URL=https://staging-reports-api.8dexsuperadmin.com

# Reporting Backend API - Used for reporting and analytics
NEXT_PUBLIC_REPORTING_BACKEND_URL=https://reporting.ingrandstation.com

# -----------------------------------------------------------------------------
# APPLICATION SETTINGS - PRODUCTION
# -----------------------------------------------------------------------------

# Base URL for the application (used for SEO and absolute URLs)
NEXT_PUBLIC_BASE_URL=https://cashier.ingrandstation.com

# Google Analytics ID for production
# NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# -----------------------------------------------------------------------------
# SEO & VERIFICATION - PRODUCTION
# -----------------------------------------------------------------------------

# Google Search Console verification for production
# NEXT_PUBLIC_GOOGLE_VERIFICATION=your-google-verification-code

# Yandex verification (if needed)
# NEXT_PUBLIC_YANDEX_VERIFICATION=your-yandex-verification-code

# Yahoo verification (if needed)
# NEXT_PUBLIC_YAHOO_VERIFICATION=your-yahoo-verification-code

# -----------------------------------------------------------------------------
# SECURITY & PERFORMANCE - PRODUCTION
# -----------------------------------------------------------------------------

# Node environment
NODE_ENV=production
