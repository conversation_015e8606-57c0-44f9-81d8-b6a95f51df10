import { FlatCompat } from "@eslint/eslintrc";
import js from "@eslint/js";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import tsParser from "@typescript-eslint/parser";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import path from "node:path";
import { fileURLToPath } from "node:url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

const config = [
    // Ignore patterns for performance
    {
        ignores: [
            "node_modules/**",
            ".next/**",
            "out/**",
            "dist/**",
            "build/**",
            "coverage/**",
            ".cache/**",
            "public/**",
            "*.min.js",
            "*.bundle.js"
        ]
    },

    // Base configuration for all files
    ...compat.extends(
        "eslint:recommended",
        "plugin:react/recommended",
        "plugin:react-hooks/recommended",
        "next/core-web-vitals"
    ),

    // Configuration for JavaScript files
    {
        files: ["**/*.js", "**/*.jsx"],
        plugins: {
            react,
            "react-hooks": reactHooks,
        },

        languageOptions: {
            ecmaVersion: "latest",
            sourceType: "module",
            parserOptions: {
                ecmaFeatures: {
                    jsx: true,
                },
            },
        },

        settings: {
            react: {
                version: "detect",
            },
        },

        rules: {
            indent: "off",
            "linebreak-style": ["error", "unix"],
            quotes: "off",
            semi: ["error", "always"],
            "no-multiple-empty-lines": ["error", {
                max: 1,
                maxEOF: 1,
            }],
            "no-console": "warn",
            "eol-last": "error",
            "block-spacing": "error",
            "vars-on-top": "error",
            "no-useless-return": "error",
            "no-useless-escape": "error",
            "no-lone-blocks": "error",
            "no-empty": "error",
            camelcase: "error",
            "no-empty-function": "off",
            "no-whitespace-before-property": "error",
            "no-mixed-spaces-and-tabs": "error",
            "react/react-in-jsx-scope": "off",
            "react/no-unknown-property": "off",
            "react/no-unescaped-entities": "off",
            "no-unused-vars": ["error", {
                argsIgnorePattern: "^_",
                varsIgnorePattern: "^_",
            }],
        },
    },

    // Configuration for TypeScript files
    {
        files: ["**/*.ts", "**/*.tsx"],
        plugins: {
            react,
            "react-hooks": reactHooks,
            "@typescript-eslint": typescriptEslint,
        },

        languageOptions: {
            parser: tsParser,
            ecmaVersion: "latest",
            sourceType: "module",
            parserOptions: {
                ecmaFeatures: {
                    jsx: true,
                },
            },
        },

        settings: {
            react: {
                version: "detect",
            },
        },

        rules: {
            indent: "off",
            "linebreak-style": ["error", "unix"],
            quotes: "off",
            semi: ["error", "always"],
            "no-multiple-empty-lines": ["error", {
                max: 1,
                maxEOF: 1,
            }],
            "no-console": "warn",
            "eol-last": "error",
            "block-spacing": "error",
            "vars-on-top": "error",
            "no-useless-return": "error",
            "no-useless-escape": "error",
            "no-lone-blocks": "error",
            "no-empty": "error",
            camelcase: "error",
            "no-empty-function": "off",
            "no-whitespace-before-property": "error",
            "no-mixed-spaces-and-tabs": "error",
            "react/react-in-jsx-scope": "off",
            "react/no-unknown-property": "off",
            "react/no-unescaped-entities": "off",
            // Disable regular no-unused-vars for TypeScript files
            "no-unused-vars": "off",
            // Enable TypeScript-specific no-unused-vars rule
            "@typescript-eslint/no-unused-vars": ["error", {
                argsIgnorePattern: "^_",
                varsIgnorePattern: "^_",
            }],
            "@typescript-eslint/no-explicit-any": "off",
            "@typescript-eslint/ban-ts-comment": "off",
        },
    }
];

export default config;
