@use "../public/assets/scss/tailwind/tailwind";

@import "../public/assets/css/styles.css";
@import "simplebar-react/dist/simplebar.min.css";

/* Icon fonts are now loaded directly in layout.tsx head for better reliability */

/* Custom animations moved to CSS modules in app/css/animations/ */

/* SignIn animations moved to CSS modules in app/css/animations/background-cycle.module.css */

/* Performance optimizations moved to CSS modules in app/css/utilities/performance.module.css */

/* Global performance optimizations that need to remain global */
* {
  font-display: swap;
}

img {
  height: auto;
  max-width: 100%;
}

table {
  table-layout: fixed;
}

/* Browser extension compatibility and hydration fixes */
/* Hide elements added by browser extensions that might cause hydration mismatches */
[bis_skin_checked],
[data-adblock-key],
[data-ublock],
[data-ghostery],
[data-avast],
[data-kaspersky],
[data-mcafee],
[data-norton],
[data-malwarebytes],
[data-extension-id] {
  /* Don't hide the elements, just ensure they don't affect layout */
  /* Browser extensions need these attributes for functionality */
}

/* Prevent browser extension injected styles from affecting our layout */
* {
  /* Ensure consistent box-sizing */
  box-sizing: border-box;
}

/* Suppress hydration warnings for elements that might be modified by extensions */
.hydration-safe {
  /* This class can be used on elements that are known to be modified by browser extensions */
}
