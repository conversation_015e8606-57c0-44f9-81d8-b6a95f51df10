"use client";

import React from "react";
import { useContentLayoutLogic } from "@/shared/hooks/business/useContentLayoutLogic";
import { ContentLayoutUI } from "@/shared/UI/layouts/ContentLayoutUI";

const ContentLayout = ({ children }: { children: React.ReactNode }) => {
  // Use custom hook for all business logic
  const {
  isAuthenticated,
  hasHydrated,
  isBetPopupOpen,
  currentBetDetails,
  hideBetPopup,
  pathname,
} = useContentLayoutLogic();

// Render pure UI component with data from hook
return (
  <ContentLayoutUI
	isAuthenticated={isAuthenticated}
	hasHydrated={hasHydrated}
	isBetPopupOpen={isBetPopupOpen}
	currentBetDetails={currentBetDetails}
	hideBetPopup={hideBetPopup}
	pathname={pathname}
  >
	{children}

  </ContentLayoutUI>
);
};

export default ContentLayout;
