// app/(components)/(content-layout)/login-history/page.tsx - Server-side rendered login history with SEO optimization
import React from "react";
import type { Metadata } from "next";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { LoginHistoryPageClient } from "./components/LoginHistoryPageClient";
import { UserManagementErrorBoundary } from "@/shared/components/error-boundaries/SSRErrorBoundary";

// Force dynamic rendering for this page since it accesses cookies for authentication
export const dynamic = "force-dynamic";

// Generate metadata for the login history page (server-side)
export const metadata: Metadata = {
	title: "Login History | Admin Dashboard",
	description: "View and monitor user login history with detailed information about IP addresses, devices, and locations.",
	keywords: ["login history", "user activity", "security monitoring", "admin dashboard"],
	openGraph: {
		title: "Login History | Admin Dashboard",
		description: "View and monitor user login history with detailed information about IP addresses, devices, and locations.",
		type: "website",
		url: "/login-history",
	},
	twitter: {
		card: "summary_large_image",
		title: "Login History | Admin Dashboard",
		description: "View and monitor user login history with detailed information about IP addresses, devices, and locations.",
	},
	robots: {
		index: false, // Admin pages should not be indexed
		follow: false,
	},
};

type Props = {};

/**
 * Server-side rendered login history page component
 * Handles SEO metadata generation and delegates client-side logic to LoginHistoryPageClient
 *
 * Features:
 * - SEO optimization with structured data
 * - Error boundary for graceful error handling
 * - Consistent layout with other management pages
 */
const LoginHistoryPage: React.FC<Props> = async () => {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Login History" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Login History System"
				description="Comprehensive login history monitoring system with detailed user activity tracking, IP address monitoring, and device information"
				url="/login-history"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<UserManagementErrorBoundary>
				<LoginHistoryPageClient />
			</UserManagementErrorBoundary>
		</>
	);
};

export default LoginHistoryPage;
