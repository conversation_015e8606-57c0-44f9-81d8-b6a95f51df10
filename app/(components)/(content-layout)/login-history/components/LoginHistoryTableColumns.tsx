import { LoginHistoryRecord } from "@/shared/types/user-management-types";
import { SpkTableColumn } from "@/shared/UI/components";

/**
 * Login History Table Columns Configuration
 *
 * Column definitions for the login history table with all 11 specified columns:
 * 1. Username, 2. IP Address, 3. Location, 4. Network, 5. Version,
 * 6. Device ID, 7. Device Type, 8. Device Model, 9. Location Type,
 * 10. Sign-in Count, 11. Last Login Date
 */

// Format date utility
const formatDate = (dateString: string) => {
  if (!dateString) return "Never";
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return "Invalid Date";
  }
};

// Format location utility - combine city, region, countryName
const formatLocation = (data: { city?: string; region?: string; countryName?: string }) => {
  if (!data) return "Unknown";

  const parts = [data.city, data.region, data.countryName].filter(Boolean);
  return parts.length > 0 ? parts.join(", ") : "Unknown";
};

// Determine location type based on IP version
// const getLocationType = (version: string) => {
//   return version === "IPv4" ? "IPv4" : version === "IPv6" ? "IPv6" : "Unknown";
// };

export const getLoginHistoryTableColumns = (): SpkTableColumn[] => {
  return [
    {
      key: "User",
      title: "Username",
      sortable: true,
      width: "120px",
      render: (_value, record: LoginHistoryRecord) => (
        <div className="flex items-center gap-2">
          <span className="font-medium text-text-muted text-sm">
            {record?.User?.userName || "Unknown"}
          </span>
        </div>
      )
    },
    {
      key: "ip",
      title: "IP Address",
      width: "130px",
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted font-mono">
            {value || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "data",
      title: "Location",
      width: "180px",
      render: (value, _record: LoginHistoryRecord) => (
        <div className="text-sm">
          <span className="text-text-muted">
            {formatLocation(value)}
          </span>
        </div>
      )
    },
    {
      key: "network",
      title: "Network",
      width: "140px",
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted font-mono">
            {value || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "version",
      title: "Version",
      width: "80px",
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted">
            {value || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "deviceId",
      title: "Device ID",
      width: "150px",
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted font-mono truncate" title={value}>
            {value || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "deviceType",
      title: "Device Type",
      width: "100px",
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted capitalize">
            {value || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "deviceModel",
      title: "Device Model",
      width: "140px",
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted truncate" title={value}>
            {value || "N/A"}
          </span>
        </div>
      )
    },
    // {
    //   key: "version",
    //   title: "Location Type",
    //   width: "110px",
    //   render: (value) => (
    //     <div className="text-sm">
    //       <span className="text-text-muted">
    //         {getLocationType(value)}
    //       </span>
    //     </div>
    //   )
    // },
    {
      key: "signInCount",
      title: "Sign-in Count",
      width: "120px",
      sortable: true,
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted font-medium">
            {value || 0}
          </span>
        </div>
      )
    },
    {
      key: "lastLoginDate",
      title: "Last Login Date",
      width: "160px",
      sortable: true,
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted">
            {formatDate(value)}
          </span>
        </div>
      )
    }
  ];
};
