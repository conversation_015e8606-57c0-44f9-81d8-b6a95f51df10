// app/(components)/(content-layout)/bet-report/components/BetReportPageClient.tsx - Client-side component for bet report
"use client";

import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useBetReport } from "@/shared/hooks/business/useBetReport";
import { useBetSettlement } from "@/shared/hooks/business/useBetSettlement";
import { useExportCsv } from "@/shared/hooks/business/useExportCsv";
import { BetReportData, BetReportFilters, BetReportResponse } from "@/shared/types/report-types";
import { Fragment, useCallback, useMemo, useState } from "react";

// Import global components
import { BET_REPORT_FILTERS, DEFAULT_BET_REPORT_VISIBLE_FILTERS } from "@/shared/config/betReportFilters";
import {
	BetSettlementModal,
	GlobalDataTable,
	GlobalFilterSection,
	GlobalPageHeader
} from "@/shared/UI/components";
import { BetSettlementData } from "@/shared/UI/components";

// Import table columns
import { getBetReportTableColumns } from "@/shared/components/tables/BetReportTableColumns";

// Import error and loading components
import { SpkErrorMessage, ReportSectionSkeleton } from "@/shared/UI/components";

// Import toast notifications
import { useToast } from "@/shared/UI/components";
import ReportSummaryCards, { ReportSummaryCardData } from '@/shared/UI/cards/ReportSummaryCards';

interface BetReportPageClientProps {
	initialBetReportResponse?: BetReportResponse | null;
	initialFilters?: BetReportFilters;
}

/**
 * Client-side component that handles all interactive functionality for bet report
 * Separated from the server component to maintain SSR SEO benefits
 * Uses custom hook for business logic separation and global components
 *
 * Features:
 * - Accepts server-side rendered initial data for performance
 * - Graceful fallback to client-side fetching
 * - Optimized re-rendering for pagination changes
 */
export function BetReportPageClient({
	initialBetReportResponse = null,
	initialFilters
}: BetReportPageClientProps = {}) {
	// Settlement modal state
	const [settlementModal, setSettlementModal] = useState<{
		isOpen: boolean;
		betId: string;
		betData: BetReportData | null;
	}>({
		isOpen: false,
		betId: '',
		betData: null
	});

	// Toast notifications
	const { showToast } = useToast();

	// Use custom hook for all business logic with initial data
	const {
		filters,
		betReportResponse,
		isLoading,
		isError,
		error,
		isFetching,
		totalBets,
		totalBetAmount,
		totalWinAmount,
		ggr,
		handleFilterChange,
		handlePageChange,
		handleRefresh,
		isAuthenticated,
		hasHydrated
	} = useBetReport({
		initialBetReportResponse,
		initialFilters
	});

	// Export CSV functionality
	const { exportCsv, isExporting } = useExportCsv({
		module: 'bet_report',
		type: 'cashier_bet_report'
	});

	// Handle export CSV
	const handleExportCsv = useCallback(async () => {
		await exportCsv(filters);
	}, [exportCsv, filters]);

	// Settlement hook with success/error handling
	const { settleBet, isSettling, clearError } = useBetSettlement({
		onSuccess: (data) => {
			showToast({
				type: 'success',
				title: 'Settlement Successful',
				message: data.message || 'Bet settled successfully',
				duration: 5000
			});
			setSettlementModal({ isOpen: false, betId: '', betData: null });
			handleRefresh(); // Refresh the table data
		},
		onError: (error) => {
			showToast({
				type: 'error',
				title: 'Settlement Failed',
				message: error.message || 'Failed to settle bet',
				duration: 5000
			});
		}
	});

	// Handle settlement button click
	const handleSettleBet = useCallback((betId: string, betData: BetReportData) => {
		setSettlementModal({
			isOpen: true,
			betId,
			betData
		});
		clearError(); // Clear any previous errors
	}, [clearError]);

	// Handle settlement modal close
	const handleCloseSettlementModal = useCallback(() => {
		setSettlementModal({ isOpen: false, betId: '', betData: null });
		clearError();
	}, [clearError]);

	// Handle settlement form submission
	const handleSettlementSubmit = useCallback(async (settlementData: BetSettlementData) => {
		if (settlementModal.betId) {
			await settleBet(settlementModal.betId, settlementData);
		}
	}, [settleBet, settlementModal.betId]);

	// Memoize table columns for performance with settlement callback
	const columns = useMemo(() => getBetReportTableColumns({
		onSettleBet: handleSettleBet
	}), [handleSettleBet]);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ limit: itemsPerPage });
	}, [handleFilterChange]);

	// Handle page change with tracking
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	// Memoize summary cards data for performance
	const summaryCardsData: ReportSummaryCardData[] = useMemo(() => [
		{
			type: 'bet-amount',
			label: 'Total Bet Amount',
			value: totalBetAmount
			// Removed hard-coded currency - will use centralized currency system
		},
		{
			type: 'win-amount',
			label: 'Total Win Amount',
			value: totalWinAmount
			// Removed hard-coded currency - will use centralized currency system
		},
		{
			type: 'ggr',
			label: 'GGR',
			value: ggr
			// Removed hard-coded currency - will use centralized currency system
		}
	], [totalBetAmount, totalWinAmount, ggr]);

	// Show loading skeleton while authentication is being checked
	if (!hasHydrated) {
		return <ReportSectionSkeleton type="full-page" />;
	}

	// Redirect to login if not authenticated (handled in hook)
	if (!isAuthenticated) {
		return null;
	}

	// Show loading state only for initial load, not for subsequent fetches
	if (isLoading && !betReportResponse) {
		return <ReportSectionSkeleton type="full-page" />;
	}

	return (
		<Fragment>
			{/* Global Page Header */}
			<GlobalPageHeader
				title="Bet Report"
			/>

			{/* Main Content with Enhanced Design */}
			<div className={`grid grid-cols-12 gap-6 ${fadeInStyles.fadeIn}`}>
				{/* Global Filter Section */}
				<div className="xl:col-span-12 col-span-12">
					<GlobalFilterSection
						filters={filters}
						onFilterChange={handleFilterChange}
						isLoading={isLoading || isFetching}
						onExport={handleExportCsv}
						showExportButton={true}
						exportLabel={isExporting ? "Exporting..." : "Export CSV"}
						showTimeSelect={false}
						availableFilters={BET_REPORT_FILTERS}
						defaultVisibleFilters={DEFAULT_BET_REPORT_VISIBLE_FILTERS}
						title="Filters"
						showRefreshButton={true}
						onRefresh={handleRefresh}
						isRefreshing={isFetching}
					/>
				</div>

				{/* Summary Cards */}
				<div className="xl:col-span-12 col-span-12">
					<ReportSummaryCards
						cards={summaryCardsData}
						backgroundType="general"
						isLoading={isLoading && !betReportResponse} // Only show loading for initial load, not filter changes
						gridColumns={3}
						height="130px"
					/>
				</div>

				{/* Enhanced Bet Report Table Section */}
				<div className="xl:col-span-12 col-span-12 transform transition-all duration-500 ease-in-out delay-100 rounded-[16px] overflow-visible relative">
					{isError ? (
						<div className="bg-filter p-[1rem] rounded-md">
							<SpkErrorMessage
								message={error?.message || "Failed to load bet report"}
								onRetry={handleRefresh}
								variant="alert"
								size="md"
							/>
						</div>
					) : isLoading && !betReportResponse ? (
						<ReportSectionSkeleton type="table" rowCount={5} />
					) : (
						<div className="bg-filter p-[1rem] rounded-md">
							<GlobalDataTable
								columns={columns}
								data={betReportResponse?.data || []}
								isLoading={isFetching && !!betReportResponse} // Only show loading overlay for subsequent fetches
								showPagination={true}
								currentPage={filters.page}
								totalItems={totalBets}
								itemsPerPage={filters.limit}
								totalPages={betReportResponse?.totalPages}
								onPageChange={handlePageChangeWithTracking}
								onItemsPerPageChange={handleItemsPerPageChange}
								emptyText="No bets found. Try adjusting your search filters."
								className="bet-report-table"
								minHeight="400px"
							/>
						</div>
					)}
				</div>
			</div>

			{/* Bet Settlement Modal */}
			<BetSettlementModal
				isOpen={settlementModal.isOpen}
				onClose={handleCloseSettlementModal}
				onSubmit={handleSettlementSubmit}
				betId={settlementModal.betId}
				isLoading={isSettling}
			/>
		</Fragment>
	);
}
