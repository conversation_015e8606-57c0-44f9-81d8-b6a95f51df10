// app/(components)/(content-layout)/bet-report/page.tsx
import { DEFAULT_BET_REPORT_FILTERS } from '@/shared/types/report-types';
import { Metadata } from 'next';
import React from 'react';
import { BetReportPageClient } from './components/BetReportPageClient';

// Force dynamic rendering to avoid static generation issues with cookies
export const dynamic = 'force-dynamic';

// Generate metadata for the bet report page (server-side)
export const metadata: Metadata = {
  title: 'Bet Report | Admin Dashboard',
  description: 'View and analyze betting activities, win/loss reports, and gaming statistics',
  keywords: 'bet, report, gaming, statistics, win, loss, admin, dashboard',
  openGraph: {
    title: 'Bet Report | Admin Dashboard',
    description: 'View and analyze betting activities, win/loss reports, and gaming statistics',
    type: 'website',
  },
};

type Props = {};

/**
 * Dynamic bet report page component
 * Handles SEO metadata generation and delegates client-side logic to BetReportPageClient
 *
 * Features:
 * - Dynamic rendering to support authentication
 * - SEO optimization with structured data
 * - Client-side data fetching for authenticated users
 */
const BetReportPage: React.FC<Props> = () => {
  return (
    <BetReportPageClient
      initialBetReportResponse={null}
      initialFilters={DEFAULT_BET_REPORT_FILTERS}
    />
  );
};

export default BetReportPage;
