// app/(components)/(content-layout)/sportsbook/page.tsx - Server-side rendered sportsbook category selection
import React from "react";
import type { Metadata } from "next";
import { SportsbookPageClient } from "./components/SportsbookPageClient";

// Generate metadata for the sportsbook page (server-side)
export const metadata: Metadata = {
  title: "Sportsbook - Select Your Sport",
  description: "Choose your preferred sport category to access the TurboStars sportsbook with personalized betting options.",
  keywords: ["sportsbook", "sports betting", "TurboStars", "sports categories", "online betting"],
  robots: {
    index: true,
    follow: true,
  },
};

interface SportsbookProps {}

/**
 * Server-side rendered sportsbook category selection page
 * Handles SEO metadata generation and delegates client-side logic to SportsbookPageClient
 */
const Sportsbook: React.FC<SportsbookProps> = () => {
  return (
    <>
      {/* Client-side component handles all interactive functionality */}
      <SportsbookPageClient />
    </>
  );
};

export default Sportsbook;
