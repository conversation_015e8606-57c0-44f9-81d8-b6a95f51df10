// app/(components)/(content-layout)/dashboard/page.tsx - Server-side rendered dashboard with SEO optimization
import React, { Fragment } from "react";
import type { Metadata } from "next";
import Link from "next/link";
import { generateDashboardMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";

// Generate metadata for the dashboard page (server-side)
export const metadata: Metadata = generateDashboardMetadata();

// Revalidate dashboard every 5 minutes (300 seconds) for fresh data
export const revalidate = 300;

type Props = {};
const Dashboard: React.FC<Props> = () => {
	return (
		<Fragment>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Dashboard" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Xintra Dashboard"
				description="Comprehensive business dashboard with advanced analytics, user management, and performance tracking"
				url="/dashboard"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			<div className="flex items-center justify-between flex-wrap gap-2">
				<div>
					<ol className="breadcrumb mb-0">
						<li className="breadcrumb-item">
							<Link scroll={false} href="#!">
								Dashboards
							</Link>
						</li>
						<li className="breadcrumb-item active" aria-current="page">Sales</li>
					</ol>
					<h1 className="page-title font-medium text-lg mb-0">Dashboard</h1>
				</div>
			</div>
		</Fragment>
	);
};

export default Dashboard;
