// app/(components)/(content-layout)/financial-report/page.tsx
import { DEFAULT_FINANCIAL_REPORT_FILTERS } from '@/shared/types/report-types';
import { Metadata } from 'next';
import React from 'react';
import { FinancialReportPageClient } from './components/FinancialReportPageClient';

// Force dynamic rendering to avoid static generation issues with cookies
export const dynamic = 'force-dynamic';

// Generate metadata for the financial report page (server-side)
export const metadata: Metadata = {
  title: 'Financial Report | Admin Dashboard',
  description: 'View and analyze financial transactions, deposits, withdrawals, and financial activities',
  keywords: 'financial, report, transactions, deposits, withdrawals, admin, dashboard',
  openGraph: {
    title: 'Financial Report | Admin Dashboard',
    description: 'View and analyze financial transactions, deposits, withdrawals, and financial activities',
    type: 'website',
  },
};

type Props = {};

/**
 * Dynamic financial report page component
 * Handles SEO metadata generation and delegates client-side logic to FinancialReportPageClient
 *
 * Features:
 * - Dynamic rendering to support authentication
 * - SEO optimization with structured data
 * - Client-side data fetching for authenticated users
 */
const FinancialReportPage: React.FC<Props> = () => {
  return (
    <FinancialReportPageClient
      initialFinancialReportResponse={null}
      initialFilters={DEFAULT_FINANCIAL_REPORT_FILTERS}
    />
  );
};

export default FinancialReportPage;
