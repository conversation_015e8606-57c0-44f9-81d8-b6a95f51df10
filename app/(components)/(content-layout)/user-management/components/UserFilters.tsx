import React from "react";
import { UserListFilters } from "@/shared/types/user-management-types";
import { SpkAdvancedFilters } from "@/shared/UI/components";
import { USER_MANAGEMENT_FILTERS, DEFAULT_USER_VISIBLE_FILTERS } from "@/shared/config/userManagementFilters";

interface UserFiltersProps {
	filters: UserListFilters;
	onFilterChange: (_filters: Partial<UserListFilters>) => void;
	isLoading: boolean;
}

const UserFilters: React.FC<UserFiltersProps> = React.memo(({
	filters: _filters,
	onFilterChange,
	isLoading
}) => {
	return (
		<SpkAdvancedFilters
			filters={_filters}
			onFilterChange={onFilterChange}
			isLoading={isLoading}
			availableFilters={USER_MANAGEMENT_FILTERS}
			defaultVisibleFilters={DEFAULT_USER_VISIBLE_FILTERS}
			title="User Filters"
			description="Filter and search users with advanced criteria"
		/>
	);
});

UserFilters.displayName = 'UserFilters';

export default UserFilters;
