import React from "react";
import { SpkPagination } from "@/shared/UI/components";

interface UserPaginationProps {
	currentPage: number;
	totalItems: number;
	itemsPerPage: number;
	onPageChange: (_page: number) => void;
	totalPages?: number;
}

const UserPagination: React.FC<UserPaginationProps> = ({
	currentPage,
	totalItems,
	itemsPerPage,
	onPageChange,
	totalPages
}) => {

	return (
		<SpkPagination
			currentPage={currentPage}
			totalItems={totalItems}
			itemsPerPage={itemsPerPage}
			onPageChange={onPageChange}
			totalPages={totalPages}
			style="style-5"
			showInfo={true}
			showGoToPage={true}
			className="border-t border-defaultborder pt-4 mt-4"
		/>
	);
};

export default UserPagination;
