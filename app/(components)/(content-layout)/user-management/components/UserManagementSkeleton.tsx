import React from "react";
import { TableSkeleton, PageSkeleton } from "@/shared/UI/components";

interface UserManagementSkeletonProps {
	showHeader?: boolean;
	showFilters?: boolean;
	showTable?: boolean;
	showPagination?: boolean;
	tableRows?: number;
}

const UserManagementSkeleton: React.FC<UserManagementSkeletonProps> = ({
	showHeader = true,
	showFilters = true,
	showTable = true,
	showPagination = true,
	tableRows = 5
}) => {
	return (
		<div className="space-y-6">
			{/* Page Header Skeleton */}
			{showHeader && (
				<PageSkeleton showBreadcrumb={true} showContent={false} />
			)}

			{/* Table with Filters and Pagination Skeleton */}
			{(showFilters || showTable || showPagination) && (
				<TableSkeleton 
					rows={tableRows}
					columns={6}
					showHeader={showTable}
					showPagination={showPagination}
					showFilters={showFilters}
				/>
			)}
		</div>
	);
};

export default UserManagementSkeleton;
