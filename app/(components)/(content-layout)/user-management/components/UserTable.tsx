import React from "react";
import { UserData, UserListFilters } from "@/shared/types/user-management-types";
import { SpkTable, SpkTableColumn } from "@/shared/UI/components";
import { getUserTableColumns } from "./UserTableColumns";
import { useModalNavigation } from "@/shared/hooks/ui/useModalNavigation";

/**
 * UserTable Component
 *
 * Pure presentational component for displaying user data in a table format.
 * Uses the standardized SpkTable component with user-specific column configurations.
 *
 * Features:
 * - Consistent styling with dark theme support
 * - Click-to-navigate functionality via column render functions
 * - Loading and empty states
 * - Responsive design
 * - Follows our component architecture patterns
 *
 * This component is now a pure configuration component that:
 * - Uses SpkTable internally for all table functionality
 * - Delegates column definitions to UserTableColumns
 * - Maintains consistent styling across the application
 * - Separates presentation from business logic
 */
interface UserTableProps {
	users: UserData[];
	isLoading: boolean;
	filters: UserListFilters;
}

const UserTable: React.FC<UserTableProps> = ({ users, isLoading, filters: _filters }) => {
	// Ensure users is always an array to prevent map errors
	const safeUsers = Array.isArray(users) ? users : [];

	// Get modal navigation functions
	const { openEditUserModal, openDeactivateUserModal, openActivateUserModal } = useModalNavigation();

	// Get column definitions from the separate columns file with modal navigation
	const columns: SpkTableColumn[] = getUserTableColumns({
		openEditUserModal,
		openDeactivateUserModal,
		openActivateUserModal
	});

	return (
		<div style={{ minHeight: "400px" }} className="w-full">
			<div className="bg-section rounded-lg overflow-visible relative" >
				{/* Remove overflow-x-auto to prevent table scrolling */}
				<div className="w-full">
					<SpkTable
						columns={columns}
						data={safeUsers}
						loading={isLoading}
						hover={true}
						responsive={false} // We handle responsive manually
						emptyText="No users found. Try adjusting your search filters."
						className="user-management-table w-full"
						tableClass="table-fixed w-full min-w-[1100px] bg-section"
						headerClass="bg-surface border-b border-border-secondary"
						rowClass="h-16 border-b border-border-secondary hover:bg-surface transition-colors duration-200"
						size="md"
					/>
				</div>
			</div>
		</div>
	);
};

export default React.memo(UserTable);
