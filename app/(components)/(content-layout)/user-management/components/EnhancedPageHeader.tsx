import React from "react";
import Link from "next/link";
import { SpkBadge } from "@/shared/UI/components";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

interface BreadcrumbItem {
	label: string;
	href?: string;
	onClick?: () => void;
	active?: boolean;
}

interface QuickStat {
	label: string;
	value: string;
	variant?: "success" | "warning" | "info" | "primary";
}

interface ActionButton {
	label: string;
	icon: string;
	onClick?: () => void;
	href?: string;
	variant?: string;
	disabled?: boolean;
	loading?: boolean;
	hideOnMobile?: boolean;
}

interface EnhancedPageHeaderProps {
	title: string;
	description?: string;
	icon: string;
	breadcrumbs: BreadcrumbItem[];
	quickStats?: QuickStat[];
	actionButtons?: ActionButton[];
	className?: string;
}

const EnhancedPageHeader: React.FC<EnhancedPageHeaderProps> = ({
	title,
	description,
	icon,
	breadcrumbs,
	quickStats = [],
	actionButtons = [],
	className = ""
}) => {
	return (
		<div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6 shadow-sm ${className}`}>
			<div className="flex items-center justify-between flex-wrap gap-4">
				<div className="flex-1">
					{/* Enhanced Breadcrumb */}
					<nav className="flex mb-4" aria-label="Breadcrumb">
						<ol className="inline-flex items-center space-x-1 md:space-x-3">
							{breadcrumbs.map((item, index) => (
								<li key={index} className={index === 0 ? "inline-flex items-center" : "flex"}>
									{index > 0 && (
										<div className="flex items-center">
											<i className="ri-arrow-right-s-line text-gray-400"></i>
										</div>
									)}
									{item.active ? (
										<span className="ml-1 text-sm font-medium text-gray-500 dark:text-gray-400 md:ml-2">
											{item.label}
										</span>
									) : (
										<button
											onClick={item.onClick}
											className={`${index === 0 ? "inline-flex items-center" : "ml-1 md:ml-2"} text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-400 dark:hover:text-white transition-colors`}
										>
											{index === 0 && <i className="ri-home-line me-2"></i>}
											{item.label}
										</button>
									)}
								</li>
							))}
						</ol>
					</nav>

					{/* Page Title */}
					<div className="flex items-center gap-3">
						<div className="p-3 bg-primary/10 rounded-lg">
							<i className={`${icon} text-primary text-xl`}></i>
						</div>
						<div>
							<h1 className="text-2xl font-bold text-gray-800 dark:text-white">
								{title}
							</h1>
							{description && (
								<p className="text-gray-600 dark:text-gray-400 text-sm">
									{description}
								</p>
							)}
						</div>
					</div>
				</div>

				{/* Action Buttons and Quick Stats */}
				<div className="flex items-center gap-3">
					{/* Quick Stats */}
					{quickStats.length > 0 && (
						<div className="hidden md:flex items-center gap-4 px-4 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
							{quickStats.map((stat, index) => (
								<React.Fragment key={index}>
									{index > 0 && <div className="w-px h-8 bg-gray-300 dark:bg-gray-600"></div>}
									<div className="text-center">
										<p className="text-xs text-gray-500 dark:text-gray-400">{stat.label}</p>
										<div className="flex items-center justify-center">
											{stat.variant ? (
												<SpkBadge variant={stat.variant} className="text-xs">
													{stat.value}
												</SpkBadge>
											) : (
												<p className="text-sm font-semibold text-gray-800 dark:text-white">
													{stat.value}
												</p>
											)}
										</div>
									</div>
								</React.Fragment>
							))}
						</div>
					)}

					{/* Action Buttons */}
					{actionButtons.length > 0 && (
						<div className="flex gap-2">
							{actionButtons.map((button, index) => (
								button.href ? (
									<Link
										key={index}
										href={button.href}
										className={`ti-btn ${button.variant || "ti-btn-primary"} ${button.hideOnMobile ? "hidden sm:inline-flex" : ""} inline-flex items-center gap-2 ${button.disabled ? "opacity-50 pointer-events-none" : ""}`}
										title={button.label}
									>
										<i className={`${button.icon} ${button.loading ? "animate-spin" : ""}`}></i>
										<span className={button.hideOnMobile ? "hidden sm:inline" : ""}>
											{button.label}
										</span>
									</Link>
								) : (
									<SpkButton
										key={index}
										type="button"
										customClass={`ti-btn ${button.variant || "ti-btn-primary"} ${button.hideOnMobile ? "hidden sm:inline-flex" : ""}`}
										disabled={button.disabled}
										onclickfunc={button.onClick}
										title={button.label}
									>
										<i className={`${button.icon} me-2 ${button.loading ? "animate-spin" : ""}`}></i>
										<span className={button.hideOnMobile ? "hidden sm:inline" : ""}>
											{button.label}
										</span>
									</SpkButton>
								)
							))}
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default EnhancedPageHeader;
