// app/(components)/(content-layout)/user-management/page.tsx - Server-side rendered user management with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateUserManagementMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { UserManagementPageClient } from "./components/UserManagementPageClient";
import { getInitialUserData } from "@/shared/utils/serverSideUserFetch";
import { UserManagementErrorBoundary } from "@/shared/components/error-boundaries/SSRErrorBoundary";

// Force dynamic rendering for this page since it accesses cookies for authentication
export const dynamic = "force-dynamic";

// Generate metadata for the user management page (server-side)
export const metadata: Metadata = generateUserManagementMetadata("list");

type Props = {};

/**
 * Server-side rendered user management page component
 * Handles SEO metadata generation, server-side data fetching, and delegates client-side logic to UserManagementPageClient
 *
 * Features:
 * - Server-side data fetching for initial 10 users
 * - SEO optimization with structured data
 * - Graceful fallback to client-side fetching if server-side fails
 */
const UserManagementPage: React.FC<Props> = async () => {
	// Fetch initial user data on the server
	const { userListResponse, initialFilters } = await getInitialUserData();

	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="User Management System"
				description="Comprehensive user management system with advanced filtering, user details, and analytics capabilities"
				url="/user-management"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<UserManagementErrorBoundary>
				<UserManagementPageClient
					initialUserListResponse={userListResponse}
					initialFilters={initialFilters}
				/>
			</UserManagementErrorBoundary>
		</>
	);
};

export default UserManagementPage;
