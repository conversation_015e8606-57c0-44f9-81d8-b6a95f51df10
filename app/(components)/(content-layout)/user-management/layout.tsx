// app/(components)/(content-layout)/user-management/layout.tsx - Server-side layout for user management with SEO
import React from "react";
import type { Metadata } from "next";
import { generateUserManagementMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

// Generate metadata for user management pages (server-side)
export const metadata: Metadata = generateUserManagementMetadata("list");

interface UserManagementLayoutProps {
	children: React.ReactNode;
}

/**
 * Server-side layout component for user management section
 * Provides SEO optimization and structured data for all user management pages
 *
 * Note: Modal functionality is now handled by GlobalUserManagementModal
 * which is rendered globally and controlled by URL parameters.
 */
export default function UserManagementLayout({ children }: UserManagementLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management", url: "/user-management" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="User Management System"
				description="Comprehensive user management dashboard with advanced filtering, search, and analytics capabilities"
				url="/user-management"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{children}
		</>
	);
}
