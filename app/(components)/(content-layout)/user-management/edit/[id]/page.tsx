// app/(components)/(content-layout)/user-management/edit/[id]/page.tsx
import React from 'react';
import EditUserPageClient from './EditUserPageClient';

interface EditUserPageProps {
  params: Promise<{
    id: string;
  }>;
}

/**
 * Edit User Redirect Route
 *
 * This route handles direct navigation to /user-management/edit/[id]
 * and redirects to the user management page with modal parameters.
 *
 * This maintains backward compatibility for bookmarks and direct links
 * while using the new global modal system.
 */
export default async function EditUserPage({ params }: EditUserPageProps) {
  const { id } = await params;
  return <EditUserPageClient userId={id} />;
}
