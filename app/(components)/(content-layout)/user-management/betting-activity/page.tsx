// app/(components)/(content-layout)/user-management/betting-activity/page.tsx - Server-side rendered betting activity page with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateUserManagementMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { BettingActivityPageClient } from "./components/BettingActivityPageClient";

/**
 * Generate metadata for betting activity page
 */
export const metadata: Metadata = generateUserManagementMetadata("list", {
	title: "Betting Activity - User Management",
	description: "Comprehensive betting activity monitoring and analytics across all users. View detailed transaction history, betting patterns, and platform-wide gambling activity.",
	path: "/user-management/betting-activity",
	keywords: ["betting activity", "transaction monitoring", "gambling analytics", "user activity", "platform statistics"]
});

/**
 * Server-side rendered betting activity page component
 * Handles SEO metadata generation and delegates client-side logic to BettingActivityPageClient
 */
const BettingActivityPage: React.FC = () => {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management", url: "/user-management" },
					{ name: "Betting Activity" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Betting Activity Monitor"
				description="Comprehensive betting activity monitoring system with advanced analytics and real-time transaction tracking"
				url="/user-management/betting-activity"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component */}
			<BettingActivityPageClient />
		</>
	);
};

export default BettingActivityPage;
