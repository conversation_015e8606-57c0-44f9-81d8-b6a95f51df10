import React from "react";
import { CardSkeleton, TableSkeleton } from "@/shared/UI/components";

interface FinancialReportSkeletonProps {
	showUserHeader?: boolean;
	showSummary?: boolean;
	showTransactions?: boolean;
}

const FinancialReportSkeleton: React.FC<FinancialReportSkeletonProps> = ({
	showUserHeader = true,
	showSummary = true,
	showTransactions = true
}) => {
	return (
		<div className="space-y-6">
			{/* User Context Header Skeleton */}
			{showUserHeader && (
				<CardSkeleton
					layout="single"
					showAvatar={true}
					showActions={true}
					showStats={true}
				/>
			)}

			{/* Financial Summary Skeleton */}
			{showSummary && (
				<CardSkeleton
					layout="grid"
					count={4}
					showAvatar={false}
					showActions={false}
					showStats={false}
				/>
			)}

			{/* Transaction History Skeleton */}
			{showTransactions && (
				<TableSkeleton
					rows={5}
					columns={4}
					showHeader={true}
					showPagination={false}
					showFilters={true}
				/>
			)}
		</div>
	);
};

export default FinancialReportSkeleton;
