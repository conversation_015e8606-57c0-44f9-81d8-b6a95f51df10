// app/(components)/(content-layout)/user-management/details/[id]/financial-report/components/FinancialReportPageClient.tsx - Client-side component for financial report
"use client";

import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";
import { SpkBadge } from "@/shared/UI/components";
import { useFinancialReportQuery, useUserDetailsQuery } from "@/shared/query";
import { useAuthStore } from "@/shared/stores/authStore";
import { FinancialReportFilters } from "@/shared/types/report-types";
import { FinancialSummaryData, FinancialTransactionData } from "@/shared/types/user-management-types";
import { buildImageUrl, isExternalImage } from "@/shared/utils/imageOptimization";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Fragment, useEffect, useState } from "react";

// Types

// Components
import { SpkErrorMessage } from "@/shared/UI/components";
import FinancialReportSkeleton from "./FinancialReportSkeleton";
import FinancialSummary from "./FinancialSummary";
import TransactionHistory from "./TransactionHistory";

interface Props {
	userId: string;
}

/**
 * Client-side component that handles all interactive functionality for financial report
 * Separated from the server component to maintain SSR SEO benefits
 */
export function FinancialReportPageClient({ userId }: Props) {
	const router = useRouter();
	const { isAuthenticated, _hasHydrated } = useAuthStore();

	// State for filters (currently not used but kept for future enhancements)
	const [filters] = useState<Partial<FinancialReportFilters>>({});

	// Fetch user details for context
	const {
		data: userDetailsResponse,
		isLoading: isUserLoading,
		isError: isUserError,
		error: userError,
	} = useUserDetailsQuery(userId);

	// Fetch financial report data
	const {
		data: financialReportResponse,
		isLoading: isReportLoading,
		isError: isReportError,
		error: reportError,
		refetch: refetchReport,
		isFetching: isReportFetching
	} = useFinancialReportQuery(filters);

	// Redirect if not authenticated
	useEffect(() => {
		if (_hasHydrated && !isAuthenticated) {
			router.replace("/authentication/sign-in/");
		}
	}, [_hasHydrated, isAuthenticated, router]);

	// Don't render anything until hydration is complete
	if (!_hasHydrated) {
		return <FinancialReportSkeleton />;
	}

	// Don't render if not authenticated
	if (!isAuthenticated) {
		return null;
	}

	// Show loading state
	if (isUserLoading || isReportLoading) {
		return <FinancialReportSkeleton />;
	}

	// Show error state
	if (isUserError || isReportError) {
		return (
			<Fragment>
				<div className="flex items-center justify-between flex-wrap gap-2 mb-6">
					<div>
						<ol className="breadcrumb mb-0">
							<li className="breadcrumb-item">
								<a href="/user-management">User Management</a>
							</li>
							<li className="breadcrumb-item">
								<a href={`/user-management/details/${userId}`}>User Details</a>
							</li>
							<li className="breadcrumb-item active" aria-current="page">Financial Report</li>
						</ol>
						<h1 className="page-title font-medium text-lg mb-0">Financial Report</h1>
					</div>
					<div className="flex gap-2">
						<SpkButton
							variant="outline-primary"
							onclickfunc={() => router.push(`/user-management/details/${userId}`)}
						>
							<i className="ri-arrow-left-line me-1"></i>
							Back to User Details
						</SpkButton>
					</div>
				</div>

				<SpkErrorMessage
					message={userError?.message || reportError?.message || "Unable to load financial report data"}
					onRetry={() => {
						if (isUserError) {
							// Retry user details query if needed
						}
						if (isReportError) {
							refetchReport();
						}
					}}
					variant="alert"
					size="md"
				/>
			</Fragment>
		);
	}

	const user = userDetailsResponse?.data;
	const financialData = financialReportResponse?.data || [];
	const financialResponse = financialReportResponse;

	if (!user) {
		return (
			<SpkErrorMessage
				message="The requested user could not be found"
				onRetry={() => router.push("/user-management")}
				variant="box"
				size="md"
				title="User Not Found"
			/>
		);
	}

	// Create summary data from the financial transactions
	const summaryData: FinancialSummaryData = {
		totalDeposits: financialResponse?.totalDeposits || 0,
		totalWithdrawals: financialResponse?.totalWithdrawals || 0,
		totalCancellations: 0, // Not available in current API response
		netAmount: (financialResponse?.totalDeposits || 0) - (financialResponse?.totalWithdrawals || 0),
		transactionCount: financialResponse?.count || 0
		// Removed hard-coded currency - will use centralized currency system
	};

	// Transform financial transactions to match expected interface
	const transformedTransactions: FinancialTransactionData[] = financialData.map(transaction => ({
		id: transaction.id || transaction.transactionId,
		type: transaction.actionType as 'deposit' | 'withdrawal' | 'cancellation' | 'bonus' | 'commission',
		amount: transaction.amount,
		currency: typeof transaction.currency === 'string' ? transaction.currency : "LKR",
		status: transaction.status as 'completed' | 'pending' | 'cancelled' | 'failed',
		description: transaction.description,
		transactionId: transaction.transactionId,
		createdAt: transaction.createdAt,
		paymentMethod: transaction.gameProvider || undefined,
		reference: transaction.utrNumber || undefined
	}));

	return (
		<Fragment>
			{/* Page Header */}
			<div className="flex items-center justify-between flex-wrap gap-2 mb-6">
				<div>
					<ol className="breadcrumb mb-0">
						<li className="breadcrumb-item">
							<a href="/user-management">User Management</a>
						</li>
						<li className="breadcrumb-item">
							<a href={`/user-management/details/${userId}`}>User Details</a>
						</li>
						<li className="breadcrumb-item active" aria-current="page">Financial Report</li>
					</ol>
					<h1 className="page-title font-medium text-lg mb-0">Financial Report</h1>
				</div>
				<div className="flex gap-2">
					<SpkButton
						variant="outline-primary"
						onclickfunc={() => router.push(`/user-management/details/${userId}`)}
					>
						<i className="ri-arrow-left-line me-1"></i>
						Back to User Details
					</SpkButton>
				</div>
			</div>

			{/* User Context */}
			<div className="box mb-6">
				<div className="box-body">
					<div className="flex items-center gap-4">
						<div className="avatar avatar-lg">
							{(() => {
								const avatarUrl = buildImageUrl(user.avatarImage);
								const isDefaultFallback = avatarUrl === '/images/profile.png';

								return !isDefaultFallback ? (
									<Image
										src={avatarUrl}
										alt={`${user.firstName} ${user.lastName}`}
										width={48}
										height={48}
										className="rounded-full"
										unoptimized={isExternalImage(avatarUrl)}
										onError={() => {
											// Handle image load error gracefully
										}}
									/>
								) : (
									<div className="avatar-initial bg-primary text-white rounded-full flex items-center justify-center">
										<i className="ri-user-line text-xl"></i>
									</div>
								);
							})()}
						</div>
						<div className="flex-1">
							<h3 className="text-lg font-semibold mb-1">
								{user.firstName} {user.lastName}
							</h3>
							<p className="text-textmuted mb-1">@{user.userName}</p>
							<div className="flex items-center gap-2">
								<SpkBadge
									variant={user.active ? "success" : "danger"}
								>
									{user.active ? "Active" : "Inactive"}
								</SpkBadge>
								<SpkBadge
									variant="info"
								>
									ID: {user.id}
								</SpkBadge>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Financial Summary */}
			<FinancialSummary
				summaryData={summaryData}
				isLoading={isReportFetching}
			/>

			{/* Transaction History */}
			<TransactionHistory
				transactions={transformedTransactions}
				currency="LKR"
				isLoading={isReportFetching}
			/>
		</Fragment>
	);
}
