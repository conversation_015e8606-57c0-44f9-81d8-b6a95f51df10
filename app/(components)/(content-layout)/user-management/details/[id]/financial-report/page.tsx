// app/(components)/(content-layout)/user-management/details/[id]/financial-report/page.tsx - Server-side rendered financial report with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateUserManagementMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { FinancialReportPageClient } from "./components/FinancialReportPageClient";

interface Props {
	params: Promise<{ id: string }>;
}

// Generate metadata for the financial report page (server-side)
export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { id } = await params;
	// In a real application, you might fetch user data here for dynamic metadata
	// For now, we'll use the base metadata with dynamic user ID
	return generateUserManagementMetadata("details", {
		title: `Financial Report - User ${id}`,
		description: `Comprehensive financial report and transaction history for user ${id} including account balance, transaction analytics, and financial activity tracking.`,
		path: `/user-management/details/${id}/financial-report`,
	});
}

/**
 * Server-side rendered financial report page component
 * Handles SEO metadata generation and delegates client-side logic to FinancialReportPageClient
 */
const FinancialReportPage: React.FC<Props> = async ({ params }) => {
	const { id } = await params;
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management", url: "/user-management" },
					{ name: "User Details", url: `/user-management/details/${id}` },
					{ name: "Financial Report" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Financial Report System"
				description="Comprehensive financial reporting and analytics system with transaction history and account balance tracking"
				url={`/user-management/details/${id}/financial-report`}
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<FinancialReportPageClient userId={id} />
		</>
	);
};

export default FinancialReportPage;
