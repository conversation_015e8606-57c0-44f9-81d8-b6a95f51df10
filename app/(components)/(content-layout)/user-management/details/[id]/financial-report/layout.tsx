// app/(components)/(content-layout)/user-management/details/[id]/financial-report/layout.tsx - Server-side layout for financial-report with SEO
import React from "react";
import type { Metadata } from "next";
import { generateFinancialReportMetadata, createCanonicalUrl } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	ArticleStructuredData
} from "@/shared/seo/components/StructuredData";

interface FinancialReportLayoutProps {
	children: React.ReactNode;
	params: Promise<{ id: string }>;
}

/**
 * Generate dynamic metadata for financial-report page
 */
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
	const { id: userId } = await params;

	// In a real application, you would fetch user data here
	// const user = await fetchUserById(userId);

	// Generate metadata with user ID for now
	return generateFinancialReportMetadata(userId, `User ${userId}`);
}

/**
 * Server-side layout component for financial-report page
 * Provides SEO optimization and structured data for financial-report pages
 */
export default async function FinancialReportLayout({ children, params }: FinancialReportLayoutProps) {
	const { id: userId } = await params;

	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management", url: "/user-management" },
					{ name: `User ${userId}`, url: `/user-management/details/${userId}` },
					{ name: "Financial Report" }
				]}
			/>
			<ArticleStructuredData
				headline={`Financial Report - User ${userId}`}
				description={`Comprehensive financial report and analytics for user ${userId} including transaction history and balance tracking`}
				url={createCanonicalUrl(`/user-management/details/${userId}/financial-report`)}
				author={{
					name: "Xintra System",
					url: "https://xintra.com"
				}}
				publisher={{
					name: "Xintra",
					logo: "https://xintra.com/assets/images/logo.png"
				}}
			/>

			{children}
		</>
	);
}
