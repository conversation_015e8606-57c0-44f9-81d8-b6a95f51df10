// app/(components)/(content-layout)/user-management/details/[id]/page.tsx - Server-side rendered user details with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateUserManagementMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { UserDetailsPageClient } from "./components/UserDetailsPageClient";

interface Props {
	params: Promise<{ id: string }>;
}

// Generate metadata for the user details page (server-side)
export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { id } = await params;
	// In a real application, you might fetch user data here for dynamic metadata
	// For now, we'll use the base metadata with dynamic user ID
	return generateUserManagementMetadata("details", {
		title: `User Details - ${id}`,
		description: `Detailed user profile and analytics for user ${id} including account information, activity history, and financial data.`,
		path: `/user-management/details/${id}`,
	});
}

/**
 * Server-side rendered user details page component
 * Handles SEO metadata generation and delegates client-side logic to UserDetailsPageClient
 */
const UserDetailsPage: React.FC<Props> = async ({ params }) => {
	const { id } = await params;
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management", url: "/user-management" },
					{ name: `User Details - ${id}` }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="User Details Management"
				description="Comprehensive user profile management with detailed analytics, account information, and activity tracking"
				url={`/user-management/details/${id}`}
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<UserDetailsPageClient userId={id} />
		</>
	);
};

export default UserDetailsPage;
