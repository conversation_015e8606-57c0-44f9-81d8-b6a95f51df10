// app/(components)/(content-layout)/user-management/details/[id]/components/LoginHistoryTabContent.tsx
import React, { Fragment } from 'react';
import { SpkErrorMessage } from "@/shared/UI/components";

interface LoginHistoryTabContentProps {
	loginHistoryResponse: any;
	isLoginHistoryLoading: boolean;
	isLoginHistoryError: boolean;
	loginHistoryError: any;
	refetchLoginHistory: () => void;
}

const LoginHistoryTabContent: React.FC<LoginHistoryTabContentProps> = ({
	loginHistoryResponse,
	isLoginHistoryLoading,
	isLoginHistoryError,
	loginHistoryError,
	refetchLoginHistory
}) => {
	return (
		<div className="space-y-6">
			{isLoginHistoryError ? (
				<SpkErrorMessage
					message={loginHistoryError?.message || "Failed to load login history"}
					onRetry={refetchLoginHistory}
					variant="alert"
					size="md"
				/>
			) : (
				<Fragment>
					{/* Login History Summary */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="bg-section p-4 rounded-lg">
							<div className="text-sm text-gray-400">Total Logins</div>
							<div className="text-2xl font-bold text-white">
								{loginHistoryResponse?.count?.toLocaleString() || '0'}
							</div>
						</div>
						<div className="bg-section p-4 rounded-lg">
							<div className="text-sm text-gray-400">Last Login</div>
							<div className="text-lg font-semibold text-white">
								{loginHistoryResponse?.data?.[0]?.lastLoginDate ?
									new Date(loginHistoryResponse.data[0].lastLoginDate).toLocaleDateString() : 'N/A'}
							</div>
						</div>
					</div>

					{/* Login History Table */}
					<div className="bg-section rounded-lg p-6">
						<div className="flex items-center justify-between mb-4">
							<h3 className="text-lg font-semibold text-white font-rubik">
								Login History
							</h3>
							<div className="text-sm text-gray-400">
								{loginHistoryResponse?.data?.length || 0} records
							</div>
						</div>

						{isLoginHistoryLoading ? (
							<div className="text-center py-8">
								<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
								<p className="text-gray-400 mt-2">Loading login history...</p>
							</div>
						) : loginHistoryResponse?.data?.length ? (
							<div className="overflow-x-auto">
								<table className="w-full text-sm">
									<thead>
										<tr className="border-b border-gray-600">
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Date & Time</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">IP Address</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Location</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Device</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Sign-in Count</th>
										</tr>
									</thead>
									<tbody>
										{loginHistoryResponse.data.slice(0, 10).map((login: any, index: number) => (
											<tr key={login.id || index} className="border-b border-gray-700 hover:bg-gray-700/20">
												<td className="py-3 px-2 text-white">
													{login.lastLoginDate ? new Date(login.lastLoginDate).toLocaleString() : 'N/A'}
												</td>
												<td className="py-3 px-2 text-gray-300 font-mono text-xs">
													{login.ip || 'N/A'}
												</td>
												<td className="py-3 px-2 text-gray-300">
													{login.data ? `${login.data.city}, ${login.data.countryName}` : 'N/A'}
												</td>
												<td className="py-3 px-2 text-gray-300">
													<div>
														<div className="text-white">{login.deviceType || 'Unknown'}</div>
														<div className="text-xs text-gray-400">{login.deviceModel || 'N/A'}</div>
													</div>
												</td>
												<td className="py-3 px-2 text-white">
													{login.signInCount || 0}
												</td>
											</tr>
										))}
									</tbody>
								</table>
							</div>
						) : (
							<div className="text-center py-8">
								<i className="ri-history-line text-4xl text-gray-400 mb-2"></i>
								<p className="text-gray-400">No login history found</p>
							</div>
						)}
					</div>
				</Fragment>
			)}
		</div>
	);
};

export default LoginHistoryTabContent;
