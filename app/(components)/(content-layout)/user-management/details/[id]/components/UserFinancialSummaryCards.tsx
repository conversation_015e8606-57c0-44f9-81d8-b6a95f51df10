"use client";

import React, { useMemo } from "react";
import { FinancialSummaryCard } from "@/shared/UI/user-management/FinancialSummaryCard";
import { useUserFinancialReportContext } from "../contexts/UserFinancialReportContext";

/**
 * Financial summary cards component that displays totals from the API
 * Uses shared context to stay synchronized with the financial report filters
 * 
 * Features:
 * - Displays Total Deposit, Total Withdraw, and Total Bets
 * - Automatically updates when filters change in the financial report
 * - Uses the totals API response for accurate filtered data
 */
export const UserFinancialSummaryCards: React.FC = () => {
  // Use the shared financial report context
  const { financialReportResponse } = useUserFinancialReportContext();

  const summaryCardsData = useMemo(() => [
    {
      id: 'total-deposit',
      svgName: 'balance' as const,
      title: 'Total Deposit',
      value: financialReportResponse?.totals?.totalDeposit || 0,
    },
    {
      id: 'total-withdraw',
      svgName: 'deposite' as const,
      title: 'Total Withdraw',
      value: financialReportResponse?.totals?.totalWithdraw || 0,
    },
    {
      id: 'total-bets',
      svgName: 'withdraw' as const,
      title: 'Total Bets',
      value: financialReportResponse?.totals?.totalBet || 0,
      currency: ''
    }
  ], [financialReportResponse?.totals]);

  return (
    <div className="flex-1 lg:flex-[0.25] flex flex-col gap-5">
      {summaryCardsData.map((cardData) => (
        <FinancialSummaryCard
          key={cardData.id}
          svgName={cardData.svgName}
          title={cardData.title}
          value={cardData.value}
          {...(cardData.currency !== undefined && { currency: cardData.currency })}
        />
      ))}
    </div>
  );
};
