// app/(components)/(content-layout)/user-management/details/[id]/components/UserAccountSummaryContent.tsx
"use client";

import { useBetReport } from "@/shared/hooks/business/useBetReport";
import React, { useCallback, useMemo } from "react";

// Import global components
import {
	GlobalDataTable,
	GlobalFilterSection,
	SpkErrorMessage
} from "@/shared/UI/components";
import { BET_REPORT_FILTERS, DEFAULT_BET_REPORT_VISIBLE_FILTERS } from "@/shared/config/betReportFilters";

// Import table columns
import { getBetReportTableColumns } from "@/shared/components/tables/BetReportTableColumns";

interface UserAccountSummaryContentProps {
	userId: string;
}

/**
 * User-specific account summary content component
 * Reuses the same GlobalFilterSection and GlobalDataTable components from the main bet report page
 * but with data pre-filtered for the specific user
 * 
 * Features:
 * - Identical UI styling and functionality to main bet report page
 * - User-specific data filtering via playerId parameter
 * - Full filter and pagination functionality
 * - Maintains exact appearance and behavior of global components
 */
export const UserAccountSummaryContent: React.FC<UserAccountSummaryContentProps> = ({ userId }) => {
	// Use the bet report hook with user-specific filtering
	const {
		filters,
		betReportResponse,
		isLoading,
		isError,
		error,
		isFetching,
		handleFilterChange,
		handlePageChange,
		handleRefresh
	} = useBetReport({
		initialFilters: {
			page: 1,
			limit: 10,
			playerId: userId, // Pre-filter for this specific user
			startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 30 days ago
			endDate: new Date().toISOString().slice(0, 16) // Now
		}
	});

	// Memoize table columns for performance
	const columns = useMemo(() => getBetReportTableColumns(), []);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ limit: itemsPerPage });
	}, [handleFilterChange]);

	// Handle page change with tracking
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	return (
		<div className="space-y-6">
			{/* Global Filter Section - identical to main bet report page */}
			<GlobalFilterSection
				filters={filters}
				onFilterChange={handleFilterChange}
				isLoading={isLoading || isFetching}
				onExport={() => {
					//eslint-disable-next-line no-console
					console.log('Export user-specific bet report');
				}}
				showExportButton={true}
				availableFilters={BET_REPORT_FILTERS}
				defaultVisibleFilters={DEFAULT_BET_REPORT_VISIBLE_FILTERS}
				title="Filters"
			/>

			{/* Bet Report Table Section - identical to main bet report page */}
			<div className="transform transition-all duration-500 ease-in-out rounded-[16px] overflow-visible relative">
				<div className="bg-filter p-[1rem] rounded-md">
					{isError ? (
						<SpkErrorMessage
							message={error?.message || "Failed to load bet report"}
							onRetry={handleRefresh}
							variant="alert"
							size="md"
						/>
					) : (
						<GlobalDataTable
							columns={columns}
							data={betReportResponse?.data || []}
							isLoading={isLoading}
							showPagination={true}
							currentPage={filters.page}
							totalItems={betReportResponse?.count || 0}
							itemsPerPage={filters.limit}
							totalPages={betReportResponse?.totalPages}
							onPageChange={handlePageChangeWithTracking}
							onItemsPerPageChange={handleItemsPerPageChange}
							showItemsPerPageSelector={true}
							className="user-bet-report-table"
							emptyText="No bet data found for this user. Try adjusting your search filters."
						/>
					)}
				</div>
			</div>
		</div>
	);
};

export default UserAccountSummaryContent;
