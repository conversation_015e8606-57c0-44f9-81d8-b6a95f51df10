// app/(components)/(content-layout)/user-management/details/[id]/components/UserDetailsTabNavigation.tsx
import React from 'react';

export type TabType = 'financial-report' | 'account-statement' | 'login-history';

interface UserDetailsTabNavigationProps {
	activeTab: TabType;
	onTabChange: (tab: TabType) => void;
}

const UserDetailsTabNavigation: React.FC<UserDetailsTabNavigationProps> = ({
	activeTab,
	onTabChange
}) => {
	const tabs = [
		{ key: 'financial-report' as TabType, label: 'Financial Report' },
		{ key: 'account-statement' as TabType, label: 'Account Statement' },
		{ key: 'login-history' as TabType, label: 'Login History' }
	];

	return (
		<div className="h-[67px] w-full max-w-[1768px] bg-section rounded-2xl p-2 flex gap-2">
			{tabs.map((tab) => (
				<button
					key={tab.key}
					onClick={() => onTabChange(tab.key)}
					className={`flex-1 h-full rounded-xl px-3 py-4 font-rubik font-normal text-base text-center transition-all ${
						activeTab === tab.key
							? 'bg-gradient-to-b from-[rgba(225,182,73,0)] to-[rgba(225,182,73,0.42)] border border-[#FFFFFF1A] text-white'
							: 'text-gray-400 hover:text-white hover:bg-gray-700/20'
					}`}
					style={{
						textTransform: 'capitalize',
						fontSize: '16px',
						lineHeight: '100%',
						fontWeight: 400
					}}
				>
					{tab.label}
				</button>
			))}
		</div>
	);
};

export default UserDetailsTabNavigation;
