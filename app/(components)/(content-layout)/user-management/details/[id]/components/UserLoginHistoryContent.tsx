// app/(components)/(content-layout)/user-management/details/[id]/components/UserLoginHistoryContent.tsx
"use client";

import { useLoginHistory } from "@/shared/hooks/business/useLoginHistory";
import React, { useCallback, useMemo } from "react";

// Import global components
import {
	GlobalDataTable,
	SpkErrorMessage
} from "@/shared/UI/components";

// Import table columns from login history page
import { getLoginHistoryTableColumns } from "@/app/(components)/(content-layout)/login-history/components/LoginHistoryTableColumns";

interface UserLoginHistoryContentProps {
	userId: string;
}

/**
 * User-specific login history content component
 * Reuses the same GlobalDataTable component from the main login history page
 * but with data pre-filtered for the specific user
 * 
 * Features:
 * - Identical UI styling and functionality to main login history page
 * - User-specific data filtering via playerId parameter
 * - Full pagination functionality
 * - Maintains exact appearance and behavior of global components
 * - No filter section needed for user-specific view
 */
export const UserLoginHistoryContent: React.FC<UserLoginHistoryContentProps> = ({ userId }) => {
	// Use the login history hook with user-specific filtering
	const {
		filters,
		loginHistoryResponse,
		isLoading,
		isError,
		error,
		isFetching: _isFetching,
		totalRecords,
		handleFilterChange,
		handlePageChange,
		handleRefresh
	} = useLoginHistory({
		playerId: userId // Pre-filter for this specific user
	});

	// Memoize table columns for performance
	const columns = useMemo(() => getLoginHistoryTableColumns(), []);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ limit: itemsPerPage.toString() });
	}, [handleFilterChange]);

	// Handle page change with tracking
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	return (
		<div className="space-y-6">
			{/* Login History Table Section - identical to main login history page */}
			<div className="transform transition-all duration-500 ease-in-out rounded-[16px] overflow-visible relative">
				<div className="bg-filter p-[1rem] rounded-md">
					{isError ? (
						<SpkErrorMessage
							message={error?.message || "Failed to load login history"}
							onRetry={handleRefresh}
							variant="alert"
							size="md"
						/>
					) : (
						<GlobalDataTable
							columns={columns}
							data={loginHistoryResponse?.data || []}
							isLoading={isLoading}
							showPagination={true}
							currentPage={parseInt(filters.page)}
							totalItems={totalRecords}
							itemsPerPage={parseInt(filters.limit)}
							totalPages={loginHistoryResponse?.totalPages}
							onPageChange={handlePageChangeWithTracking}
							onItemsPerPageChange={handleItemsPerPageChange}
							showItemsPerPageSelector={true}
							className="user-login-history-table"
							emptyText="No login history found for this user."
							minHeight="400px"
						/>
					)}
				</div>
			</div>
		</div>
	);
};

export default UserLoginHistoryContent;
