// app/(components)/(content-layout)/user-management/details/[id]/components/AccountStatementTabContent.tsx
import React, { Fragment } from 'react';
import { SpkErrorMessage } from "@/shared/UI/components";

interface AccountStatementTabContentProps {
	betReportResponse: any;
	isBetReportLoading: boolean;
	isBetReportError: boolean;
	betReportError: any;
	refetchBetReport: () => void;
}

const AccountStatementTabContent: React.FC<AccountStatementTabContentProps> = ({
	betReportResponse,
	isBetReportLoading,
	isBetReportError,
	betReportError,
	refetchBetReport
}) => {
	return (
		<div className="space-y-6">
			{isBetReportError ? (
				<SpkErrorMessage
					message={betReportError?.message || "Failed to load bet report"}
					onRetry={refetchBetReport}
					variant="alert"
					size="md"
				/>
			) : (
				<Fragment>
					{/* Bet Report Summary */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="bg-section p-4 rounded-lg">
							<div className="text-sm text-gray-400">Total Bets</div>
							<div className="text-2xl font-bold text-white">
								{betReportResponse?.count?.toLocaleString() || '0'}
							</div>
						</div>
						<div className="bg-section p-4 rounded-lg">
							<div className="text-sm text-gray-400">Total Bet Amount</div>
							<div className="text-2xl font-bold text-blue-400">
								${betReportResponse?.totalBetAmount?.toLocaleString() || '0'}
							</div>
						</div>
						<div className="bg-section p-4 rounded-lg">
							<div className="text-sm text-gray-400">Total Win Amount</div>
							<div className="text-2xl font-bold text-green-400">
								${betReportResponse?.totalWinAmount?.toLocaleString() || '0'}
							</div>
						</div>
					</div>

					{/* Bet Report Table */}
					<div className="bg-section rounded-lg p-6">
						<div className="flex items-center justify-between mb-4">
							<h3 className="text-lg font-semibold text-white font-rubik">
								Bet History
							</h3>
							<div className="text-sm text-gray-400">
								{betReportResponse?.data?.length || 0} records
							</div>
						</div>

						{isBetReportLoading ? (
							<div className="text-center py-8">
								<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
								<p className="text-gray-400 mt-2">Loading bet data...</p>
							</div>
						) : betReportResponse?.data?.length ? (
							<div className="overflow-x-auto">
								<table className="w-full text-sm">
									<thead>
										<tr className="border-b border-gray-600">
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Bet ID</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Date</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Market</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Bet Amount</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Win Amount</th>
											<th className="text-left py-3 px-2 text-gray-400 font-medium">Status</th>
										</tr>
									</thead>
									<tbody>
										{betReportResponse.data.slice(0, 10).map((bet: any, index: number) => (
											<tr key={bet.betId || index} className="border-b border-gray-700 hover:bg-gray-700/20">
												<td className="py-3 px-2 text-white font-mono text-xs">
													{bet.betId || 'N/A'}
												</td>
												<td className="py-3 px-2 text-gray-300">
													{bet.createdAt ? new Date(bet.createdAt).toLocaleDateString() : 'N/A'}
												</td>
												<td className="py-3 px-2 text-gray-300">
													{bet.marketName || 'N/A'}
												</td>
												<td className="py-3 px-2 text-white">
													${bet.betAmount?.toLocaleString() || '0'}
												</td>
												<td className="py-3 px-2 text-green-400">
													${bet.winAmount?.toLocaleString() || '0'}
												</td>
												<td className="py-3 px-2">
													<span className={`px-2 py-1 rounded-full text-xs ${bet.status === 'won' ? 'bg-green-100 text-green-800' :
														bet.status === 'lost' ? 'bg-red-100 text-red-800' :
															bet.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
																'bg-gray-100 text-gray-800'
														}`}>
														{bet.status || 'Unknown'}
													</span>
												</td>
											</tr>
										))}
									</tbody>
								</table>
							</div>
						) : (
							<div className="text-center py-8">
								<i className="ri-file-chart-line text-4xl text-gray-400 mb-2"></i>
								<p className="text-gray-400">No bet data found</p>
							</div>
						)}
					</div>
				</Fragment>
			)}
		</div>
	);
};

export default AccountStatementTabContent;
