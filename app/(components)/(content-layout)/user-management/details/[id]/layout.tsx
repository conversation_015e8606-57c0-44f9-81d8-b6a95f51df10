// app/(components)/(content-layout)/user-management/details/[id]/layout.tsx - Dynamic metadata for user details
import React from "react";
import type { Metadata } from "next";
import { generateUserManagementMetadata, createCanonicalUrl } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	ArticleStructuredData
} from "@/shared/seo/components/StructuredData";

interface UserDetailsLayoutProps {
	children: React.ReactNode;
	params: Promise<{ id: string }>;
}

/**
 * Generate dynamic metadata for user details page
 */
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
	const { id: userId } = await params;

	// In a real application, you would fetch user data here
	// const user = await fetchUserById(userId);

	// For now, we'll generate metadata with the user ID
	const dynamicData = {
		title: `User Details - User ${userId}`,
		description: `Comprehensive user profile and details for user ${userId} including activity history, wallet information, and administrative controls.`,
		path: `/user-management/details/${userId}`,
		keywords: ["user profile", "user details", "user activity", "account information", "user analytics"],
	};

	return generateUserManagementMetadata("details", dynamicData);
}

/**
 * Server-side layout component for user details
 * Provides SEO optimization and structured data for user detail pages
 */
export default async function UserDetailsLayout({ children, params }: UserDetailsLayoutProps) {
	const { id: userId } = await params;

	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management", url: "/user-management" },
					{ name: `User ${userId}` }
				]}
			/>
			<ArticleStructuredData
				headline={`User Profile - User ${userId}`}
				description={`Comprehensive user profile and details for user ${userId}`}
				url={createCanonicalUrl(`/user-management/details/${userId}`)}
				author={{
					name: "Xintra System",
					url: "https://xintra.com"
				}}
				publisher={{
					name: "Xintra",
					logo: "https://xintra.com/assets/images/logo.png"
				}}
			/>

			{children}
		</>
	);
}
