// app/(components)/(content-layout)/user-management/details/[id]/bet-history/page.tsx - Server-side rendered bet history page with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateUserManagementMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { BetHistoryPageClient } from "./components/BetHistoryPageClient";

interface Props {
	params: Promise<{ id: string }>;
}

/**
 * Generate metadata for bet history page
 */
export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { id } = await params;
	return generateUserManagementMetadata("details", {
		title: `Bet History - User ${id}`,
		description: `Comprehensive betting transaction history and analytics for user ${id}. View detailed bet records, transaction types, amounts, and performance metrics.`,
		path: `/user-management/details/${id}/bet-history`,
		keywords: ["bet history", "transaction history", "user analytics", "betting records", "user management"]
	});
}

/**
 * Server-side rendered bet history page component
 * Handles SEO metadata generation and delegates client-side logic to BetHistoryPageClient
 */
const BetHistoryPage: React.FC<Props> = async ({ params }) => {
	const { id } = await params;
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management", url: "/user-management" },
					{ name: `User Details - ${id}`, url: `/user-management/details/${id}` },
					{ name: "Bet History" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Bet History Management"
				description="Comprehensive betting transaction history and analytics with advanced filtering and reporting capabilities"
				url={`/user-management/details/${id}/bet-history`}
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component */}
			<BetHistoryPageClient userId={id} />
		</>
	);
};

export default BetHistoryPage;
