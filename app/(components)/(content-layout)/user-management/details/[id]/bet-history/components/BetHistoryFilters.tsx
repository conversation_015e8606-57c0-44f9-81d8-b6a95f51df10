// app/(components)/(content-layout)/user-management/details/[id]/bet-history/components/BetHistoryFilters.tsx
"use client";

import React from "react";
import { BetHistoryFilters as BetHistoryFiltersType } from "@/shared/types/user-management-types";
import { SpkAdvancedFilters } from "@/shared/UI/components";
import { BET_HISTORY_FILTERS, DEFAULT_BET_HISTORY_VISIBLE_FILTERS } from "@/shared/config/betHistoryFilters";

interface BetHistoryFiltersProps {
	filters: BetHistoryFiltersType;
	onFilterChange: (_filters: Partial<BetHistoryFiltersType>) => void;
	isLoading?: boolean;
}

const BetHistoryFilters: React.FC<BetHistoryFiltersProps> = ({
	filters: _filters,
	onFilterChange,
	isLoading = false
}) => {
	return (
		<SpkAdvancedFilters
			filters={_filters}
			onFilterChange={onFilterChange}
			isLoading={isLoading}
			availableFilters={BET_HISTORY_FILTERS}
			defaultVisibleFilters={DEFAULT_BET_HISTORY_VISIBLE_FILTERS}
			title="Bet History Filters"
			description="Filter and search betting transactions with advanced criteria"
		/>
	);
};

export default BetHistoryFilters;
