// app/(components)/(content-layout)/user-management/details/[id]/bet-history/components/BetHistoryPageClient.tsx - Client-side component for bet history
"use client";

import React, { Fragment, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useBetHistoryQuery } from "@/shared/query";
import { useAuthStore } from "@/shared/stores/authStore";
import { BetHistoryFilters, DEFAULT_BET_HISTORY_FILTERS } from "@/shared/types/user-management-types";
import { formatSriLankanCurrency, formatUserDate } from "@/shared/utils/userDetailsUtils";
import { SpkTable, SpkTableColumn, SpkBadge } from "@/shared/UI/components";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

// Import enhanced components
import EnhancedPageHeader from "../../../../components/EnhancedPageHeader";
import BetHistoryFiltersComponent from "./BetHistoryFilters";

interface Props {
	userId: string;
}

/**
 * Client-side component that handles all interactive functionality for bet history
 * Separated from the server component to maintain SSR SEO benefits
 */
export function BetHistoryPageClient({ userId }: Props) {
	const router = useRouter();
	const { isAuthenticated, _hasHydrated } = useAuthStore();

	// State for filters
	const [filters, setFilters] = useState<BetHistoryFilters>({
		...DEFAULT_BET_HISTORY_FILTERS,
		playerId: userId
	});

	// Fetch bet history using the query hook
	const {
		data: betHistoryResponse,
		isLoading,
		isError,
		error,
		refetch,
		isFetching
	} = useBetHistoryQuery(userId, filters);

	// Redirect if not authenticated
	useEffect(() => {
		if (_hasHydrated && !isAuthenticated) {
			router.replace("/authentication/sign-in/");
		}
	}, [_hasHydrated, isAuthenticated, router]);

	// Handle filter changes
	const handleFilterChange = (newFilters: Partial<BetHistoryFilters>) => {
		setFilters(prev => ({
			...prev,
			...newFilters,
			page: 1 // Reset to first page when filters change
		}));
	};

	// Handle pagination
	const handlePageChange = (page: number) => {
		setFilters(prev => ({ ...prev, page }));
	};

	// Handle refresh
	const handleRefresh = () => {
		refetch();
	};

	const handleBackToUserDetails = () => {
		router.push(`/user-management/details/${userId}`);
	};

	// Don't render anything if not authenticated and hydrated
	if (_hasHydrated && !isAuthenticated) {
		return null;
	}

	// Safely extract transactions array from response
	const transactions = Array.isArray(betHistoryResponse?.data)
		? betHistoryResponse.data
		: [];

	// Define table columns for bet history
	const columns: SpkTableColumn[] = [
		{
			key: "transaction_id",
			title: "Transaction ID",
			width: "140px",
			render: (value) => (
				<span className="font-mono text-xs text-gray-600 dark:text-gray-400">
					{value || "N/A"}
				</span>
			)
		},
		{
			key: "transaction_type",
			title: "Type",
			width: "150px",
			render: (value) => {
				// Simplify transaction type display
				const getTypeDisplay = (type: string) => {
					if (type?.includes("credit")) return "Credit";
					if (type?.includes("debit")) return "Debit";
					if (type?.includes("bet")) return "Bet";
					if (type?.includes("win")) return "Win";
					return type?.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()) || "N/A";
				};

				const getTypeVariant = (type: string) => {
					if (type?.includes("credit") || type?.includes("win")) return "success";
					if (type?.includes("debit") || type?.includes("bet")) return "primary";
					return "info";
				};

				return (
					<SpkBadge variant={getTypeVariant(value)} className="text-xs">
						{getTypeDisplay(value)}
					</SpkBadge>
				);
			}
		},
		{
			key: "amount",
			title: "Amount",
			width: "120px",
			render: (value) => (
				<span className="font-semibold">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "currency",
			title: "Currency",
			width: "100px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{value?.toUpperCase() || "N/A"}
				</span>
			)
		},
		{
			key: "status",
			title: "Status",
			width: "100px",
			render: (value) => {
				const getStatusVariant = (status: string) => {
					switch (status?.toLowerCase()) {
						case "success":
							return "success";
						case "pending":
							return "warning";
						case "cancelled":
						case "failed":
							return "danger";
						default:
							return "secondary";
					}
				};

				return (
					<SpkBadge variant={getStatusVariant(value)} className="text-xs">
						{value?.toUpperCase() || "N/A"}
					</SpkBadge>
				);
			}
		},
		{
			key: "game_provider",
			title: "Provider",
			width: "120px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{value || "N/A"}
				</span>
			)
		},
		{
			key: "initial_balance",
			title: "Initial Balance",
			width: "130px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "ending_balance",
			title: "Ending Balance",
			width: "130px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "created_at",
			title: "Date",
			width: "140px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatUserDate(value)}
				</span>
			)
		}
	];

	return (
		<Fragment>
			{/* Enhanced Page Header */}
			<EnhancedPageHeader
				title="Bet History"
				description={`Comprehensive betting transaction history for User ${userId}`}
				icon="ri-history-line"
				breadcrumbs={[
					{ label: "User Management", href: "/user-management" },
					{ label: "User Details", href: `/user-management/details/${userId}` },
					{ label: "Bet History" }
				]}
				quickStats={[
					{
						label: "Total Transactions",
						value: betHistoryResponse?.count?.toString() || "0"
					},
					{
						label: "Current Page",
						value: `${filters.page} of ${betHistoryResponse?.totalPages || 1}`
					}
				]}
				actionButtons={[
					{
						label: "Back to User Details",
						icon: "ri-arrow-left-line",
						onClick: handleBackToUserDetails,
						variant: "ti-btn-outline-secondary"
					},
					{
						label: "Refresh",
						icon: "ri-refresh-line",
						onClick: handleRefresh,
						variant: "ti-btn-primary",
						disabled: isFetching,
						loading: isFetching
					}
				]}
			/>

			{/* Filters */}
			<BetHistoryFiltersComponent
				filters={filters}
				onFilterChange={handleFilterChange}
				isLoading={isLoading}
			/>

			{/* Bet History Table */}
			<div className="box">
				<div className="box-header">
					<div className="box-title">
						<i className="ri-history-line me-2"></i>
						Transaction History
						{transactions.length > 0 && (
							<SpkBadge variant="primary" className="ml-2">
								{transactions.length}
							</SpkBadge>
						)}
					</div>
				</div>
				<div className="box-body">
					<SpkTable
						columns={columns}
						data={transactions}
						loading={isLoading}
						hover={true}
						responsive={true}
						emptyText="No transactions found"
						className="bet-history-table"
						size="sm"
					/>

					{/* Pagination */}
					{betHistoryResponse?.totalPages && betHistoryResponse.totalPages > 1 && (
						<div className="flex justify-between items-center mt-4">
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Showing page {filters.page} of {betHistoryResponse.totalPages}
							</div>
							<div className="flex gap-2">
								<SpkButton
									type="button"
									customClass="ti-btn ti-btn-sm ti-btn-outline-primary"
									disabled={filters.page <= 1}
									onclickfunc={() => handlePageChange(filters.page - 1)}
								>
									<i className="ri-arrow-left-line me-1"></i>
									Previous
								</SpkButton>
								<SpkButton
									type="button"
									customClass="ti-btn ti-btn-sm ti-btn-outline-primary"
									disabled={filters.page >= (betHistoryResponse.totalPages || 1)}
									onclickfunc={() => handlePageChange(filters.page + 1)}
								>
									Next
									<i className="ri-arrow-right-line ms-1"></i>
								</SpkButton>
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Show error state */}
			{isError && (
				<div className="box">
					<div className="box-body">
						<div className="text-center py-8">
							<i className="ri-error-warning-line text-4xl text-red-500 mb-2"></i>
							<p className="text-gray-600 dark:text-gray-400">
								{error?.message || "Failed to load bet history"}
							</p>
							<SpkButton
								type="button"
								customClass="ti-btn ti-btn-primary mt-4"
								onclickfunc={handleRefresh}
							>
								<i className="ri-refresh-line me-1"></i>
								Try Again
							</SpkButton>
						</div>
					</div>
				</div>
			)}
		</Fragment>
	);
}
