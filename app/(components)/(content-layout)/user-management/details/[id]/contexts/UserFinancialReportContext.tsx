"use client";

import { useFinancialReport } from "@/shared/hooks/business/useFinancialReport";
import { FinancialReportFilters, FinancialReportResponse, DEFAULT_FINANCIAL_REPORT_FILTERS } from "@/shared/types/report-types";
import React, { createContext, useContext, ReactNode } from "react";

interface UserFinancialReportContextType {
  // State
  filters: FinancialReportFilters;

  // Data
  financialReportResponse: FinancialReportResponse | null;
  isLoading: boolean;
  isError: boolean;
  error: any;
  isFetching: boolean;

  // Computed values
  totalTransactions: number;
  totalAmount: number;
  totalDeposits: number;
  totalWithdrawals: number;

  // Actions
  handleFilterChange: (newFilters: Partial<FinancialReportFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

const UserFinancialReportContext = createContext<UserFinancialReportContextType | undefined>(undefined);

interface UserFinancialReportProviderProps {
  userId: string;
  children: ReactNode;
}

/**
 * Context provider for user financial report data
 * Provides shared state between UserDetailsPageClient summary cards and UserFinancialReportContent
 * 
 * Features:
 * - Shared filters and data between components
 * - Automatic totals fetching for summary cards
 * - Filter synchronization across components
 */
export const UserFinancialReportProvider: React.FC<UserFinancialReportProviderProps> = ({
  userId,
  children
}) => {
  // Use the financial report hook with user-specific filtering and totals enabled
  const financialReportData = useFinancialReport({
    userId, // This will automatically set playerId in the filters
    initialFilters: {
      ...DEFAULT_FINANCIAL_REPORT_FILTERS,
      totals: true // Enable totals for summary cards
    }
  });

  return (
    <UserFinancialReportContext.Provider value={financialReportData}>
      {children}
    </UserFinancialReportContext.Provider>
  );
};

/**
 * Hook to access the user financial report context
 * Must be used within a UserFinancialReportProvider
 */
export const useUserFinancialReportContext = (): UserFinancialReportContextType => {
  const context = useContext(UserFinancialReportContext);

  if (context === undefined) {
    throw new Error('useUserFinancialReportContext must be used within a UserFinancialReportProvider');
  }

  return context;
};
