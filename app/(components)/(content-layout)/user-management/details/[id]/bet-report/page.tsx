// app/(components)/(content-layout)/user-management/details/[id]/bet-report/page.tsx - Server-side rendered bet report page with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateUserManagementMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { BetReportPageClient } from "./components/BetReportPageClient";

interface Props {
	params: Promise<{
		id: string;
	}>;
}

/**
 * Generate metadata for bet report page
 */
export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { id } = await params;
	return generateUserManagementMetadata("details", {
		title: `Bet Report - User ${id}`,
		description: `Comprehensive bet win report and betting analytics for user ${id}. View detailed bet history, win/loss analysis, and betting patterns.`,
		path: `/user-management/details/${id}/bet-report`,
		keywords: ["bet report", "betting analytics", "win loss report", "user betting", "gambling statistics"]
	});
}

/**
 * Server-side rendered bet report page component
 * Handles SEO metadata generation and delegates client-side logic to BetReportPageClient
 */
const BetReportPage: React.FC<Props> = async ({ params }) => {
	const { id } = await params;
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "User Management", url: "/user-management" },
					{ name: "User Details", url: `/user-management/details/${id}` },
					{ name: "Bet Report" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Bet Report System"
				description="Comprehensive bet win report system with detailed analytics and betting pattern analysis"
				url={`/user-management/details/${id}/bet-report`}
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<BetReportPageClient userId={id} />
		</>
	);
};

export default BetReportPage;
