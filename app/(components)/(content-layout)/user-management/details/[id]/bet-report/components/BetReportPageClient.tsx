// app/(components)/(content-layout)/user-management/details/[id]/bet-report/components/BetReportPageClient.tsx - Client-side component for bet report
"use client";

import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";
import { useBetWinReportQuery } from "@/shared/query";
import { useAuthStore } from "@/shared/stores/authStore";
import { BetWinReportFilters, DEFAULT_BET_WIN_REPORT_FILTERS } from "@/shared/types/user-management-types";
import { SpkBadge, SpkTable, SpkTableColumn } from "@/shared/UI/components";
import { formatSriLankanCurrency, formatUserDate } from "@/shared/utils/userDetailsUtils";
import { useRouter } from "next/navigation";
import { Fragment, useEffect, useState } from "react";

// Import enhanced components
import EnhancedPageHeader from "../../../../components/EnhancedPageHeader";
import BetReportFilters from "./BetReportFilters";

interface Props {
	userId: string;
}

/**
 * Client-side component that handles all interactive functionality for bet report
 * Separated from the server component to maintain SSR SEO benefits
 */
export function BetReportPageClient({ userId }: Props) {
	const router = useRouter();
	const { isAuthenticated, _hasHydrated } = useAuthStore();

	// State for filters
	const [filters, setFilters] = useState<BetWinReportFilters>({
		...DEFAULT_BET_WIN_REPORT_FILTERS,
		playerId: userId
	});

	// Fetch bet report using the query hook
	const {
		data: betReportResponse,
		isLoading,
		isError,
		error,
		refetch,
		isFetching
	} = useBetWinReportQuery(filters);

	// Safely extract bet data array from response
	const betData = Array.isArray(betReportResponse?.data)
		? betReportResponse.data
		: [];

	// Handle authentication redirect
	useEffect(() => {
		if (_hasHydrated && !isAuthenticated) {
			router.push('/auth/login');
		}
	}, [isAuthenticated, _hasHydrated, router]);

	// Event handlers
	const handleFilterChange = (newFilters: Partial<BetWinReportFilters>) => {
		setFilters(prev => ({
			...prev,
			...newFilters,
			playerId: userId // Always maintain the userId
		}));
	};

	const handleBackToUserDetails = () => {
		router.push(`/user-management/details/${userId}`);
	};

	const handleRefresh = () => {
		refetch();
	};

	// Show loading state during hydration
	if (!_hasHydrated) {
		return <div className="flex justify-center items-center min-h-screen">
			<div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
		</div>;
	}

	// Show error state
	if (isError) {
		return (
			<Fragment>
				<div className="flex items-center justify-between flex-wrap gap-2 mb-6">
					<div>
						<ol className="breadcrumb mb-0">
							<li className="breadcrumb-item">
								<a href="/user-management">User Management</a>
							</li>
							<li className="breadcrumb-item">
								<a href={`/user-management/details/${userId}`}>User Details</a>
							</li>
							<li className="breadcrumb-item active" aria-current="page">Bet Report</li>
						</ol>
						<h1 className="page-title font-medium text-lg mb-0">Bet Report</h1>
					</div>
					<div className="flex gap-2">
						<SpkButton
							variant="outline-primary"
							onclickfunc={handleBackToUserDetails}
						>
							<i className="ri-arrow-left-line me-1"></i>
							Back to User Details
						</SpkButton>
					</div>
				</div>

				<div className="box">
					<div className="box-body text-center py-8">
						<i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
						<h3 className="text-lg font-semibold mb-2">Failed to Load Bet Report</h3>
						<p className="text-gray-600 dark:text-gray-400 mb-4">
							{error?.message || "An error occurred while fetching the bet report data"}
						</p>
						<SpkButton
							variant="primary"
							onclickfunc={handleRefresh}
						>
							<i className="ri-refresh-line me-1"></i>
							Try Again
						</SpkButton>
					</div>
				</div>
			</Fragment>
		);
	}

	// Define table columns for bet data
	const columns: SpkTableColumn[] = [
		{
			key: "betSlipId",
			title: "Bet Slip ID",
			width: "120px",
			render: (value) => (
				<span className="font-mono text-sm text-primary">
					#{value}
				</span>
			)
		},
		{
			key: "betId",
			title: "Bet ID",
			width: "200px",
			render: (value) => (
				<span className="font-mono text-xs text-gray-600 dark:text-gray-400">
					{value}
				</span>
			)
		},
		{
			key: "status",
			title: "Status",
			width: "100px",
			render: (value) => {
				const getStatusVariant = (status: string) => {
					switch (status?.toLowerCase()) {
						case "won":
							return "success";
						case "lost":
							return "danger";
						case "cancelled":
							return "secondary";
						case "pending":
							return "warning";
						default:
							return "info";
					}
				};

				return (
					<SpkBadge variant={getStatusVariant(value)} className="text-xs">
						{value?.toUpperCase() || "N/A"}
					</SpkBadge>
				);
			}
		},
		{
			key: "betAmount",
			title: "Bet Amount",
			width: "120px",
			render: (value) => (
				<span className="text-sm font-medium text-gray-800 dark:text-white">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "winAmount",
			title: "Win Amount",
			width: "120px",
			render: (value) => (
				<span className={`text-sm font-medium ${Number(value) > 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}`}>
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "betList",
			title: "Market Details",
			width: "300px",
			render: (value, _record) => {
				const betList = Array.isArray(value) ? value : [];
				if (betList.length === 0) return <span className="text-gray-400">No details</span>;

				const firstBet = betList[0];
				return (
					<div className="text-xs">
						<div className="font-medium text-gray-800 dark:text-white mb-1">
							{firstBet.match}
						</div>
						<div className="text-gray-600 dark:text-gray-400 mb-1">
							{firstBet.market}
						</div>
						<div className="text-primary font-medium">
							Odds: {firstBet.price}
						</div>
						{betList.length > 1 && (
							<div className="text-gray-500 mt-1">
								+{betList.length - 1} more selections
							</div>
						)}
					</div>
				);
			}
		},
		{
			key: "createdAt",
			title: "Date",
			width: "140px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatUserDate(value)}
				</span>
			)
		}
	];

	return (
		<Fragment>
			{/* Enhanced Page Header */}
			<EnhancedPageHeader
				title="Bet Report"
				description={`Comprehensive bet win report and analytics for user ${userId}`}
				icon="ri-file-chart-line"
				breadcrumbs={[
					{ label: "User Management", href: "/user-management" },
					{ label: "User Details", href: `/user-management/details/${userId}` },
					{ label: "Bet Report" }
				]}
				quickStats={[
					{
						label: "Total Bets",
						value: betReportResponse?.count?.toString() || "0"
					},
					{
						label: "Current Page",
						value: `${filters.page} of ${Math.ceil(Number(betReportResponse?.count || 0) / filters.limit)}`
					},
					{
						label: "Results Per Page",
						value: filters.limit.toString()
					}
				]}
				actionButtons={[
					{
						label: "Back to User Details",
						icon: "ri-arrow-left-line",
						onClick: handleBackToUserDetails,
						variant: "ti-btn-outline-secondary"
					},
					{
						label: "Refresh",
						icon: "ri-refresh-line",
						onClick: handleRefresh,
						variant: "ti-btn-primary",
						disabled: isFetching,
						loading: isFetching
					}
				]}
			/>

			{/* Filters */}
			<BetReportFilters
				filters={filters}
				onFilterChange={handleFilterChange}
				isLoading={isLoading}
			/>

			{/* Bet Report Table */}
			<div className="box">
				<div className="box-header">
					<div className="box-title">
						<i className="ri-file-chart-line me-2"></i>
						Bet Win Report
						{betData.length > 0 && (
							<SpkBadge variant="primary" className="ml-2">
								{betData.length}
							</SpkBadge>
						)}
					</div>
				</div>
				<div className="box-body">
					<SpkTable
						columns={columns}
						data={betData}
						loading={isLoading}
						hover={true}
						responsive={true}
						emptyText="No bet data found"
						className="bet-report-table"
						size="sm"
					/>

					{betData.length === 0 && !isLoading && (
						<div className="text-center py-8">
							<i className="ri-file-chart-line text-4xl text-gray-400 mb-2"></i>
							<p className="text-gray-600 dark:text-gray-400">
								No bet data found for the selected criteria
							</p>
						</div>
					)}
				</div>
			</div>
		</Fragment>
	);
}
