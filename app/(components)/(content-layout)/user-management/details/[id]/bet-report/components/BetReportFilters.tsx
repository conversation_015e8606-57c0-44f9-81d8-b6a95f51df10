// app/(components)/(content-layout)/user-management/details/[id]/bet-report/components/BetReportFilters.tsx
"use client";

import React, { useState } from "react";
import { BetWinReportFilters } from "@/shared/types/user-management-types";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

interface BetReportFiltersProps {
	filters: BetWinReportFilters;
	onFilterChange: (_filters: Partial<BetWinReportFilters>) => void;
	isLoading?: boolean;
}

/**
 * Bet Report Filters Component
 * Provides filtering options for the bet win report
 */
const BetReportFilters: React.FC<BetReportFiltersProps> = ({
	filters,
	onFilterChange,
	isLoading = false
}) => {
	const [isExpanded, setIsExpanded] = useState(false);

	// Handle form input changes
	const handleInputChange = (field: keyof BetWinReportFilters, value: any) => {
		onFilterChange({ [field]: value });
	};

	// Handle date range presets
	const handleDatePreset = (preset: string) => {
		const now = new Date();
		let startDate: Date;
		let endDate = new Date();

		switch (preset) {
			case 'today':
				startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
				break;
			case 'yesterday':
				startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
				endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59);
				break;
			case 'last7days':
				startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
				break;
			case 'last30days':
				startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
				break;
			case 'thisMonth':
				startDate = new Date(now.getFullYear(), now.getMonth(), 1);
				break;
			case 'lastMonth':
				startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
				endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
				break;
			default:
				return;
		}

		onFilterChange({
			startDate: startDate.toISOString().slice(0, 16),
			endDate: endDate.toISOString().slice(0, 16)
		});
	};

	// Reset filters to defaults
	const handleReset = () => {
		const now = new Date();
		const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

		onFilterChange({
			page: 1,
			limit: 25,
			startDate: thirtyDaysAgo.toISOString().slice(0, 16),
			endDate: now.toISOString().slice(0, 16),
			transactionId: undefined
		});
	};

	return (
		<div className="box">
			<div className="box-header">
				<div className="flex items-center justify-between w-full">
					<h5 className="box-title flex items-center gap-2">
						<i className="ri-filter-3-line text-primary"></i>
						Bet Report Filters
					</h5>
					<SpkButton
						type="button"
						customClass="ti-btn ti-btn-sm ti-btn-outline-primary"
						onclickfunc={() => setIsExpanded(!isExpanded)}
					>
						<i className={`ri-${isExpanded ? 'arrow-up' : 'arrow-down'}-s-line me-1`}></i>
						{isExpanded ? 'Collapse' : 'Expand'} Filters
					</SpkButton>
				</div>
			</div>

			{isExpanded && (
				<div className="box-body">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
						{/* Date Range */}
						<div className="col-span-1 md:col-span-2">
							<label className="form-label text-sm font-medium mb-2">Date Range</label>
							<div className="grid grid-cols-2 gap-2">
								<div>
									<label className="form-label text-xs text-gray-600 dark:text-gray-400">Start Date</label>
									<input
										type="datetime-local"
										className="form-control form-control-sm"
										value={filters.startDate}
										onChange={(e) => handleInputChange('startDate', e.target.value)}
										disabled={isLoading}
									/>
								</div>
								<div>
									<label className="form-label text-xs text-gray-600 dark:text-gray-400">End Date</label>
									<input
										type="datetime-local"
										className="form-control form-control-sm"
										value={filters.endDate}
										onChange={(e) => handleInputChange('endDate', e.target.value)}
										disabled={isLoading}
									/>
								</div>
							</div>
						</div>

						{/* Transaction ID Filter */}
						<div>
							<label className="form-label text-sm font-medium mb-2">Transaction ID</label>
							<input
								type="text"
								className="form-control form-control-sm"
								placeholder="Enter transaction ID"
								value={filters.transactionId || ''}
								onChange={(e) => handleInputChange('transactionId', e.target.value || undefined)}
								disabled={isLoading}
							/>
							<small className="text-gray-500 dark:text-gray-400">
								Leave empty to view all bets
							</small>
						</div>

						{/* Results Per Page */}
						<div>
							<label className="form-label text-sm font-medium mb-2">Results Per Page</label>
							<select
								className="form-select form-select-sm"
								value={filters.limit}
								onChange={(e) => handleInputChange('limit', parseInt(e.target.value))}
								disabled={isLoading}
							>
								<option value={10}>10</option>
								<option value={25}>25</option>
								<option value={50}>50</option>
								<option value={100}>100</option>
							</select>
						</div>
					</div>

					{/* Date Presets */}
					<div className="mb-4">
						<label className="form-label text-sm font-medium mb-2">Quick Date Presets</label>
						<div className="flex flex-wrap gap-2">
							{[
								{ label: 'Today', value: 'today' },
								{ label: 'Yesterday', value: 'yesterday' },
								{ label: 'Last 7 Days', value: 'last7days' },
								{ label: 'Last 30 Days', value: 'last30days' },
								{ label: 'This Month', value: 'thisMonth' },
								{ label: 'Last Month', value: 'lastMonth' }
							].map((preset) => (
								<SpkButton
									key={preset.value}
									type="button"
									customClass="ti-btn ti-btn-sm ti-btn-outline-info"
									onclickfunc={() => handleDatePreset(preset.value)}
									disabled={isLoading}
								>
									{preset.label}
								</SpkButton>
							))}
						</div>
					</div>

					{/* Action Buttons */}
					<div className="flex items-center gap-2">
						<SpkButton
							type="button"
							customClass="ti-btn ti-btn-sm ti-btn-primary"
							onclickfunc={() => onFilterChange({ page: 1 })} // Trigger refresh by resetting page
							disabled={isLoading}
						>
							<i className="ri-search-line me-1"></i>
							Apply Filters
						</SpkButton>
						<SpkButton
							type="button"
							customClass="ti-btn ti-btn-sm ti-btn-outline-secondary"
							onclickfunc={handleReset}
							disabled={isLoading}
						>
							<i className="ri-refresh-line me-1"></i>
							Reset
						</SpkButton>
					</div>
				</div>
			)}
		</div>
	);
};

export default BetReportFilters;
