import React from "react";
import { UserDetailsData } from "@/shared/types/user-management-types";
import { formatUserDate } from "@/shared/utils/userDetailsUtils";
import { CurrencyDisplay } from "@/shared/UI/components";

interface UserWalletDetailsProps {
	userData: UserDetailsData;
}

interface WalletInfoRowProps {
	label: string;
	value: string | number | React.ReactNode;
	icon?: string;
	variant?: "default" | "success" | "warning" | "info";
	showCurrency?: boolean;
}

const WalletInfoRow: React.FC<WalletInfoRowProps> = ({
	label,
	value,
	icon,
	variant = "default",
	showCurrency = false
}) => {
	const getVariantClasses = () => {
		switch (variant) {
			case "success":
				return "text-green-600 dark:text-green-400";
			case "warning":
				return "text-yellow-600 dark:text-yellow-400";
			case "info":
				return "text-blue-600 dark:text-blue-400";
			default:
				return "text-gray-800 dark:text-white";
		}
	};

	return (
		<div className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
			<div className="flex items-center gap-2">
				{icon && (
					<i className={`${icon} text-gray-500 dark:text-gray-400 text-sm`}></i>
				)}
				<span className="text-sm font-medium text-gray-600 dark:text-gray-300">
					{label}
				</span>
			</div>
			<div className={`text-sm font-semibold ${getVariantClasses()}`}>
				{showCurrency && typeof value === 'number' ? (
					<CurrencyDisplay
						amount={value}
						context="table"
						size={14}
						amountClassName="font-semibold"
						gap="sm"
					/>
				) : (
					value
				)}
			</div>
		</div>
	);
};

const UserWalletDetails: React.FC<UserWalletDetailsProps> = ({ userData }) => {
	return (
		<div className="box h-full flex flex-col">
			<div className="box-header">
				<div className="box-title">
					<i className="ri-wallet-3-line me-2"></i>
					Wallet Details
				</div>
			</div>
			<div className="box-body flex-1">
				<div className="space-y-1">
					{/* Current Balance */}
					<WalletInfoRow
						label="Current Balance"
						value={userData.amount}
						icon="ri-money-dollar-circle-line"
						variant="success"
						showCurrency={true}
					/>

					{/* Non-Cash Amount (Bonus) */}
					{userData.nonCashAmount > 0 && (
						<WalletInfoRow
							label="Bonus Balance"
							value={userData.nonCashAmount}
							icon="ri-gift-line"
							variant="info"
							showCurrency={true}
						/>
					)}

					{/* Last Deposited Amount */}
					<WalletInfoRow
						label="Last Deposited"
						value={userData.lastdepositedamount}
						icon="ri-arrow-down-circle-line"
						variant="warning"
						showCurrency={true}
					/>

					{/* Wallet ID */}
					<WalletInfoRow
						label="Wallet ID"
						value={userData.walletid || "N/A"}
						icon="ri-bank-card-line"
					/>

					{/* Currency */}
					<WalletInfoRow
						label="Currency"
						value={userData.currencycode?.toUpperCase() || "N/A"}
						icon="ri-exchange-line"
					/>

					{/* Last Updated */}
					<WalletInfoRow
						label="Last Updated"
						value={formatUserDate(userData.walletupdatedat)}
						icon="ri-time-line"
					/>

					{/* Withdraw Wager Status */}
					<WalletInfoRow
						label="Withdraw Wager"
						value={
							<span className={`px-2 py-1 rounded-full text-xs font-medium ${userData.withdrawWagerAllowed
								? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
								: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
								}`}>
								{userData.withdrawWagerAllowed ? "Allowed" : "Not Allowed"}
							</span>
						}
						icon="ri-shield-check-line"
					/>
				</div>

				{/* Wallet Summary Card */}
				<div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
					<div className="flex items-center gap-3">
						<div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
							<i className="ri-wallet-3-fill text-blue-600 dark:text-blue-300 text-xl"></i>
						</div>
						<div>
							<div className="text-lg font-bold text-blue-800 dark:text-blue-200">
								<CurrencyDisplay
									amount={userData.amount + (userData.nonCashAmount || 0)}
									context="card"
									size={18}
									amountClassName="text-lg font-bold"
									gap="sm"
								/>
							</div>
							<div className="text-sm text-blue-600 dark:text-blue-300">
								Total Wallet Balance
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default UserWalletDetails;
