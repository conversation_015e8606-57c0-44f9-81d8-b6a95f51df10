import React, { useMemo } from "react";
import Link from "next/link";
import { useBetHistoryQuery } from "@/shared/query";
import { BetHistoryTransaction } from "@/shared/types/user-management-types";
import { formatSriLankanCurrency, formatUserDate } from "@/shared/utils/userDetailsUtils";
import { SpkTable, SpkTableColumn, SpkBadge, SpkLoadingSpinner } from "@/shared/UI/components";

interface UserBetHistoryProps {
	userId: string;
}

const UserBetHistory: React.FC<UserBetHistoryProps> = ({ userId }) => {
	// Fetch recent bet history (limit to 6 transactions)
	const {
		data: betHistoryResponse,
		isLoading,
		isError,
		error
	} = useBetHistoryQuery(userId, { size: 6, page: 1 });

	// Safely extract transactions array from response
	const transactions = Array.isArray(betHistoryResponse?.data)
		? betHistoryResponse.data
		: [];

	// Table columns - Updated for new API structure
	const columns: SpkTableColumn[] = useMemo(() => [
		{
			key: "transaction_type",
			title: "Type",
			width: "120px",
			render: (value, _record: BetHistoryTransaction) => {
				// Simplify transaction type display
				const getTypeDisplay = (type: string) => {
					if (type?.includes("credit")) return "Credit";
					if (type?.includes("debit")) return "Debit";
					if (type?.includes("bet")) return "Bet";
					if (type?.includes("win")) return "Win";
					return type?.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()) || "N/A";
				};

				const getTypeColor = (type: string) => {
					if (type?.includes("credit") || type?.includes("win")) return "text-green-600 dark:text-green-400";
					if (type?.includes("debit") || type?.includes("bet")) return "text-blue-600 dark:text-blue-400";
					return "text-gray-600 dark:text-gray-400";
				};

				const getTypeIcon = (type: string) => {
					if (type?.includes("credit") || type?.includes("win")) return "ri-arrow-up-circle-line";
					if (type?.includes("debit") || type?.includes("bet")) return "ri-arrow-down-circle-line";
					return "ri-exchange-line";
				};

				return (
					<div className="flex items-center gap-2">
						<i className={`${getTypeIcon(value)} ${getTypeColor(value)}`}></i>
						<span className={`font-medium ${getTypeColor(value)}`}>
							{getTypeDisplay(value)}
						</span>
					</div>
				);
			}
		},
		{
			key: "transaction_id",
			title: "Transaction ID",
			width: "140px",
			render: (value) => (
				<span className="font-mono text-xs text-gray-600 dark:text-gray-400 truncate">
					{value}
				</span>
			)
		},
		{
			key: "amount",
			title: "Amount",
			width: "120px",
			render: (value, _record: BetHistoryTransaction) => (
				<span className="font-semibold">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "status",
			title: "Status",
			width: "100px",
			render: (value) => {
				const getStatusVariant = (status: string) => {
					switch (status?.toLowerCase()) {
						case "success":
							return "success";
						case "pending":
							return "warning";
						case "cancelled":
						case "failed":
							return "danger";
						default:
							return "secondary";
					}
				};

				return (
					<SpkBadge variant={getStatusVariant(value)} className="text-xs">
						{value?.toUpperCase() || "N/A"}
					</SpkBadge>
				);
			}
		},
		{
			key: "game_provider",
			title: "Provider",
			width: "100px",
			render: (value) => (
				<span className="text-xs text-gray-600 dark:text-gray-400">
					{value || "N/A"}
				</span>
			)
		},
		{
			key: "created_at",
			title: "Date",
			width: "140px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatUserDate(value)}
				</span>
			)
		}
	], []);

	// Loading state
	if (isLoading) {
		return (
			<div className="box h-full flex flex-col">
				<div className="box-header">
					<div className="box-title">
						<i className="ri-history-line me-2"></i>
						Bet History
					</div>
				</div>
				<div className="box-body flex-1 flex items-center justify-center">
					<SpkLoadingSpinner size="lg" />
				</div>
			</div>
		);
	}

	// Error state
	if (isError) {
		return (
			<div className="box h-full flex flex-col">
				<div className="box-header">
					<div className="box-title">
						<i className="ri-history-line me-2"></i>
						Bet History
					</div>
				</div>
				<div className="box-body flex-1 flex items-center justify-center">
					<div className="text-center">
						<i className="ri-error-warning-line text-4xl text-red-500 mb-2"></i>
						<p className="text-gray-600 dark:text-gray-400">
							{error?.message || "Failed to load bet history"}
						</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="box h-full flex flex-col">
			<div className="box-header">
				<div className="flex items-center justify-between w-full">
					<div className="box-title">
						<i className="ri-history-line me-2"></i>
						Bet History
						{transactions.length > 0 && (
							<SpkBadge variant="primary" className="ml-2">
								{transactions.length}
							</SpkBadge>
						)}
					</div>
					<Link
						href={`/user-management/details/${userId}/bet-history`}
						className="ti-btn ti-btn-sm ti-btn-outline-primary"
					>
						<i className="ri-external-link-line me-1"></i>
						{transactions.length > 0 ? "View All" : "View Bet History"}
					</Link>
				</div>
			</div>
			<div className="box-body flex-1">
				{transactions.length === 0 ? (
					<div className="flex items-center justify-center h-full">
						<div className="text-center">
							<i className="ri-history-line text-4xl text-gray-400 mb-2"></i>
							<p className="text-gray-600 dark:text-gray-400">
								No bet history found
							</p>
							<p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
								Recent betting transactions will appear here
							</p>
						</div>
					</div>
				) : (
					<div className="overflow-x-auto">
						<SpkTable
							columns={columns}
							data={transactions}
							loading={false}
							hover={true}
							responsive={false}
							emptyText="No transactions found"
							className="bet-history-table"
							tableClass="table-auto w-full"
							size="sm"
						/>
					</div>
				)}
			</div>
		</div>
	);
};

export default UserBetHistory;
