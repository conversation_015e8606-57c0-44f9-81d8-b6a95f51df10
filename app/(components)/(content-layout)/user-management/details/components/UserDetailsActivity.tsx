import React from "react";
import { UserDetailsData } from "@/shared/types/user-management-types";
import { SpkBadge } from "@/shared/UI/components";
import { formatUserDate, getTimeAgo } from "@/shared/utils/userDetailsUtils";
import { useUserDetails } from "@/shared/hooks";

interface UserDetailsActivityProps {
	userData: UserDetailsData;
}

const UserDetailsActivity: React.FC<UserDetailsActivityProps> = ({ userData }) => {
	// Use custom hook for activities data
	const { activities } = useUserDetails({ userId: userData.id });

	// Add settings activities
	if (userData.setting && userData.setting.length > 0) {
		userData.setting.forEach((setting, _index) => {
			activities.push({
				id: `setting_${setting.id}`,
				type: "setting_updated",
				title: "Setting Updated",
				description: `${setting.key.replace(/([A-Z])/g, " $1").replace(/^./, str => str.toUpperCase())} was updated`,
				timestamp: setting.updatedAt,
				icon: "ri-settings-3-line",
				variant: "info"
			});
		});
	}

	// Sort all activities by timestamp
	activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

	return (
		<div className="box h-full flex flex-col">
			<div className="box-header">
				<div className="box-title">
					<i className="ri-time-line me-2"></i>
					Recent Activity
				</div>
			</div>
			<div className="box-body flex-1">
				{/* Quick Stats - Updated with correct dates, removed Total Wagered Amount */}
				<div className="grid grid-cols-1 gap-4 mb-6">
					{/* Member Since - Use createdAt date */}
					<div className="p-4 bg-success/10 rounded-lg">
						<div className="flex items-center gap-3">
							<div className="w-10 h-10 rounded-full bg-success/20 flex items-center justify-center">
								<i className="ri-calendar-line text-success text-lg"></i>
							</div>
							<div>
								<div className="text-lg font-semibold text-gray-800 dark:text-white">
									{formatUserDate(userData.createdAt)}
								</div>
								<div className="text-sm text-gray-500">Member Since</div>
							</div>
						</div>
					</div>

					{/* Wallet Updated - Use walletUpdatedAt date */}
					<div className="p-4 bg-warning/10 rounded-lg">
						<div className="flex items-center gap-3">
							<div className="w-10 h-10 rounded-full bg-warning/20 flex items-center justify-center">
								<i className="ri-wallet-line text-warning text-lg"></i>
							</div>
							<div>
								<div className="text-lg font-semibold text-gray-800 dark:text-white">
									{formatUserDate(userData.walletupdatedat)}
								</div>
								<div className="text-sm text-gray-500">Wallet Updated</div>
							</div>
						</div>
					</div>

					{/* Profile Updated - Use updatedAt date */}
					<div className="p-4 bg-primary/10 rounded-lg">
						<div className="flex items-center gap-3">
							<div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
								<i className="ri-edit-line text-primary text-lg"></i>
							</div>
							<div>
								<div className="text-lg font-semibold text-gray-800 dark:text-white">
									{formatUserDate(userData.updatedAt)}
								</div>
								<div className="text-sm text-gray-500">Profile Updated</div>
							</div>
						</div>
					</div>

					{/* Account Created - Use createdAt date */}
					<div className="p-4 bg-info/10 rounded-lg">
						<div className="flex items-center gap-3">
							<div className="w-10 h-10 rounded-full bg-info/20 flex items-center justify-center">
								<i className="ri-user-add-line text-info text-lg"></i>
							</div>
							<div>
								<div className="text-lg font-semibold text-gray-800 dark:text-white">
									{formatUserDate(userData.createdAt)}
								</div>
								<div className="text-sm text-gray-500">Account Created</div>
							</div>
						</div>
					</div>
				</div>

				{/* Activity Timeline */}
				<div className="space-y-4">
					<h6 className="text-sm font-semibold text-gray-800 dark:text-white mb-3">
						Activity Timeline
					</h6>

					{activities.length > 0 ? (
						<div className="space-y-3">
							{activities.slice(0, 10).map((activity, _index) => (
								<div key={activity.id} className="flex items-start gap-3">
									<div className={`w-8 h-8 rounded-full bg-${activity.variant}/10 flex items-center justify-center flex-shrink-0 mt-1`}>
										<i className={`${activity.icon} text-${activity.variant} text-sm`}></i>
									</div>
									<div className="flex-1 min-w-0">
										<div className="flex items-center justify-between">
											<h6 className="text-sm font-medium text-gray-800 dark:text-white">
												{activity.title}
											</h6>
											<SpkBadge variant={activity.variant as "primary" | "secondary" | "success" | "danger" | "warning" | "info" | "light" | "dark"} className="text-xs">
												{getTimeAgo(activity.timestamp)}
											</SpkBadge>
										</div>
										<p className="text-xs text-gray-500 mt-1">
											{activity.description}
										</p>
										<div className="text-xs text-gray-400 mt-1">
											{formatUserDate(activity.timestamp)}
										</div>
									</div>
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-6">
							<div className="w-12 h-12 mx-auto mb-3 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
								<i className="ri-time-line text-xl text-gray-400"></i>
							</div>
							<div className="text-sm text-gray-500">No recent activity</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default UserDetailsActivity;
