import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";
import { useModalNavigation } from "@/shared/hooks/ui/useModalNavigation";
import { UserDetailsData } from "@/shared/types/user-management-types";
import { SpkBadge } from "@/shared/UI/components";
import { buildImageUrl, isExternalImage } from "@/shared/utils/imageOptimization";
import Image from "next/image";
import Link from "next/link";
import React from "react";

interface UserDetailsHeaderProps {
	userData: UserDetailsData | null;
}

const UserDetailsHeader: React.FC<UserDetailsHeaderProps> = ({ userData }) => {
	// Get modal navigation functions (must be called before any early returns)
	const { openEditUserModal } = useModalNavigation();

	// Early return if userData is null
	if (!userData) {
		return (
			<div className="bg-elevated rounded-lg border border-border-primary">
				<div className="p-6">
					<div className="animate-pulse">
						<div className="flex flex-col lg:flex-row lg:items-center gap-4">
							<div className="flex items-center gap-4">
								<div className="w-20 h-20 bg-[#404040] rounded-full"></div>
								<div>
									<div className="h-6 bg-[#404040] rounded w-32 mb-2"></div>
									<div className="h-4 bg-[#333333] rounded w-48"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	const formatDate = (dateString: string) => {
		if (!dateString) return "N/A";
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	};

	const getStatusBadge = (active: boolean) => {
		return (
			<SpkBadge variant={active ? "success" : "danger"}>
				{active ? "Active" : "Inactive"}
			</SpkBadge>
		);
	};

	const getVerificationBadge = (verified: boolean, label: string) => {
		return (
			<SpkBadge variant={verified ? "success" : "warning"} className="me-2">
				{verified ? `${label} Verified` : `${label} Pending`}
			</SpkBadge>
		);
	};

	return (
		<div className="box mb-6">
			<div className="box-body">
				<div className="flex flex-col lg:flex-row lg:items-center gap-4">
					{/* Avatar and Basic Info */}
					<div className="flex items-center gap-4">
						<div className="avatar avatar-xl avatar-rounded">
							{(() => {
								const avatarUrl = buildImageUrl(userData.avatarImage);
								const isDefaultFallback = avatarUrl === '/images/profile.png';

								return !isDefaultFallback ? (
									<Image
										src={avatarUrl}
										alt="User Avatar"
										width={80}
										height={80}
										className="rounded-full object-cover"
										unoptimized={isExternalImage(avatarUrl)} // For external URLs
										onError={() => {
											// Handle image load error gracefully
										}}
									/>
								) : (
									<div className="avatar-title bg-primary/10 text-primary rounded-full text-2xl">
										{userData.id.charAt(0).toUpperCase()}
									</div>
								);
							})()}
						</div>

						<div>
							<h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-1">
								User #{userData.id}
							</h2>
							<div className="flex flex-wrap gap-2 mb-2">
								{getStatusBadge(userData.active)}
								{getVerificationBadge(userData.profileVerified, "Profile")}
								{userData.demo && (
									<SpkBadge variant="info">Demo Account</SpkBadge>
								)}
							</div>
							<div className="text-sm text-gray-600 dark:text-gray-400">
								<div>Parent: {userData.parentemail}</div>
								<div>Created: {formatDate(userData.createdAt)}</div>
							</div>
						</div>
					</div>

					{/* Quick Stats */}
					<div className="flex-1 lg:ml-8">
						<div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
							<div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
								<div className="text-lg font-semibold text-gray-800 dark:text-white">
									{userData.currencycode?.toUpperCase()}
								</div>
								<div className="text-xs text-gray-500">Currency</div>
							</div>

							<div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
								<div className="text-lg font-semibold text-gray-800 dark:text-white">
									{userData.categoryType}
								</div>
								<div className="text-xs text-gray-500">Category</div>
							</div>

							<div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
								<div className="text-lg font-semibold text-gray-800 dark:text-white">
									{userData.referralCode}
								</div>
								<div className="text-xs text-gray-500">Referral Code</div>
							</div>

							<div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
								<div className="text-lg font-semibold text-gray-800 dark:text-white">
									{userData.totalWageredAmount.toLocaleString()}
								</div>
								<div className="text-xs text-gray-500">Total Wagered</div>
							</div>
						</div>
					</div>

					{/* Action Buttons */}
					<div className="flex gap-2 flex-wrap">
						<Link
							href={`/user-management/details/${userData.id}/bet-history`}
							className="ti-btn ti-btn-sm ti-btn-primary ti-btn-wave"
							title="View Bet History"
							style={{ display: "inline-flex", alignItems: "center" }}
						>
							<i className="ri-history-line me-1"></i>
							Bet History
						</Link>

						<Link
							href={`/user-management/details/${userData.id}/financial-report`}
							className="ti-btn ti-btn-sm ti-btn-info ti-btn-wave"
							title="View Financial Report"
						>
							<i className="ri-line-chart-line me-1"></i>
							Financial Report
						</Link>

						<Link
							href={`/user-management/details/${userData.id}/bet-report`}
							className="ti-btn ti-btn-sm ti-btn-success ti-btn-wave"
							title="View Bet Report"
							style={{ display: "inline-flex", alignItems: "center" }}
						>
							<i className="ri-file-chart-line me-1"></i>
							Bet Report
						</Link>

						<button
							onClick={() => openEditUserModal(userData.id)}
							className="ti-btn ti-btn-sm ti-btn-secondary ti-btn-wave"
							title="Edit User"
						>
							<i className="ri-edit-line me-1"></i>
							Edit
						</button>

						<SpkButton
							type="button"
							customClass="ti-btn ti-btn-sm ti-btn-warning"
							title="Send Message"
						>
							<i className="ri-mail-line me-1"></i>
							Message
						</SpkButton>

						<SpkButton
							type="button"
							customClass={`ti-btn ti-btn-sm ${userData.active ? "ti-btn-danger" : "ti-btn-success"}`}
							title={userData.active ? "Deactivate" : "Activate"}
						>
							<i className={userData.active ? "ri-user-forbid-line me-1" : "ri-user-add-line me-1"}></i>
							{userData.active ? "Deactivate" : "Activate"}
						</SpkButton>
					</div>
				</div>
			</div>
		</div>
	);
};

export default UserDetailsHeader;
