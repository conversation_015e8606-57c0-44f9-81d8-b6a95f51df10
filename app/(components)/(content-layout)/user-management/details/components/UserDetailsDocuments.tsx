import React from "react";
import { UserDetailsData } from "@/shared/types/user-management-types";
import { SpkBadge } from "@/shared/UI/components";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

interface UserDetailsDocumentsProps {
	userData: UserDetailsData;
}

const UserDetailsDocuments: React.FC<UserDetailsDocumentsProps> = ({ userData }) => {
	const formatDate = (dateString: string) => {
		if (!dateString) return "N/A";
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	};

	const getDocumentIcon = (type: string) => {
		switch (type.toLowerCase()) {
		case "passport":
			return "ri-passport-line";
		case "id":
		case "identity":
			return "ri-id-card-line";
		case "license":
			return "ri-car-line";
		case "utility":
			return "ri-file-text-line";
		case "bank":
			return "ri-bank-card-line";
		default:
			return "ri-file-line";
		}
	};

	const getStatusVariant = (status: string) => {
		switch (status.toLowerCase()) {
		case "approved":
		case "verified":
			return "success";
		case "pending":
		case "review":
			return "warning";
		case "rejected":
		case "declined":
			return "danger";
		default:
			return "secondary";
		}
	};

	return (
		<div className="box h-full flex flex-col">
			<div className="box-header">
				<div className="box-title">
					<i className="ri-file-list-3-line me-2"></i>
					Documents & KYC
				</div>
				<div className="ms-auto">
					<SpkButton
						type="button"
						customClass="ti-btn ti-btn-sm ti-btn-primary"
						title="Upload Document"
					>
						<i className="ri-upload-line me-1"></i>
						Upload
					</SpkButton>
				</div>
			</div>
			<div className="box-body flex-1">
				{userData.userDocuments && userData.userDocuments.length > 0 ? (
					<div className="space-y-4">
						{userData.userDocuments.map((document) => (
							<div
								key={document.id}
								className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
							>
								<div className="flex items-center gap-3">
									<div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
										<i className={`${getDocumentIcon(document.type)} text-primary text-lg`}></i>
									</div>
									<div>
										<h6 className="text-sm font-semibold text-gray-800 dark:text-white mb-1">
											{document.type.charAt(0).toUpperCase() + document.type.slice(1)} Document
										</h6>
										<div className="text-xs text-gray-500">
											Uploaded: {formatDate(document.uploadedAt)}
										</div>
									</div>
								</div>

								<div className="flex items-center gap-2">
									<SpkBadge variant={getStatusVariant(document.status)}>
										{document.status.charAt(0).toUpperCase() + document.status.slice(1)}
									</SpkBadge>
									<SpkButton
										type="button"
										customClass="ti-btn ti-btn-sm ti-btn-light"
										title="View Document"
									>
										<i className="ri-eye-line"></i>
									</SpkButton>
								</div>
							</div>
						))}
					</div>
				) : (
					<div className="text-center py-8">
						<div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
							<i className="ri-file-list-3-line text-2xl text-gray-400"></i>
						</div>
						<h6 className="text-base font-medium text-gray-600 dark:text-gray-400 mb-2">
							No Documents Uploaded
						</h6>
						<p className="text-sm text-gray-500 mb-4">
							This user hasn't uploaded any documents for verification yet.
						</p>
						<SpkButton
							type="button"
							customClass="ti-btn ti-btn-sm ti-btn-primary"
						>
							<i className="ri-upload-line me-1"></i>
							Upload First Document
						</SpkButton>
					</div>
				)}

				{/* Bonus Information */}
				<div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
					<h6 className="text-sm font-semibold text-gray-800 dark:text-white mb-3">
						Bonus Information
					</h6>

					{userData.bonus && userData.bonus.length > 0 ? (
						<div className="space-y-3">
							{userData.bonus.map((bonus) => (
								<div
									key={bonus.id}
									className="flex items-center justify-between p-3 bg-success/10 rounded-lg"
								>
									<div className="flex items-center gap-3">
										<div className="w-8 h-8 rounded-full bg-success/20 flex items-center justify-center">
											<i className="ri-gift-line text-success"></i>
										</div>
										<div>
											<div className="text-sm font-medium text-gray-800 dark:text-white">
												{bonus.type} Bonus
											</div>
											<div className="text-xs text-gray-500">
												Expires: {formatDate(bonus.expiresAt)}
											</div>
										</div>
									</div>
									<div className="text-right">
										<div className="text-sm font-semibold text-success">
											${bonus.amount.toLocaleString()}
										</div>
										<SpkBadge variant={getStatusVariant(bonus.status)} className="text-xs">
											{bonus.status}
										</SpkBadge>
									</div>
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
							<i className="ri-gift-line text-2xl text-gray-400 mb-2"></i>
							<div className="text-sm text-gray-500">No active bonuses</div>
						</div>
					)}

					{/* Active Bonus */}
					{userData.activeBonus && (
						<div className="mt-4 p-4 bg-primary/10 rounded-lg border border-primary/20">
							<div className="flex items-center gap-2 mb-2">
								<i className="ri-star-line text-primary"></i>
								<span className="text-sm font-semibold text-primary">Active Bonus</span>
							</div>
							<div className="flex justify-between items-center">
								<div>
									<div className="text-sm font-medium text-gray-800 dark:text-white">
										{userData.activeBonus.type} Bonus
									</div>
									<div className="text-xs text-gray-500">
										Expires: {formatDate(userData.activeBonus.expiresAt)}
									</div>
								</div>
								<div className="text-right">
									<div className="text-lg font-semibold text-primary">
										${userData.activeBonus.amount.toLocaleString()}
									</div>
									<SpkBadge variant="primary" className="text-xs">
										{userData.activeBonus.status}
									</SpkBadge>
								</div>
							</div>
						</div>
					)}
				</div>

				{/* Document Summary */}
				<div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
					<div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
						<div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
							<div className="text-sm font-semibold text-gray-800 dark:text-white">
								{userData.userDocuments?.length || 0}
							</div>
							<div className="text-xs text-gray-500">Total Documents</div>
						</div>

						<div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
							<div className="text-sm font-semibold text-gray-800 dark:text-white">
								{userData.bonus?.length || 0}
							</div>
							<div className="text-xs text-gray-500">Total Bonuses</div>
						</div>

						<div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
							<div className="text-sm font-semibold text-gray-800 dark:text-white">
								{userData.activeBonus ? "1" : "0"}
							</div>
							<div className="text-xs text-gray-500">Active Bonus</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default UserDetailsDocuments;
