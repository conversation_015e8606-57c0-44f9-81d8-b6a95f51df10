// app/(components)/(content-layout)/user-management/deactivate/[id]/page.tsx
import React from 'react';
import DeactivateUserPageClient from './DeactivateUserPageClient';

interface DeactivateUserPageProps {
  params: Promise<{
    id: string;
  }>;
}

/**
 * Deactivate User Redirect Route
 *
 * This route handles direct navigation to /user-management/deactivate/[id]
 * and redirects to the user management page with modal parameters.
 *
 * This maintains backward compatibility for bookmarks and direct links
 * while using the new global modal system.
 */
export default async function DeactivateUserPage({ params }: DeactivateUserPageProps) {
  const { id } = await params;
  return <DeactivateUserPageClient userId={id} />;
}
