// app/(components)/(authentication-layout)/authentication/sign-in/page.tsx - Server-side rendered sign-in with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateAuthMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import {SignInPageClient} from "./components/SignInPageClient";

// Generate metadata for the sign-in page (server-side)
export const metadata: Metadata = generateAuthMetadata("signIn");

// Revalidate authentication pages every hour (3600 seconds)
export const revalidate = 3600;

interface SignInProps { }

/**
 * Server-side rendered sign-in page component
 * Handles SEO metadata generation and delegates client-side logic to SignInPageClient
 */
const SignIn: React.FC<SignInProps> = () => {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Sign In" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="User Authentication System"
				description="Secure user authentication and login system with two-factor authentication support"
				url="/authentication/sign-in"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<SignInPageClient />
		</>
	);
};

export default SignIn;
