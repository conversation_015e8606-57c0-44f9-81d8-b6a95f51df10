// app/(components)/(authentication-layout)/authentication/sign-in/layout.tsx - Server-side layout for sign-in with SEO
import React from "react";
import type { Metadata } from "next";
import { generateAuthMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData 
} from "@/shared/seo/components/StructuredData";

interface SignInLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for sign-in page (server-side)
export const metadata: Metadata = generateAuthMetadata("signIn");

/**
 * Server-side layout component for sign-in page
 * Provides SEO optimization and structured data for the sign-in page
 */
export default function SignInLayout({ children }: SignInLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Sign In" }
				]}
			/>
			
			{children}
		</>
	);
}
