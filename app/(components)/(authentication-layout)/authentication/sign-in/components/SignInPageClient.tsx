"use client";

import React, { FC } from "react";
import { useSignIn, useTwoStepVerificationLogic } from "@/shared/hooks";
import { UnifiedAuthenticationUI } from "@/shared/UI/components";

interface SignInProps { }

export const SignInPageClient: FC<SignInProps> = () => {
	// Use the custom hook for all business logic
	const signInProps = useSignIn();
	const twoStepProps = useTwoStepVerificationLogic();

	// Render nothing if already fully authenticated and hydrated (useEffect will redirect)
	if (signInProps.hasHydrated && signInProps.isAuthenticated) {
		return null;
	}

	// If 2-step verification is required, show the 2-step form instead of redirecting
	if (signInProps.hasHydrated && signInProps.twoStepVerificationRequired) {
		return (
			<UnifiedAuthenticationUI
				pageType="2-step-verification"
				isPending={twoStepProps.isPending}
				isError={twoStepProps.isError}
				error={twoStepProps.error}
				hasHydrated={signInProps.hasHydrated}
				backgroundImages={signInProps.backgroundImages}
				inputValues={twoStepProps.inputValues}
				setRef={twoStepProps.setRef}
				handleChange={twoStepProps.handleChange}
				handleKeyDown={twoStepProps.handleKeyDown}
				handlePaste={twoStepProps.handlePaste}
				handleVerifyPin={twoStepProps.handleVerifyPin}
				PIN_LENGTH={twoStepProps.PIN_LENGTH}
				tempUserFor2FA={twoStepProps.tempUserFor2FA}
				loginMessage={twoStepProps.loginMessage}
			/>
		);
	}

	// Pass all props to the unified component with sign-in page type
	return <UnifiedAuthenticationUI pageType="sign-in" {...signInProps} />;
};

