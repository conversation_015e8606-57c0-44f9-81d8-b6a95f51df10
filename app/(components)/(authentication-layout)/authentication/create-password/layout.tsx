// app/(components)/(authentication-layout)/authentication/create-password/layout.tsx - Server-side layout for create-password with SEO
import React from "react";
import type { Metadata } from "next";
import {
	BreadcrumbStructuredData
} from "@/shared/seo/components/StructuredData";

interface CreatePasswordLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for create-password page (server-side)
export const metadata: Metadata = {
	title: "Create Password - Secure Account Setup",
	description: "Create a secure password for your Xintra account to protect your data and ensure safe access to the platform.",
	keywords: ["create password", "account setup", "security", "password creation", "account protection"],
	robots: {
		index: false,
		follow: false,
	},
};

/**
 * Server-side layout component for create-password page
 * Provides SEO optimization and structured data for the create-password page
 */
export default function CreatePasswordLayout({ children }: CreatePasswordLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Create Password" }
				]}
			/>

			{children}
		</>
	);
}
