// app/(components)/(authentication-layout)/authentication/sign-up/page.tsx - Server-side rendered sign-up with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateAuthMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { SignUpPageClient } from "./components/SignUpPageClient";

// Generate metadata for the sign-up page (server-side)
export const metadata: Metadata = generateAuthMetadata("signUp");

interface SignUpProps { }

/**
 * Server-side rendered sign-up page component
 * Handles SEO metadata generation and delegates client-side logic to SignUpPageClient
 */
const SignUp: React.FC<SignUpProps> = () => {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Sign Up" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="User Registration System"
				description="Secure user registration and account creation system with social login options"
				url="/authentication/sign-up"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<SignUpPageClient />
		</>
	);
};

export default SignUp;

