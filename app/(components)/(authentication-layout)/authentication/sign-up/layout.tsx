// app/(components)/(authentication-layout)/authentication/sign-up/layout.tsx - Server-side layout for sign-up with SEO
import React from "react";
import type { Metadata } from "next";
import { generateAuthMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData 
} from "@/shared/seo/components/StructuredData";

interface SignUpLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for sign-up page (server-side)
export const metadata: Metadata = generateAuthMetadata("signUp");

/**
 * Server-side layout component for sign-up page
 * Provides SEO optimization and structured data for the sign-up page
 */
export default function SignUpLayout({ children }: SignUpLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Sign Up" }
				]}
			/>
			
			{children}
		</>
	);
}
