// app/(components)/(authentication-layout)/authentication/sign-up/components/SignUpPageClient.tsx - Client-side component for sign-up
"use client";

import React, { Fragment, useState } from "react";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";
import Link from "next/link";
import Image from "next/image";

/**
 * Client-side component that handles all interactive functionality for sign-up
 * Separated from the server component to maintain SSR SEO benefits
 */
export function SignUpPageClient() {
	const [passwordshow, setpasswordshow] = useState(false);
	const [passwordshow1, setpasswordshow1] = useState(false);

	return (
		<Fragment>
			<div className="container">
				<div className="flex justify-center items-center authentication authentication-basic h-full">
					<div className="xl:max-w-[41.66666667%] md:max-w-[60%]">
						<div className="box my-4">
							<div className="box-body !p-[3rem]">
								<div className="mb-4 flex justify-center">
									<Link href="/dashboard">
										<Image
											src="/assets/images/brand-logos/desktop-logo.png"
											alt="Desktop Logo"
											width={150}
											height={40}
											className="desktop-logo"
											priority
										/>
										<Image
											src="/assets/images/brand-logos/desktop-white.png"
											alt="Desktop Logo White"
											width={150}
											height={40}
											className="desktop-white"
											priority
										/>
									</Link>
								</div>
								<p className="h5 mb-2 text-center">Sign Up</p>
								<p className="mb-4 text-textmuted dark:text-textmuted/50 opacity-70 font-normal text-center">Welcome! Begin by creating your account.</p>
								<div className="flex mb-3 justify-between gap-2 flex-wrap flex-lg-nowrap">
									<SpkButton variant="light" Size="lg" customClass="ti-btn border border-defaultborder dark:border-defaultborder/10 flex items-center justify-center flex-auto">
										<span className="avatar avatar-xs">
											<Image src="/assets/images/media/apps/google.png" alt="Google" width={20} height={20} />
										</span>
										<span className="leading-[1.2rem] ms-2 text-[13px] text-defaulttextcolor">Signup with Google</span>
									</SpkButton>
								</div>
								<div className="text-center my-3 authentication-barrier">
									<span>OR</span>
								</div>
								<div className="grid grid-cols-12 gap-y-4">
									<div className="xl:col-span-12 col-span-12">
										<label htmlFor="signup-firstname" className="form-label text-default">First Name</label>
										<input type="text" className="form-control" id="signup-firstname" placeholder="first name" />
									</div>
									<div className="xl:col-span-12 col-span-12">
										<label htmlFor="signup-lastname" className="form-label text-default">Last Name</label>
										<input type="text" className="form-control" id="signup-lastname" placeholder="last name" />
									</div>
									<div className="xl:col-span-12 col-span-12">
										<label htmlFor="signup-password" className="form-label text-default">Password</label>
										<div className="input-group">
											<input type={passwordshow ? "text" : "password"} className="form-control" id="signup-password" placeholder="password" />
											<button onClick={() => setpasswordshow(!passwordshow)} className="ti-btn ti-btn-light" type="button" id="button-addon2"><i className={`${passwordshow ? "ri-eye-line" : "ri-eye-off-line"} align-middle`}></i></button>
										</div>
									</div>
									<div className="xl:col-span-12 col-span-12">
										<label htmlFor="signup-confirmpassword" className="form-label text-default">Confirm Password</label>
										<div className="input-group">
											<input type={passwordshow1 ? "text" : "password"} className="form-control" id="signup-confirmpassword" placeholder="confirm password" />
											<button onClick={() => setpasswordshow1(!passwordshow1)} className="ti-btn ti-btn-light" type="button" id="button-addon21"><i className={`${passwordshow1 ? "ri-eye-line" : "ri-eye-off-line"} align-middle`}></i></button>
										</div>
									</div>
									<div className="xl:col-span-12 col-span-12">
										<div className="form-check mt-3">
											<input className="form-check-input" type="checkbox" value="" id="defaultCheck1" />
											<label className="form-check-label text-textmuted dark:text-textmuted/50 font-normal" htmlFor="defaultCheck1">
												By creating a account you agree to our <Link href="/pages/terms-conditions" className="text-success"><u>Terms & Conditions</u></Link> and <Link className="text-success" href="/pages/privacy-policy"><u>Privacy Policy</u></Link>
											</label>
										</div>
									</div>
								</div>
								<div className="grid mt-4">
									<Link className="ti-btn ti-btn-primary" href="/dashboard">Create Account</Link>
								</div>
								<div className="text-center">
									<p className="text-textmuted dark:text-textmuted/50 mt-3 mb-0">Already have an account? <Link scroll={false} href="/authentication/sign-in/basic" className="text-primary">Sign In</Link></p>
								</div>
								<div className="btn-list text-center mt-3">
									<SpkButton variant="soft-primary" customClass="ti-btn ti-btn-icon btn-wave">
										<i className="ri-facebook-line leading-none align-center text-[17px]"></i>
									</SpkButton>
									<SpkButton variant="soft-primary1" customClass="ti-btn ti-btn-icon btn-wave">
										<i className="ri-twitter-x-line leading-none align-center text-[17px]"></i>
									</SpkButton>
									<SpkButton variant="soft-primary2" customClass="ti-btn ti-btn-icon btn-wave">
										<i className="ri-instagram-line leading-none align-center text-[17px]"></i>
									</SpkButton>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</Fragment>
	);
}
