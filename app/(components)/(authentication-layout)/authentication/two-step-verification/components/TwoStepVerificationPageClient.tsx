// app/(components)/(authentication-layout)/authentication/two-step-verification/components/TwoStepVerificationPageClient.tsx - Client-side component for two-step verification
"use client";

import React from "react";
import { useTwoStepVerificationLogic } from "@/shared/hooks";
import { UnifiedAuthenticationUI } from "@/shared/UI/components";
import { useAuthenticationBackground } from "@/shared/hooks/ui/useAuthenticationBackground";

/**
 * Client-side component that handles all interactive functionality for two-step verification
 * Separated from the server component to maintain SSR SEO benefits
 */
export function TwoStepVerificationPageClient() {
	const {
		inputValues,
		setRef,
		handleChange,
		handleKeyDown,
		handlePaste,
		handleVerifyPin,
		isPending,
		isError,
		error,
		twoStepVerificationRequired,
		tempUserFor2FA,
		PIN_LENGTH,
		loginMessage,
	} = useTwoStepVerificationLogic();

	// Get background images for authentication wrapper
	const { backgroundImages } = useAuthenticationBackground();

	// Show loading state while checking 2FA requirements
	if (!twoStepVerificationRequired || !tempUserFor2FA) {
		return (
			<div className="flex min-h-screen w-full items-center justify-center bg-background">
				<div className="bg-elevated rounded-lg border border-border-primary p-12 text-center animate-pulse">
					<div className="animate-spin rounded-full h-8 w-8 border-2 border-[#404040] border-t-primary mx-auto"></div>
					<p className="mt-4 text-text-secondary font-rubik">Checking verification requirements...</p>
				</div>
			</div>
		);
	}

	// Pass all props to the unified component with 2-step verification page type
	return (
		<UnifiedAuthenticationUI
			pageType="2-step-verification"
			isPending={isPending}
			isError={isError}
			error={error}
			hasHydrated={true} // Always hydrated at this point since we're in the 2FA flow
			backgroundImages={backgroundImages}
			inputValues={inputValues}
			setRef={setRef}
			handleChange={handleChange}
			handleKeyDown={handleKeyDown}
			handlePaste={handlePaste}
			handleVerifyPin={handleVerifyPin}
			PIN_LENGTH={PIN_LENGTH}
			tempUserFor2FA={tempUserFor2FA}
			loginMessage={loginMessage}
		/>
	);
}
