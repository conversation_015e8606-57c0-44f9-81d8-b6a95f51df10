// app/(components)/(authentication-layout)/authentication/two-step-verification/layout.tsx - Server-side layout for two-step verification with SEO
import React from "react";
import type { Metadata } from "next";
import { 
	BreadcrumbStructuredData 
} from "@/shared/seo/components/StructuredData";

interface TwoStepVerificationLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for two-step verification page (server-side)
export const metadata: Metadata = {
	title: "Two-Step Verification - Secure Account Access",
	description: "Complete two-step verification to securely access your Xintra account with enhanced security protection.",
	keywords: ["two-step verification", "2FA", "security", "authentication", "account protection"],
	robots: {
		index: false,
		follow: false,
	},
};

/**
 * Server-side layout component for two-step verification page
 * Provides SEO optimization and structured data for the two-step verification page
 */
export default function TwoStepVerificationLayout({ children }: TwoStepVerificationLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Two-Step Verification" }
				]}
			/>
			
			{children}
		</>
	);
}
