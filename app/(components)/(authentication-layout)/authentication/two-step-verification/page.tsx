// app/(components)/(authentication-layout)/authentication/two-step-verification/page.tsx - Server-side rendered two-step verification with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateAuthMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { TwoStepVerificationPageClient } from "./components/TwoStepVerificationPageClient";

// Force dynamic rendering for this page since it uses client-side state
export const dynamic = "force-dynamic";

// Generate metadata for the two-step verification page (server-side)
export const metadata: Metadata = generateAuthMetadata("signIn");

interface TwoStepVerificationProps { }

/**
 * Server-side rendered two-step verification page component
 * Handles SEO metadata generation and delegates client-side logic to TwoStepVerificationPageClient
 */
const TwoStepVerification: React.FC<TwoStepVerificationProps> = () => {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Two-Step Verification" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Two-Factor Authentication System"
				description="Secure two-factor authentication verification system for enhanced account security"
				url="/authentication/two-step-verification"
				applicationCategory="SecurityApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<TwoStepVerificationPageClient />
		</>
	);
};

export default TwoStepVerification;
