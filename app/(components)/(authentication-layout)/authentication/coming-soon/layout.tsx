// app/(components)/(authentication-layout)/authentication/coming-soon/layout.tsx - Server-side layout for coming-soon with SEO
import React from "react";
import type { Metadata } from "next";
import { 
	BreadcrumbStructuredData 
} from "@/shared/seo/components/StructuredData";

interface ComingSoonLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for coming-soon page (server-side)
export const metadata: Metadata = {
	title: "Coming Soon - Xintra Platform",
	description: "Exciting new features are coming soon to Xintra. Stay tuned for updates on our advanced user management and analytics platform.",
	keywords: ["coming soon", "new features", "platform updates", "xintra"],
	robots: {
		index: false,
		follow: false,
	},
};

/**
 * Server-side layout component for coming-soon page
 * Provides SEO optimization and structured data for the coming-soon page
 */
export default function ComingSoonLayout({ children }: ComingSoonLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Coming Soon" }
				]}
			/>
			
			{children}
		</>
	);
}
