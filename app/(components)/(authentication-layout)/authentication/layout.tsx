// app/(components)/(authentication-layout)/authentication/layout.tsx - Server-side layout for authentication with SEO
import React from "react";
import type { Metadata } from "next";
import { generateAuthMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData,
	WebsiteStructuredData 
} from "@/shared/seo/components/StructuredData";

interface AuthLayoutProps {
	children: React.ReactNode;
}

// Default metadata for authentication section
export const metadata: Metadata = generateAuthMetadata("signIn");

/**
 * Server-side layout component for authentication section
 * Provides SEO optimization and structured data for authentication pages
 */
export default function AuthLayout({ children }: AuthLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication" }
				]}
			/>
			<WebsiteStructuredData
				name="Xintra Authentication"
				url="/authentication"
				description="Secure authentication system for Xintra platform"
			/>
			
			{children}
		</>
	);
}
