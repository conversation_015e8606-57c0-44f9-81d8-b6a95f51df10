// app/(components)/(authentication-layout)/authentication/under-maintainance/layout.tsx - Server-side layout for under-maintainance with SEO
import React from "react";
import type { Metadata } from "next";
import {
	BreadcrumbStructuredData
} from "@/shared/seo/components/StructuredData";

interface UnderMaintainanceLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for under-maintainance page (server-side)
export const metadata: Metadata = {
	title: "Under Maintenance - Xintra Platform",
	description: "Xintra platform is currently under maintenance. We're working to improve your experience and will be back shortly.",
	keywords: ["maintenance", "system update", "temporary unavailable", "xintra"],
	robots: {
		index: false,
		follow: false,
	},
};

/**
 * Server-side layout component for under-maintainance page
 * Provides SEO optimization and structured data for the under-maintainance page
 */
export default function UnderMaintainanceLayout({ children }: UnderMaintainanceLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Under Maintenance" }
				]}
			/>

			{children}
		</>
	);
}
