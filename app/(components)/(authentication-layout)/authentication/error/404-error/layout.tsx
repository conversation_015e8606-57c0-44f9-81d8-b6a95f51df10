// app/(components)/(authentication-layout)/authentication/error/404-error/layout.tsx - Server-side layout for 404 error with SEO
import React from "react";
import type { Metadata } from "next";
import { generateErrorMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData 
} from "@/shared/seo/components/StructuredData";

interface Error404LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for 404 error page (server-side)
export const metadata: Metadata = generateErrorMetadata("404");

/**
 * Server-side layout component for 404 error page
 * Provides SEO optimization and structured data for the 404 error page
 */
export default function Error404Layout({ children }: Error404LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "404 Error" }
				]}
			/>
			
			{children}
		</>
	);
}
