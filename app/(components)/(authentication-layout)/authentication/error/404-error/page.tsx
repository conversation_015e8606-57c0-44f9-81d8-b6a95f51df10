// app/(components)/(authentication-layout)/authentication/error/404-error/page.tsx - Server-side rendered 404 error with SEO optimization
import Link from "next/link";
import React, { Fragment } from "react";
import type { Metadata } from "next";
import { generateErrorMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";

// Generate metadata for the 404 error page (server-side)
export const metadata: Metadata = generateErrorMetadata("404");

interface Error404Props { }

/**
 * Server-side rendered 404 error page component
 * Handles SEO metadata generation with proper error page optimization
 */
const Error404: React.FC<Error404Props> = () => {
	return (
		<Fragment>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "404 Error" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Error Page - 404 Not Found"
				description="Page not found error with helpful navigation options to return to the main application"
				url="/authentication/error/404-error"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			<div className="page error-bg">
				{/* { <!-- Start::error-page -->  */}
				<div className="error-page">
					<div className="container">
						<div className="my-auto">
							<div className="grid grid-cols-11 items-center justify-center h-full">
								<div className="xl:col-span-2 lg:col-span-2 md:col-span-1 col-span-12"></div>
								<div className="xl:col-span-7 lg:col-span-7 md:col-span-9 col-span-12 px-2">
									<p className="error-text mb-4">404</p>
									<p className="text-[1.5rem] font-normal mb-2">Oops, the page you are trying to access does not exist ?</p>
									<p className="text-[15px] mb-[3rem] text-textmuted dark:text-textmuted/50">The requested page is not available. It might have been relocated, deleted, or never existed.</p>
									<Link href="/dashboard" className="ti-btn ti-btn-primary"><i className="ri-arrow-left-line align-middle me-1 inline-block"></i> BACK TO HOME PAGE</Link>
								</div>
								<div className="xl:col-span-2 lg:col-span-2 md:col-span-1 col-span-12"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</Fragment>
	);
};

export default Error404;
