// app/(components)/(authentication-layout)/authentication/error/500-error/layout.tsx - Server-side layout for 500 error with SEO
import React from "react";
import type { Metadata } from "next";
import { generateErrorMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData 
} from "@/shared/seo/components/StructuredData";

interface Error500LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for 500 error page (server-side)
export const metadata: Metadata = generateErrorMetadata("500");

/**
 * Server-side layout component for 500 error page
 * Provides SEO optimization and structured data for the 500 error page
 */
export default function Error500Layout({ children }: Error500LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "500 Error" }
				]}
			/>
			
			{children}
		</>
	);
}
