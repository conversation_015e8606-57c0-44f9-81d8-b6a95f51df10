// app/(components)/(authentication-layout)/authentication/error/500-error/page.tsx - Server-side rendered 500 error with SEO optimization
import Link from "next/link";
import React, { Fragment } from "react";
import type { Metadata } from "next";
import { generateErrorMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";

// Generate metadata for the 500 error page (server-side)
export const metadata: Metadata = generateErrorMetadata("500");

interface Error500Props { }

/**
 * Server-side rendered 500 error page component
 * Handles SEO metadata generation with proper error page optimization
 */
const Error500: React.FC<Error500Props> = () => {
	return (
		<Fragment>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "500 Error" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Error Page - 500 Internal Server Error"
				description="Internal server error page with helpful navigation options to return to the main application"
				url="/authentication/error/500-error"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			<div className="page error-bg">
				{/* <!-- Start::error-page --> */}
				<div className="error-page">
					<div className="container">
						<div className="my-auto">
							<div className="grid grid-cols-11 items-center justify-center h-full">
								<div className="xl:col-span-2 lg:col-span-2 md:col-span-1 col-span-12"></div>
								<div className="xl:col-span-7 lg:col-span-7 md:col-span-9 col-span-12">
									<p className="error-text mb-4">500</p>
									<p className="text-[1.5rem] font-normal mb-2">Oops, something went wrong on our end!</p>
									<p className="text-[15px] mb-[3rem] text-textmuted dark:text-textmuted/50">We're experiencing technical difficulties. Our team has been notified and is working to resolve the issue.</p>
									<Link href="/dashboard" className="ti-btn ti-btn-primary"><i className="ri-arrow-left-line align-middle me-1 inline-block"></i> BACK TO HOME PAGE</Link>
								</div>
								<div className="xl:col-span-2 lg:col-span-2 md:col-span-1 col-span-12"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</Fragment>
	);
};

export default Error500;
