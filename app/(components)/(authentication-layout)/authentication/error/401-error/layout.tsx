// app/(components)/(authentication-layout)/authentication/error/401-error/layout.tsx - Server-side layout for 401 error with SEO
import React from "react";
import type { Metadata } from "next";
import { generateErrorMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData
} from "@/shared/seo/components/StructuredData";

interface Error401LayoutProps {
	children: React.ReactNode;
}

// Generate metadata for 401 error page (server-side)
export const metadata: Metadata = generateErrorMetadata("401");

/**
 * Server-side layout component for 401 error page
 * Provides SEO optimization and structured data for the 401 error page
 */
export default function Error401Layout({ children }: Error401LayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "401 Error" }
				]}
			/>

			{children}
		</>
	);
}
