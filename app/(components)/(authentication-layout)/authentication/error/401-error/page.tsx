// app/(components)/(authentication-layout)/authentication/error/401-error/page.tsx - Server-side rendered 401 error with SEO optimization
import Link from "next/link";
import React, { Fragment } from "react";
import type { Metadata } from "next";
import { generateErrorMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";

// Generate metadata for the 401 error page (server-side)
export const metadata: Metadata = generateErrorMetadata("401");

interface Error401Props { }

/**
 * Server-side rendered 401 error page component
 * Handles SEO metadata generation with proper error page optimization
 */
const Error401: React.FC<Error401Props> = () => {
	return (
		<Fragment>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "401 Error" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Error Page - 401 Unauthorized"
				description="Unauthorized access error page with authentication options to access the application"
				url="/authentication/error/401-error"
				applicationCategory="BusinessApplication"
				operatingSystem="Web Browser"
			/>

			<div className="page error-bg">
				{/* <!-- Start::error-page --> */}
				<div className="error-page">
					<div className="container">
						<div className="my-auto">
							<div className="grid grid-cols-11 items-center justify-center h-full">
								<div className="xl:col-span-2 lg:col-span-2 md:col-span-1 col-span-12"></div>
								<div className="xl:col-span-7 lg:col-span-7 md:col-span-9 col-span-12 px-2">
									<p className="error-text mb-4">401</p>
									<p className="text-[1.5rem] font-normal mb-2">Access Denied - Authentication Required</p>
									<p className="text-[15px] mb-[3rem] text-textmuted dark:text-textmuted/50">You need to be authenticated to access this resource. Please sign in to continue.</p>
									<div className="flex gap-3">
										<Link href="/authentication/sign-in" className="ti-btn ti-btn-primary"><i className="ri-login-box-line align-middle me-1 inline-block"></i> SIGN IN</Link>
										<Link href="/dashboard" className="ti-btn ti-btn-secondary"><i className="ri-arrow-left-line align-middle me-1 inline-block"></i> BACK TO HOME</Link>
									</div>
								</div>
								<div className="xl:col-span-2 lg:col-span-2 md:col-span-1 col-span-12"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</Fragment>
	);
};

export default Error401;
