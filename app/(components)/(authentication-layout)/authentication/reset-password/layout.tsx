// app/(components)/(authentication-layout)/authentication/reset-password/layout.tsx - Server-side layout for reset-password with SEO
import React from "react";
import type { Metadata } from "next";
import { generateAuthMetadata } from "@/shared/seo";
import { 
	BreadcrumbStructuredData 
} from "@/shared/seo/components/StructuredData";

interface ResetPasswordLayoutProps {
	children: React.ReactNode;
}

// Generate metadata for reset-password page (server-side)
export const metadata: Metadata = generateAuthMetadata("forgotPassword");

/**
 * Server-side layout component for reset-password page
 * Provides SEO optimization and structured data for the reset-password page
 */
export default function ResetPasswordLayout({ children }: ResetPasswordLayoutProps) {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Reset Password" }
				]}
			/>
			
			{children}
		</>
	);
}
