// app/(components)/(authentication-layout)/authentication/reset-password/page.tsx - Server-side rendered reset password with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generateAuthMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	SoftwareApplicationStructuredData
} from "@/shared/seo/components/StructuredData";
import { ResetPasswordPageClient } from "./components/ResetPasswordPageClient";

// Generate metadata for the reset password page (server-side)
export const metadata: Metadata = generateAuthMetadata("forgotPassword");

interface ResetPasswordProps { }

/**
 * Server-side rendered reset password page component
 * Handles SEO metadata generation and delegates client-side logic to ResetPasswordPageClient
 */
const ResetPassword: React.FC<ResetPasswordProps> = () => {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home", url: "/" },
					{ name: "Authentication", url: "/authentication" },
					{ name: "Reset Password" }
				]}
			/>
			<SoftwareApplicationStructuredData
				name="Password Reset System"
				description="Secure password reset and recovery system for user account management"
				url="/authentication/reset-password"
				applicationCategory="SecurityApplication"
				operatingSystem="Web Browser"
			/>

			{/* Client-side component handles all interactive functionality */}
			<ResetPasswordPageClient />
		</>
	);
};

export default ResetPassword;
