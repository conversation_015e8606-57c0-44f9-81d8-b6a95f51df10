"use client";

import * as switcherdata from "@/shared/data/switcherdata/switcherdata";
import { Initialload } from "@/shared/layouts-components/contextapi";
import { useUIStore } from "@/shared/stores/uiStore"; // Corrected path to shared/stores/uiStore
import { applyToHtml } from "@/shared/utils";
import React, { Fragment, useContext, useEffect, useMemo, useState, useRef } from "react"; // Added useMemo and useRef

const MainLayout = ({ children }: { children: React.ReactNode }) => {

	// Only subscribe to specific UI state properties to prevent excessive re-renders
	const colorPrimaryRgb = useUIStore((state) => state.colorPrimaryRgb);
	const PrimaryRgb = useUIStore((state) => state.PrimaryRgb);
	const bodyBg = useUIStore((state) => state.bodyBg);
	const darkBg = useUIStore((state) => state.darkBg);
	const lightRgb = useUIStore((state) => state.lightRgb);
	const gray = useUIStore((state) => state.gray);
	const inputBorder = useUIStore((state) => state.inputBorder);
	const ThemeChanger = useUIStore((state) => state.ThemeChanger);

	// Subscribe to properties needed for applyToHtml
	const dir = useUIStore((state) => state.dir);
	const class_ = useUIStore((state) => state.class);
	const dataMenuStyles = useUIStore((state) => state.dataMenuStyles);
	const dataNavLayout = useUIStore((state) => state.dataNavLayout);
	const dataHeaderStyles = useUIStore((state) => state.dataHeaderStyles);
	const dataVerticalStyle = useUIStore((state) => state.dataVerticalStyle);
	const toggled = useUIStore((state) => state.toggled);
	const dataNavStyle = useUIStore((state) => state.dataNavStyle);
	const dataPageStyle = useUIStore((state) => state.dataPageStyle);
	const dataWidth = useUIStore((state) => state.dataWidth);
	const dataMenuPosition = useUIStore((state) => state.dataMenuPosition);
	const dataHeaderPosition = useUIStore((state) => state.dataHeaderPosition);
	const iconOverlay = useUIStore((state) => state.iconOverlay);
	const bgImg = useUIStore((state) => state.bgImg);
	const iconText = useUIStore((state) => state.iconText);
	const [isHydrated, setIsHydrated] = useState(false);
	const [isInitialized, setIsInitialized] = useState(false);
	const themeChangerRef = useRef(ThemeChanger);

	// Update ref when ThemeChanger changes
	useEffect(() => {
		themeChangerRef.current = ThemeChanger;
	}, [ThemeChanger]);

	// CSS Custom Properties for inline style attribute
	// These will be applied to the <html> element's style.
	// Only set custom properties if they differ from our layered background defaults
	const customstyles = useMemo(() => {
		const layeredDefaults = {
			bodyBg: "15 15 15",              // Layer 1: Main body background (#0F0F0F) - NEW DARK THEME
			darkBg: "29 29 29",              // Layer 2: Navigation background (#1D1D1D) - NEW DARK THEME
			lightRgb: "29 29 29",            // Layer 2: Navigation background (#1D1D1D) - NEW DARK THEME
		};

		return {
			...(colorPrimaryRgb && { "--primary-rgb": colorPrimaryRgb }),
			...(PrimaryRgb && { "--primary": PrimaryRgb }),
			// Only set CSS custom properties if they differ from layered background defaults
			...(bodyBg && bodyBg !== layeredDefaults.bodyBg && { "--body-bg": bodyBg }),
			...(darkBg && darkBg !== layeredDefaults.darkBg && { "--dark-bg": darkBg }),
			...(lightRgb && lightRgb !== layeredDefaults.lightRgb && { "--light": lightRgb }),
			...(gray && { "--gray-3": gray }),
			...(inputBorder && { "--input-border": inputBorder }),
		};
	}, [
		colorPrimaryRgb,
		PrimaryRgb,
		bodyBg,
		darkBg,
		lightRgb,
		gray,
		inputBorder,
	]);

	const theme: any = useContext(Initialload);

	// Handle hydration
	useEffect(() => {
		setIsHydrated(true);
	}, []);

	useEffect(() => {
		// Only run once after hydration to prevent infinite re-renders
		if (isHydrated && !isInitialized && theme?.setPageLoading) {
			switcherdata.LocalStorageBackup(themeChangerRef.current, theme.setPageLoading);
			switcherdata.HorizontalClick(themeChangerRef.current);
			setIsInitialized(true);
		}
	}, [isHydrated, isInitialized, theme?.setPageLoading]);

	useEffect(() => {
		// Only apply styles after hydration
		if (isHydrated) {
			// Create a complete uiState object with all required properties for applyToHtml
			const uiStateForApply = {
				dir,
				class: class_,
				dataMenuStyles,
				dataNavLayout,
				dataHeaderStyles,
				dataVerticalStyle,
				toggled,
				dataNavStyle,
				dataPageStyle,
				dataWidth,
				dataMenuPosition,
				dataHeaderPosition,
				iconOverlay,
				bgImg,
				iconText
			};
			applyToHtml({ uiState: uiStateForApply, customstyles });
		}
	}, [isHydrated, dir, class_, dataMenuStyles, dataNavLayout, dataHeaderStyles, dataVerticalStyle, toggled, dataNavStyle, dataPageStyle, dataWidth, dataMenuPosition, dataHeaderPosition, iconOverlay, bgImg, iconText, customstyles]);

	return (
		<Fragment>
			{children}
		</Fragment>
	);
};

export default MainLayout;
