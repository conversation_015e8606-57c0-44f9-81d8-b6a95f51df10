
// app/page.tsx - Server-side rendered home page with SEO optimization
import React from "react";
import type { Metadata } from "next";
import { generatePageMetadata } from "@/shared/seo";
import {
	BreadcrumbStructuredData,
	WebsiteStructuredData,
	OrganizationStructuredData
} from "@/shared/seo/components/StructuredData";
import { HomePageClient } from "./components/HomePageClient";

// Force dynamic rendering for this page since it uses client-side state
export const dynamic = "force-dynamic";

// Generate metadata for the home page (server-side)
export const metadata: Metadata = generatePageMetadata({
	title: "Home",
	description: "Welcome to Xintra - Advanced user management and analytics platform for modern businesses. Secure authentication, comprehensive user management, and powerful analytics tools.",
	keywords: ["user management", "analytics", "business platform", "authentication", "dashboard"],
	canonical: "/",
	openGraph: {
		type: "website",
		url: "/",
		title: "Xintra - Advanced User Management Platform",
		description: "Secure, scalable user management and analytics platform for modern businesses",
		images: [
			{
				url: "/og-image.jpg",
				width: 1200,
				height: 630,
				alt: "Xintra Platform"
			}
		]
	},
	twitter: {
		card: "summary_large_image",
		title: "Xintra - Advanced User Management Platform",
		description: "Secure, scalable user management and analytics platform for modern businesses"
	}
});

/**
 * Server-side rendered home page component
 * Handles SEO metadata generation and delegates client-side logic to HomePageClient
 */
export default function Home() {
	return (
		<>
			{/* Structured data for SEO */}
			<BreadcrumbStructuredData
				breadcrumbs={[
					{ name: "Home" }
				]}
			/>
			<WebsiteStructuredData
				name="Xintra"
				url="https://xintra.com"
				description="Advanced user management and analytics platform for modern businesses"
				searchUrl="https://xintra.com/search?q={search_term_string}"
			/>
			<OrganizationStructuredData
				name="Xintra"
				url="https://xintra.com"
				logo="https://xintra.com/logo.png"
				description="Leading provider of user management and analytics solutions for modern businesses"
				contactPoint={[{
					telephone: "******-0123",
					contactType: "customer service",
					email: "<EMAIL>"
				}]}
				address={{
					streetAddress: "123 Business Ave",
					addressLocality: "Tech City",
					addressRegion: "TC",
					postalCode: "12345",
					addressCountry: "US"
				}}
				sameAs={[
					"https://twitter.com/xintra",
					"https://linkedin.com/company/xintra"
				]}
			/>

			{/* Debug info */}
			<div style={{ position: 'fixed', top: 0, right: 0, background: 'red', color: 'white', padding: '10px', zIndex: 9999 }}>
				HOME PAGE RENDERED
			</div>

			{/* Client-side component handles authentication logic */}
			<HomePageClient />
		</>
	);
}
