"use client";

import React, { useEffect, useState, Suspense } from "react";
import QueryProvider from "@/shared/query/QueryProvider"; // Import your TanStack Query Provider
import { Initialload } from "@/shared/layouts-components/contextapi"; // Assuming this context is still needed
import PrelineScript from "./PrelineScript"; // Your PrelineScript component
import { usePathname } from "next/navigation";
import BodyClassProvider from "./BodyClassProvider";
import { initializeDOMCleaning } from "@/shared/utils/hydrationUtils";

import { Global401ErrorProvider } from "@/shared/providers/Global401ErrorProvider";
import { ToastProvider } from "@/shared/UI/components";
import { AuthInitializer } from "@/shared/components/auth/AuthInitializer";

import { GlobalUserManagementModal, GlobalExportCenterModal, GlobalCheckResultModal } from "@/shared/UI/components";
import GlobalBulkUploadModal from "@/shared/UI/modals/GlobalBulkUploadModal";
import { SessionTimeoutHandler } from "@/shared/stores/SessionTimeoutHandler";
// Debug components removed after fixing re-render issues
// import { RenderTracker, DOMMutationTracker, useRenderTracker } from "@/shared/components/debug/RenderTracker";

interface ClientProvidersProps {
	children: React.ReactNode;
}

export default function ClientProviders({ children }: ClientProvidersProps) {
	const [pageLoading, setPageLoading] = useState<boolean>(false);
	const [backgroundClass, setBackgroundClass] = useState<string>("");
	const pathname = usePathname();

	// Debug tracking removed after fixing re-render issues
	// const renderStats = useRenderTracker('ClientProviders');

	useEffect(() => {
		if (pathname === "/" || pathname.includes("authentication")) {
			setBackgroundClass("authentication-background");
		} else {
			setBackgroundClass("");
		}
	}, [pathname]);

	// Initialize DOM cleaning for browser extension attributes
	useEffect(() => {
		initializeDOMCleaning();
	}, []);
	return (
		<QueryProvider>
			<Global401ErrorProvider
				enableDebugLogging={process.env.NODE_ENV === "development"}
				defaultErrorMessage="Your session has expired or you are not authorized to access this resource. Please sign in again to continue."
				modalTitle="Authentication Required"
			>
				<ToastProvider maxToasts={5}>
					<Initialload.Provider value={{ pageLoading, setPageLoading }}>
						<BodyClassProvider backgroundClass={backgroundClass}>
							<AuthInitializer />
							{children}
							<Suspense>
								<GlobalUserManagementModal />
								<GlobalExportCenterModal />
								<GlobalCheckResultModal />
								<GlobalBulkUploadModal />
								<SessionTimeoutHandler />
							</Suspense>
							<PrelineScript />
						</BodyClassProvider>
					</Initialload.Provider>
				</ToastProvider>
			</Global401ErrorProvider>
		</QueryProvider>
	);
}
