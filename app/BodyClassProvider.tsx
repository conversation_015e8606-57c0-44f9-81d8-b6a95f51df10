"use client";

import React, { useEffect } from "react";

interface BodyClassProviderProps {
	backgroundClass: string;
	children: React.ReactNode;
}

export default function BodyClassProvider({ backgroundClass, children }: BodyClassProviderProps) {
	useEffect(() => {
		// Remove any existing authentication background class
		document.body.classList.remove("authentication-background");

		// Add the background class if provided
		if (backgroundClass) {
			document.body.classList.add(backgroundClass);
		}

		// Cleanup function to remove the class when component unmounts
		return () => {
			if (backgroundClass) {
				document.body.classList.remove(backgroundClass);
			}
		};
	}, [backgroundClass]);

	return <>{children}</>;
}
