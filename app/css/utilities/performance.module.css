/* Performance optimizations to reduce layout shifts and improve rendering */

/* Prevent layout shift when loading */
.userManagementTable {
  min-height: 400px;
}

/* Improve rendering performance */
.box {
  contain: layout;
}

/* Ensure avatars have consistent dimensions */
.avatar {
  flex-shrink: 0;
}

/* Prevent layout shifts from dynamic content */
.mainContent {
  min-height: 100vh;
}

/* Optimize font rendering */
.optimizedFont {
  font-display: swap;
}

/* Global font optimization - apply to all elements */
* {
  font-display: swap;
}

/* Improve button rendering performance */
.tiBtn {
  contain: layout style;
}

/* Prevent layout shifts from images */
.responsiveImage {
  height: auto;
  max-width: 100%;
}

/* Global image optimization */
img {
  height: auto;
  max-width: 100%;
}

/* Improve table rendering performance */
.optimizedTable {
  table-layout: fixed;
}

/* Global table optimization */
table {
  table-layout: fixed;
}

/* Reduce paint complexity */
.boxShadow {
  will-change: transform;
}

/* Optimize dropdown rendering */
.tiDropdownMenu {
  contain: layout;
}

/* Additional performance optimizations */

/* GPU acceleration for animations */
.gpuAccelerated {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize scrolling performance */
.smoothScroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Reduce repaints during animations */
.animationOptimized {
  will-change: opacity, transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize for mobile touch */
.touchOptimized {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Lazy loading optimization */
.lazyLoad {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* Critical rendering path optimization */
.criticalContent {
  contain: layout style paint;
}

/* Memory optimization for large lists */
.virtualizedList {
  contain: strict;
  content-visibility: auto;
}
