/* SignInForm background cycling animations */
@keyframes backgroundCycle {
  0% { opacity: 1; }
  20% { opacity: 1; }
  25% { opacity: 0; }
  95% { opacity: 0; }
  100% { opacity: 1; }
}

/* Individual background animation classes with staggered delays */
.signinBg1 {
  animation: backgroundCycle 20s infinite;
  animation-delay: 0s;
}

.signinBg2 {
  animation: backgroundCycle 20s infinite;
  animation-delay: 5s;
}

.signinBg3 {
  animation: backgroundCycle 20s infinite;
  animation-delay: 10s;
}

.signinBg4 {
  animation: backgroundCycle 20s infinite;
  animation-delay: 15s;
}

/* Base styles for signin background layers */
.signinBackgroundLayer {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 0;
  will-change: opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Dark overlay for improved text readability */
.signinOverlay {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 5;
  pointer-events: none;
}
