/* Fade-in animation for filter sections and general use */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Alternative fade-in variations */
@keyframes fadeInSlow {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeInSlow {
  animation: fadeInSlow 0.6s ease-out;
}

@keyframes fadeInFast {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeInFast {
  animation: fadeInFast 0.15s ease-out;
}
