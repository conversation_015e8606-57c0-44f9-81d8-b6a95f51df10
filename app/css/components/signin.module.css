/* SignIn/Login form specific styles */

/* Main container styles */
.signinContainer {
  display: flex;
  min-height: 100vh;
  width: 100%;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background-color: #0F0F0F;
}

/* Form container with background border */
.formContainer {
  width: 760px;
  height: 430px;
  border-radius: 0.5rem;
  padding-left: 3rem;
  padding-right: 3rem;
  display: flex;
  background-image: url('/assets/login/bgborder.svg');
  background-size: cover;
  background-position: center;
}

/* Logo container */
.logoContainer {
  margin-bottom: 2rem;
}

/* Form section (right side) */
.formSection {
  width: 60%;
  height: 100%;
  margin-left: auto;
  display: flex;
  padding: 1.5rem;
  flex-direction: column;
  justify-content: center;
}

/* Form title */
.formTitle {
  color: #E1B649;
  font-size: 1.5rem;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  text-align: center;
  margin-bottom: 1.5rem;
}

/* Form subtitle */
.formSubtitle {
  color: #D1D5DB;
  font-size: 0.875rem;
  text-align: center;
  margin-bottom: 1.5rem;
}

/* Form styles */
.signinForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Input field overrides for signin theme */
.signinInput {
  background-color: rgba(0, 0, 0, 0.5);
  border-color: rgba(225, 182, 73, 0.5);
  color: white;
}

.signinInput::placeholder {
  color: #9CA3AF;
}

/* Remember me section */
.rememberSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.rememberCheckbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rememberLabel {
  color: #D1D5DB;
  font-size: 0.875rem;
}

/* Forgot password link */
.forgotPassword {
  color: #E1B649;
  font-size: 0.875rem;
  text-decoration: none;
  transition: color 0.2s;
}

.forgotPassword:hover {
  color: #F59E0B;
}

/* Submit button */
.submitButton {
  background-color: #E1B649;
  color: #0F0F0F;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.submitButton:hover {
  background-color: #F59E0B;
}

.submitButton:disabled {
  background-color: #6B7280;
  cursor: not-allowed;
}

/* Loading state */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Error message styles */
.errorMessage {
  color: #EF4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Success message styles */
.successMessage {
  color: #10B981;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .formContainer {
    width: 90%;
    height: auto;
    min-height: 400px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  .formSection {
    width: 100%;
    margin-left: 0;
  }
}
