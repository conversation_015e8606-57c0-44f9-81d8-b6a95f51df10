"use client";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

import { IStaticMethods } from "preline/preline";

declare global {
	interface Window {
		HSStaticMethods: IStaticMethods;
	}
}

export default function PrelineScript() {
	const path = usePathname();
	const [isHydrated, setIsHydrated] = useState(false);

	// Handle hydration
	useEffect(() => {
		setIsHydrated(true);
	}, []);

	useEffect(() => {
		// Only load Preline after hydration to prevent SSR/client mismatch
		if (isHydrated && typeof window !== "undefined") {
			const loadPreline = async () => {
				try {
					await import("preline/preline");
					if (window.HSStaticMethods) {
						window.HSStaticMethods.autoInit();
					}
				} catch {
					// Silently handle Preline loading errors
				}
			};

			loadPreline();
		}
	}, [isHydrated, path]);

	return null;
}
