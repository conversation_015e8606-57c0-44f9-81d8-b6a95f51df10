// app/components/HomePageClient.tsx - Client-side component for home page authentication logic
"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/shared/stores/authStore";

/**
 * Client-side component that handles authentication-based routing
 * Separated from the server component to maintain SSR SEO benefits
 */
export function HomePageClient() {
	const router = useRouter();
	const { isAuthenticated, _hasHydrated } = useAuthStore();

	useEffect(() => {
		// Wait for hydration to complete before making routing decisions
		if (_hasHydrated) {
			if (isAuthenticated) {
				// User is authenticated, redirect to dashboard
				router.push("/dashboard");
			} else {
				// User is not authenticated, redirect to sign-in page
				router.push("/authentication/sign-in");
			}
		}
	}, [_hasHydrated, isAuthenticated, router]);

	// Show loading state while hydrating
	if (!_hasHydrated) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
					<p className="mt-2 text-textmuted">Loading...</p>
				</div>
			</div>
		);
	}

	// This should not be reached due to the useEffect redirect, but just in case
	return <></>;
}
