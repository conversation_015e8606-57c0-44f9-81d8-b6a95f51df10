// app/manifest.ts - Progressive Web App manifest generation
/* eslint-disable camelcase */
import { MetadataRoute } from "next";
import { siteConfig } from "@/shared/seo";

// Revalidate manifest every 30 days (2592000 seconds)
export const revalidate = 2592000;

/**
 * Generate PWA manifest for the application
 * This creates a web app manifest for better mobile experience and SEO
 */
export default function manifest(): MetadataRoute.Manifest {
	return {
		name: siteConfig.name,
		short_name: siteConfig.name,
		description: siteConfig.description,
		start_url: "/",
		display: "standalone",
		background_color: "#ffffff",
		theme_color: siteConfig.themeColor,
		orientation: "portrait-primary",
		scope: "/",
		lang: "en",
		categories: ["business", "productivity", "utilities"],
		icons: [
			{
				src: "/android-chrome-192x192.png",
				sizes: "192x192",
				type: "image/png",
				purpose: "maskable",
			},
			{
				src: "/android-chrome-512x512.png",
				sizes: "512x512",
				type: "image/png",
				purpose: "maskable",
			},
			{
				src: "/apple-touch-icon.png",
				sizes: "180x180",
				type: "image/png",
				purpose: "any",
			},
			{
				src: "/favicon-32x32.png",
				sizes: "32x32",
				type: "image/png",
				purpose: "any",
			},
			{
				src: "/favicon-16x16.png",
				sizes: "16x16",
				type: "image/png",
				purpose: "any",
			},
		],
		// Screenshots removed until actual screenshots are available
		// screenshots: [
		// 	{
		// 		src: "/screenshots/desktop-dashboard.png",
		// 		sizes: "1280x720",
		// 		type: "image/png",
		// 		form_factor: "wide",
		// 		label: "Dashboard view on desktop",
		// 	},
		// 	{
		// 		src: "/screenshots/mobile-dashboard.png",
		// 		sizes: "390x844",
		// 		type: "image/png",
		// 		form_factor: "narrow",
		// 		label: "Dashboard view on mobile",
		// 	},
		// ],
		shortcuts: [
			{
				name: "Dashboard",
				short_name: "Dashboard",
				description: "Go to the main dashboard",
				url: "/dashboard",
				icons: [
					{
						src: "/favicon-32x32.png",
						sizes: "32x32",
						type: "image/png",
					},
				],
			},
			{
				name: "User Management",
				short_name: "Users",
				description: "Manage user accounts",
				url: "/user-management",
				icons: [
					{
						src: "/favicon-32x32.png",
						sizes: "32x32",
						type: "image/png",
					},
				],
			},
		],
		related_applications: [],
		prefer_related_applications: false,
	};
}
