// app/sitemap.ts - Dynamic sitemap generation for SEO optimization
import { MetadataRoute } from "next";
import { siteConfig } from "@/shared/seo";

// Revalidate sitemap every 24 hours (86400 seconds)
export const revalidate = 86400;

/**
 * Generate sitemap for the application
 * This function creates a dynamic sitemap that includes all static and dynamic routes
 */
export default function sitemap(): MetadataRoute.Sitemap {
	const baseUrl = siteConfig.url;
	const currentDate = new Date();

	// Static routes with their priorities and change frequencies
	const staticRoutes: MetadataRoute.Sitemap = [
		{
			url: baseUrl,
			lastModified: currentDate,
			changeFrequency: "daily",
			priority: 1.0,
		},
		{
			url: `${baseUrl}/dashboard`,
			lastModified: currentDate,
			changeFrequency: "daily",
			priority: 0.9,
		},
		{
			url: `${baseUrl}/user-management`,
			lastModified: currentDate,
			changeFrequency: "daily",
			priority: 0.8,
		},
		{
			url: `${baseUrl}/user-management/create`,
			lastModified: currentDate,
			changeFrequency: "weekly",
			priority: 0.7,
		},
		{
			url: `${baseUrl}/authentication/sign-in`,
			lastModified: currentDate,
			changeFrequency: "monthly",
			priority: 0.6,
		},
		{
			url: `${baseUrl}/authentication/sign-up`,
			lastModified: currentDate,
			changeFrequency: "monthly",
			priority: 0.6,
		},
		{
			url: `${baseUrl}/authentication/forgot-password`,
			lastModified: currentDate,
			changeFrequency: "monthly",
			priority: 0.5,
		},
	];

	// UI Components and showcase pages (lower priority)
	const uiRoutes: MetadataRoute.Sitemap = [
		{
			url: `${baseUrl}/ui/icons`,
			lastModified: currentDate,
			changeFrequency: "monthly",
			priority: 0.3,
		},
		{
			url: `${baseUrl}/ui/buttons`,
			lastModified: currentDate,
			changeFrequency: "monthly",
			priority: 0.3,
		},
		{
			url: `${baseUrl}/ui/badges`,
			lastModified: currentDate,
			changeFrequency: "monthly",
			priority: 0.3,
		},
	];

	// Error pages (very low priority, but included for completeness)
	const errorRoutes: MetadataRoute.Sitemap = [
		{
			url: `${baseUrl}/authentication/error/404-error`,
			lastModified: currentDate,
			changeFrequency: "yearly",
			priority: 0.1,
		},
		{
			url: `${baseUrl}/authentication/error/500-error`,
			lastModified: currentDate,
			changeFrequency: "yearly",
			priority: 0.1,
		},
	];

	// Combine all routes
	return [
		...staticRoutes,
		...uiRoutes,
		...errorRoutes,
	];
}

/**
 * Generate dynamic user routes for sitemap
 * This would typically fetch user IDs from your database
 * For now, we'll return an empty array as user routes are dynamic
 */
async function _generateUserRoutes(): Promise<MetadataRoute.Sitemap> {
	// In a real application, you would fetch user IDs from your database
	// Example:
	// const users = await fetchUsers();
	// return users.map(user => ({
	//   url: `${siteConfig.url}/user-management/details/${user.id}`,
	//   lastModified: new Date(user.updatedAt),
	//   changeFrequency: 'weekly' as const,
	//   priority: 0.6,
	// }));

	return [];
}

/**
 * Alternative sitemap generation with database integration
 * Uncomment and modify this function when you have database access
 */
/*
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = siteConfig.url;
  const currentDate = new Date();

  // Static routes
  const staticRoutes: MetadataRoute.Sitemap = [
	// ... same as above
  ];

  // Dynamic user routes
  try {
	const userRoutes = await generateUserRoutes();
	return [...staticRoutes, ...userRoutes];
  } catch (error) {
	console.error('Error generating dynamic sitemap routes:', error);
	return staticRoutes;
  }
}
*/
