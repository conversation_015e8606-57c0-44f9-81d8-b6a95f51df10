'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { LKRCurrencyIcon } from '@/shared';

// 🔁 Reusable inline FieldRow component
const FieldRow = ({ label, value, className = '', amount }: { label: string; value: React.ReactNode; className?: string, amount?: boolean }) => {
  return <div className="flex lg:flex-row justify-between lg:gap-0 lg:items-center items-start flex-col gap-2">
    <span className="font-rubik font-medium lg:text-lg text-md leading-none capitalize lg:text-right text-left  text-[#928a7f] lg:text-white">
      {label}:
    </span>
    <div className={`flex  gap-[2px] justify-center font-rubik font-medium lg:text-lg text-sm lg:text-right text-left leading-none capitalize  ${className}`}>
      {amount && <LKRCurrencyIcon
        variant="white"
        size={16}
        aria-hidden={true}
        className="flex-shrink-0"
      />}
      <span className='lg:mt-[0px] mt-[3px]'>
        {value}
      </span>
    </div>
  </div>;
};

// Types...
interface BetDetailsData {
  provider: string;
  marketDetail: { marketId: string; marketName: string; marketStatus: string };
  betDetails: {
    betId: string;
    settlementStatus: string;
    betAmount: number;
    settlementAmount: number;
    createdDate: string;
    payoutStatus: number;
    status: string;
    betQrCode: string;
  };
  betList: Array<{ betId: string; marketName: string; rate: number; stake: number }>;
  customerSupportDetails: { id: string; phone: string; email: string };
}
interface ApiResponse {
  code: number;
  message: string;
  success: number;
  data: BetDetailsData;
}

const BetDetailsContent: React.FC = () => {
  const searchParams = useSearchParams();
  const betId = searchParams.get('bet_id');

  const [betData, setBetData] = useState<ApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBetDetails = async (transactionId: string): Promise<ApiResponse> => {
    const myHeaders = new Headers();
    myHeaders.append("accept", "application/json, text/plain, */*");
    myHeaders.append("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
    myHeaders.append("access-control-allow-origin", "*");
    myHeaders.append("cache-control", "no-cache");
    myHeaders.append("content-type", "application/json");

    const response = await fetch("https://api.ingrandstation.com/turboStars/betResults", {
      method: "POST",
      headers: myHeaders,
      body: JSON.stringify({ providerName: "turbostars", transactionId }),
    });

    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

    const result: ApiResponse = await response.json();
    if (result.success !== 1) throw new Error(result.message || 'Failed to fetch bet details');

    return result;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric', month: 'short', day: 'numeric',
        hour: '2-digit', minute: '2-digit', second: '2-digit',
      });
    } catch (error) {
      //eslint-disable-next-line no-console
      console.error(error);
      return dateString;
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'win': return 'text-green-400';
      case 'loss':
      case 'lose': return 'text-red-400';
      case 'pending': return 'text-yellow-400';
      case 'void': return 'text-gray-400';
      default: return 'text-white';
    }
  };

  const getPayoutStatusText = (payoutStatus: number): string => {
    switch (payoutStatus) {
      case 1: return 'Settled';
      case 2: return 'Unsettled';
      case 3: return 'Settled';
      default: return 'Unknown';
    }
  };

  useEffect(() => {
    if (!betId) {
      setError('Bet ID is required');
      setIsLoading(false);
      return;
    }

    const loadBetDetails = async () => {
      try {
        setIsLoading(true);
        const data = await fetchBetDetails(betId);
        setBetData(data);
      } catch (err) {
        //eslint-disable-next-line no-console
        console.error('Error loading bet details:', err);
        setError(err instanceof Error ? err.message : 'Failed to load bet details');
      } finally {
        setIsLoading(false);
      }
    };

    loadBetDetails();
  }, [betId]);

  if (error) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-center text-white">
          <div className="text-red-400 text-xl mb-4">Error Loading Bet Details</div>
          <div className="text-lg mb-6">{error}</div>
          {!betId && <div className="text-gray-400 text-sm">Please provide a valid bet_id parameter in the URL</div>}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-golden mx-auto mb-4"></div>
          <div className="text-white text-xl">Loading bet details...</div>
        </div>
      </div>
    );
  }

  if (!betData) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-white text-xl">No bet details available</div>
      </div>
    );
  }

  const { data } = betData;

  const marketDetailsFields = [
    { label: 'Market ID', value: data.marketDetail.marketId },
    { label: 'Market Name', value: data.marketDetail.marketName, className: 'lg:max-w-[60%] max-w-[100%]' },
    { label: 'Market Status', value: data.marketDetail.marketStatus },
  ];

  const betDetailsFields = [
    { label: 'Bet ID', value: data.betDetails.betId },
    { label: 'Settlement Status', value: data.betDetails.settlementStatus },
    { label: 'Bet Amount', value: formatCurrency(data.betDetails.betAmount), amount: true },
    { label: 'Settlement Amount', value: formatCurrency(data.betDetails.settlementAmount), amount: true },
    { label: 'Created Date', value: formatDate(data.betDetails.createdDate) },
    { label: 'Status', value: data.betDetails.status, className: getStatusColor(data.betDetails.status) },
    { label: 'Payout Status', value: getPayoutStatusText(data.betDetails.payoutStatus) },
  ];

  const customerSupportFields = [
    { label: 'Support ID', value: data.customerSupportDetails.id },
    { label: 'Phone', value: data.customerSupportDetails.phone },
    { label: 'Email', value: data.customerSupportDetails.email },
  ];

  return (
    <div className="min-h-screen bg-[#0F0F0F] text-white">
      {/* Logo Section */}
      <div className="max-w-4xl mx-auto flex justify-between py-6 gap-2">
        <div className="relative rounded-lg overflow-hidden h-[80px] bg-purple-header-gradient-1">
          <div className="absolute left-[0px] top-1/2 transform -translate-y-1/2">
            <Image src="/bet-slip-icon-print.png" alt="Bet slip print icon" width={70} height={70} />
          </div>
          <div className="relative z-10 flex items-center justify-between h-full px-6">
            <div className="flex-1 pl-10">
              <h1 className="text-white text-2xl font-medium">Betslip</h1>
            </div>
          </div>
        </div>
        <div className="w-[100px] h-[100px] relative">
          <Image src="/assets/images/Golden-Island.webp" alt="Golden Island Logo" fill className="object-contain" priority />
        </div>
      </div>

      {/* Bet Details Content */}
      <div className="max-w-4xl mx-auto mb-5 p-6 pb-8 bg-[#1D1D1F] rounded-lg">
        {/* Market Details */}
        <Section title="Market Details" fields={marketDetailsFields} />

        {/* Bet Details */}
        <Section title="Bet Details" fields={betDetailsFields} />

        {/* Individual Bets */}
        {data.betList.length > 0 && (
          <div className="mb-8">
            <h2 className="font-rubik font-bold text-2xl capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">Individual Bets</h2>
            <div className="space-y-6">
              {data.betList.map((bet, index) => (
                <div key={index} className="bg-surface p-4 rounded-lg">
                  <h3 className="font-rubik font-medium text-lg pb-2 capitalize text-white mb-3 border-b border-[#1D1D1F]">Bet {index + 1}</h3>
                  <div className="space-y-3">
                    {[
                      { label: 'Bet ID', value: bet.betId },
                      { label: 'Market Name', value: bet.marketName, className: 'lg:max-w-[60%] max-w-[100%]' },
                      { label: 'Rate', value: bet.rate },
                      { label: 'Stake', value: formatCurrency(bet.stake), amount: true },
                    ].map((field) => (
                      <FieldRow key={field.label} label={field.label} value={field.value} className={field.className} amount={field?.amount} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Customer Support */}
        <Section title="Customer Support" fields={customerSupportFields} />
      </div>
    </div>
  );
};

// 🔁 Reusable section wrapper
const Section = ({ title, fields }: { title: string; fields: Array<{ label: string; value: React.ReactNode; className?: string, amount?: boolean }> }) => (
  <div className="mb-8">
    <h2 className="font-rubik font-bold text-2xl capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">{title}</h2>
    <div className="space-y-3">
      {fields.map((field) => (
        <FieldRow key={field.label} label={field.label} value={field.value} className={field.className} amount={field?.amount} />
      ))}
    </div>
  </div>
);

const BetDetailsPage: React.FC = () => (
  <Suspense fallback={
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <div className="text-white text-xl">Loading...</div>
      </div>
    </div>
  }>
    <BetDetailsContent />
  </Suspense>
);

export default BetDetailsPage;
