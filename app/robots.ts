// app/robots.ts - Dynamic robots.txt generation
import { MetadataRoute } from "next";
import { siteConfig } from "@/shared/seo";

// Revalidate robots.txt every 7 days (604800 seconds)
export const revalidate = 604800;

/**
 * Generate robots.txt for the application
 * This function creates a dynamic robots.txt file based on environment
 */
export default function robots(): MetadataRoute.Robots {
	const baseUrl = siteConfig.url;
	const isProduction = process.env.NODE_ENV === "production";

	// Production robots.txt - allow all crawling
	if (isProduction) {
		return {
			rules: [
				{
					userAgent: "*",
					allow: "/",
					disallow: [
						"/api/",
						"/authentication/error/",
						"/authentication/under-maintainance",
						"/authentication/coming-soon",
						"/_next/",
						"/admin/",
						"/private/",
						"*.json",
						"*.xml",
						"/user-management/create",
						"/user-management/edit/*",
					],
				},
				{
					userAgent: "GPTBot",
					disallow: "/",
				},
				{
					userAgent: "ChatGPT-User",
					disallow: "/",
				},
				{
					userAgent: "CCBot",
					disallow: "/",
				},
				{
					userAgent: "anthropic-ai",
					disallow: "/",
				},
				{
					userAgent: "Claude-Web",
					disallow: "/",
				},
			],
			sitemap: `${baseUrl}/sitemap.xml`,
			host: baseUrl,
		};
	}

	// Development/staging robots.txt - disallow all crawling
	return {
		rules: [
			{
				userAgent: "*",
				disallow: "/",
			},
		],
		sitemap: `${baseUrl}/sitemap.xml`,
	};
}
