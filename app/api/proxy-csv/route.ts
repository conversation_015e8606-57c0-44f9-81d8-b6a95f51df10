
import { NextRequest } from 'next/server';

export async function GET(req: NextRequest) {
    const { searchParams } = new URL(req.url);
    const file = searchParams.get('file');

    // Basic validation: only allow certain path pattern
    if (!file || !/^tenants\/\d+\/csv\/.+\.csv$/.test(file)) {
        return new Response('Invalid file path', { status: 400 });
    }

    const csvUrl = `https://assets.roylfc.com/${file}`;
    const response = await fetch(csvUrl, {
        headers: {
            Referer: "https://www.ingrandstation.com"
        }
    });

    if (!response.ok || !response.body) {
        return new Response('Failed to fetch CSV', { status: 500 });
    }

    // Extract filename from path for Content-Disposition
    const filename = file.split('/').pop() || 'report.csv';

    const headers = new Headers();
    headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    headers.set('Content-Type', 'text/csv');

    return new Response(response.body, {
        status: 200,
        headers
    });
}
