// app/api/performance/route.ts - Performance metrics API endpoint with caching

import { NextRequest, NextResponse } from "next/server";
import { cacheHeaders } from "@/shared/utils/cache";

/**
 * Performance metrics data structure
 */
interface PerformanceMetrics {
	timestamp: number;
	metrics: {
		serverResponseTime: number;
		databaseQueryTime: number;
		cacheHitRate: number;
		activeUsers: number;
		memoryUsage: number;
		cpuUsage: number;
	};
	buildInfo: {
		version: string;
		buildTime: string;
		environment: string;
	};
}

/**
 * Get actual performance metrics from monitoring services
 */
function getPerformanceMetrics(): PerformanceMetrics {
	return {
		timestamp: Date.now(),
		metrics: {
			serverResponseTime: 0,
			databaseQueryTime: 0,
			cacheHitRate: 0,
			activeUsers: 0,
			memoryUsage: 0,
			cpuUsage: 0,
		},
		buildInfo: {
			version: process.env.npm_package_version || "1.0.0",
			buildTime: process.env.BUILD_TIME || new Date().toISOString(),
			environment: process.env.NODE_ENV || "production",
		},
	};
}

/**
 * GET /api/performance
 * Returns current performance metrics with appropriate caching headers
 */
export async function GET(_request: NextRequest) {
	try {
		// Get performance metrics
		const metrics = getPerformanceMetrics();

		// Create response with performance metrics
		const response = NextResponse.json({
			success: true,
			data: metrics,
			cached: false, // This would be true if served from cache
		});

		// Add cache headers for 30 seconds with 60 seconds stale-while-revalidate
		const headers = cacheHeaders.staleWhileRevalidate(30, 60);
		Object.entries(headers).forEach(([key, value]) => {
			response.headers.set(key, value);
		});

		// Add performance-related headers
		response.headers.set("X-Response-Time", `${Date.now()}ms`);
		response.headers.set("X-Cache-Status", "MISS");
		response.headers.set("X-Server-Region", process.env.VERCEL_REGION || "local");

		return response;
	} catch {
		// Handle performance metrics API error silently

		const errorResponse = NextResponse.json({
			success: false,
			error: "Failed to fetch performance metrics",
		}, { status: 500 });

		// Don't cache error responses
		const headers = cacheHeaders.noCache;
		Object.entries(headers).forEach(([key, value]) => {
			errorResponse.headers.set(key, value);
		});

		return errorResponse;
	}
}

/**
 * POST /api/performance
 * Accepts performance metrics from client-side monitoring
 */
export async function POST(request: NextRequest) {
	try {
		const body = await request.json();

		// Validate the incoming performance data
		if (!body.metrics || typeof body.metrics !== "object") {
			return NextResponse.json({
				success: false,
				error: "Invalid metrics data",
			}, { status: 400 });
		}

		// Store metrics in database and send to monitoring services
		// Implementation would go here for production use

		const response = NextResponse.json({
			success: true,
			message: "Performance metrics recorded",
			timestamp: Date.now(),
		});

		// Don't cache POST responses
		const headers = cacheHeaders.noCache;
		Object.entries(headers).forEach(([key, value]) => {
			response.headers.set(key, value);
		});

		return response;
	} catch {
		// Handle performance metrics POST error silently

		return NextResponse.json({
			success: false,
			error: "Failed to record performance metrics",
		}, { status: 500 });
	}
}

/**
 * OPTIONS /api/performance
 * Handle CORS preflight requests
 */
export async function OPTIONS(_request: NextRequest) {
	const response = new NextResponse(null, { status: 200 });

	// CORS headers
	response.headers.set("Access-Control-Allow-Origin", "*");
	response.headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
	response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
	response.headers.set("Access-Control-Max-Age", "86400"); // 24 hours

	return response;
}
