/* Custom RemixIcon CSS with absolute paths */
@font-face {
  font-family: "remixicon";
  src: url('/assets/icon-fonts/RemixIcons/fonts/remixicon.eot?t=1708865856766'); /* IE9*/
  src: url('/assets/icon-fonts/RemixIcons/fonts/remixicon.eot?t=1708865856766#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url("/assets/icon-fonts/RemixIcons/fonts/remixicon.woff2?t=1708865856766") format("woff2"),
  url("/assets/icon-fonts/RemixIcons/fonts/remixicon.woff?t=1708865856766") format("woff"),
  url('/assets/icon-fonts/RemixIcons/fonts/remixicon.ttf?t=1708865856766') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('/assets/icon-fonts/RemixIcons/fonts/remixicon.svg?t=1708865856766#remixicon') format('svg'); /* iOS 4.1- */
  font-display: swap;
}

[class^="ri-"], [class*=" ri-"] {
  font-family: 'remixicon' !important;
  font-style: normal;
  font-size: 16px !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Size classes */
.ri-lg { font-size: 1.3333em; line-height: 0.75em; vertical-align: -.0667em; }
.ri-xl { font-size: 1.5em; line-height: 0.6666em; vertical-align: -.075em; }
.ri-xxs { font-size: .5em; }
.ri-xs { font-size: .75em; }
.ri-sm { font-size: .875em }
.ri-1x { font-size: 1em; }
.ri-2x { font-size: 2em; }
.ri-3x { font-size: 3em; }
.ri-4x { font-size: 4em; }
.ri-5x { font-size: 5em; }
.ri-6x { font-size: 6em; }
.ri-7x { font-size: 7em; }
.ri-8x { font-size: 8em; }
.ri-9x { font-size: 9em; }
.ri-10x { font-size: 10em; }
.ri-fw { text-align: center; width: 1.25em; }

/* Common RemixIcon definitions */
.ri-home-line:before { content: "\ee2b"; }
.ri-user-line:before { content: "\f264"; }
.ri-settings-line:before { content: "\f0ee"; }
.ri-search-line:before { content: "\f0d1"; }
.ri-heart-line:before { content: "\ee0f"; }
.ri-arrow-down-s-line:before { content: "\ea4e"; }
.ri-clipboard-line:before { content: "\eb91"; }
.ri-check-line:before { content: "\eb7b"; }

/* Additional common icons */
.ri-add-line:before { content: "\ea13"; }
.ri-close-line:before { content: "\eb99"; }
.ri-edit-line:before { content: "\ec7e"; }
.ri-delete-bin-line:before { content: "\ec39"; }
.ri-eye-line:before { content: "\ed53"; }
.ri-eye-off-line:before { content: "\ed54"; }
.ri-download-line:before { content: "\ec5f"; }
.ri-upload-line:before { content: "\f22c"; }
.ri-refresh-line:before { content: "\f0a9"; }
.ri-more-line:before { content: "\ef5f"; }
.ri-menu-line:before { content: "\ef2e"; }
.ri-arrow-left-line:before { content: "\ea5e"; }
.ri-arrow-right-line:before { content: "\ea6c"; }
.ri-arrow-up-line:before { content: "\ea74"; }
.ri-arrow-down-line:before { content: "\ea4c"; }
