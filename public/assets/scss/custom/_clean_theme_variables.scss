/**
 * Clean Theme Variables
 * 
 * This file defines all CSS variables used by the clean Tailwind config.
 * All colors are defined as RGB values for use with opacity modifiers.
 */

:root {
  // === CORE THEME COLORS ===
  --primary: 225 182 73;           // #E1B649 - Golden primary
  --primary-dark: 184 134 11;      // #B8860B - Darker golden
  --secondary: 107 114 128;        // #6B7280 - Gray
  --success: 16 185 129;           // #10B981 - Green
  --warning: 245 158 11;           // #F59E0B - Orange
  --danger: 239 68 68;             // #EF4444 - Red
  --info: 59 130 246;              // #3B82F6 - Blue
  
  // === CUSTOM GOLDEN THEME ===
  --golden: 225 182 73;            // #E1B649 - Main golden color
  --golden-dark: 184 134 11;       // #B8860B - Darker golden for gradients
  
  // === DARK THEME LAYOUT COLORS ===
  --background: 15 15 15;          // #0F0F0F - Main body background
  --bodybg: 29 29 29;              // #1D1D1D - Body background
  --nav: 29 29 29;                 // #1D1D1D - Navigation background
  --section: 39 39 41;             // #272729 - Section backgrounds
  --elevated: 39 39 41;            // #272729 - Elevated content (tables, cards)
  
  // === COMPONENT SPECIFIC COLORS ===
  --form-input: 44 44 47;          // #2C2C2F - Form input background
  --form-bg: 29 29 31;             // #1D1D1F - Form background
  --table-section: 29 29 31;       // #1D1D1F - Table section background
  --table-head: 49 52 82;          // #313452 - Table header background
  --modal-header: 39 39 41;        // #272729 - Modal header background
  
  // === TEXT COLORS ===
  --defaulttextcolor: 255 255 255; // #FFFFFF - Primary text
  --textmuted: 153 153 153;        // #999999 - Muted text
  
  // === BORDER COLORS ===
  --defaultborder: 51 51 51;       // #333333 - Default borders
  
  // === NOTIFICATION COLORS ===
  // Background colors (subtle)
  --success-notification: 11 83 59;    // Dark green background
  --error-notification: 105 0 0;       // Dark red background  
  --warning-notification: 114 73 2;    // Dark orange background
  --info-notification: 45 45 47;       // Dark gray background
  
  // Message text colors (bright for readability)
  --success-message: 52 211 153;       // #34D399 - Light green text
  --error-message: 248 113 113;        // #F87171 - Light red text
  --warning-message: 251 191 36;       // #FBBF24 - Light orange text
  --info-message: 209 213 219;         // #D1D5DB - Light gray text
}

// === LIGHT THEME OVERRIDES (if needed) ===
[data-theme="light"] {
  --background: 255 255 255;        // #FFFFFF - Light background
  --bodybg: 249 250 251;            // #F9FAFB - Light body background
  --nav: 255 255 255;               // #FFFFFF - Light navigation
  --section: 249 250 251;           // #F9FAFB - Light sections
  --elevated: 255 255 255;          // #FFFFFF - Light elevated content
  
  --form-input: 255 255 255;        // #FFFFFF - Light form inputs
  --form-bg: 249 250 251;           // #F9FAFB - Light form background
  --table-section: 249 250 251;     // #F9FAFB - Light table section
  --table-head: 243 244 246;        // #F3F4F6 - Light table header
  --modal-header: 255 255 255;      // #FFFFFF - Light modal header
  
  --defaulttextcolor: 17 24 39;     // #111827 - Dark text on light
  --textmuted: 107 114 128;         // #6B7280 - Muted text on light
  
  --defaultborder: 229 231 235;     // #E5E7EB - Light borders
}

// === COMPONENT-SPECIFIC MIXINS ===
@mixin golden-button {
  background: linear-gradient(135deg, rgb(var(--golden)), rgb(var(--golden-dark)));
  border: 1px solid rgb(var(--golden));
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
  box-shadow: 0 4px 14px 0 rgba(225, 182, 73, 0.39);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 6px 20px rgba(225, 182, 73, 0.4);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

@mixin custom-container {
  background-color: rgb(var(--background));
  border: 1px solid rgb(var(--defaultborder));
  border-radius: 0.5rem;
  padding: 1rem;
}

@mixin form-input {
  background-color: rgb(var(--form-input));
  border: 1px solid rgb(var(--defaultborder));
  border-radius: 0.5rem;
  padding: 0.75rem;
  color: rgb(var(--defaulttextcolor));
  
  &::placeholder {
    color: rgb(var(--textmuted));
  }
  
  &:focus {
    border-color: rgb(var(--golden));
    outline: none;
    box-shadow: 0 0 0 3px rgb(var(--golden) / 0.1);
  }
}

// === UTILITY CLASSES ===
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.golden-gradient {
  background: linear-gradient(135deg, rgb(var(--golden)), rgb(var(--golden-dark)));
}

.text-gradient-golden {
  background: linear-gradient(135deg, rgb(var(--golden)), rgb(var(--golden-dark)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// === RESPONSIVE HELPERS ===
@media (max-width: 768px) {
  :root {
    // Adjust spacing for mobile if needed
    --container-padding: 1rem;
  }
}

@media (min-width: 1200px) {
  :root {
    // Adjust for larger screens if needed
    --container-padding: 2rem;
  }
}
