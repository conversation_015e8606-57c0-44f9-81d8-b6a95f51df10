/* Start:: double_menu */
[data-vertical-style="doublemenu"] {
    @media (min-width: 992px) {
        .slide-menu.double-menu-active{
            @apply block #{!important};
        }
        .app-sidebar {
            @apply w-20;
            .main-sidebar {
                @apply overflow-visible h-full shadow-none mt-0;
            }
            .slide-menu.child1 {
                @apply pb-12;
            }
            .main-sidebar-header {
                @apply w-20 backdrop-blur-[30px] justify-center;
                .header-logo {
                    .toggle-logo {
                        @apply block #{!important};
                    }
                    .desktop-dark,
                    .desktop-logo,
                    .toggle-dark {
                        @apply hidden #{!important};
                    }
                }
            }
            .main-menu-container {
                @apply mt-[3.75rem];
            }
            .main-menu >.slide {
                @apply px-[0.6rem] py-0;
            }
            .category-name,
            .side-menu__label,
            .side-menu__angle {
                @apply hidden;
            }
            .side-menu__icon {
                @apply text-menuprimecolor me-0;
            }
            .slide__category {
                @apply hidden;
            }
            .simplebar-content-wrapper {
                position: initial;
            }
            .simplebar-mask {
                position: inherit;
                
            }
            .simplebar-placeholder {
                @apply h-auto #{!important};
            }
        }
        .app-header {
            @apply ps-[17rem];
        }
        .app-content {
            @apply ms-[17rem];
        }
        .slide.has-sub .slide-menu {
            @apply absolute bg-menubg shadow-none transition-none h-full border-e-white/10 border-e border-solid start-20 end-0 #{!important};
            &.child2,
            &.child3 {
                @apply relative h-auto border-e-0 start-0 top-0 #{!important};
                .slide {
                    &:nth-child(2) {
                        @apply pt-0;
                    }
                }
            }
        }
        .slide-menu {
            &.child1,
            &.child2,
            &.child3 {
                .slide {
                    @apply ps-0;
                    .side-menu__item {
                        @apply text-start;
                        &:before {
                            @apply hidden;
                        }
                    }
                }
                .side-menu__angle {
                    @apply block end-4 top-[0.65rem];
                }
            }
            &.child2,&.child3 {
                @apply min-w-[10rem];
            }
            &.child1 {
                @apply min-w-[12rem];
                .slide {
                    &:nth-child(2) {
                        @apply pt-3;
                    }
                }
            }
        }
        .side-menu__label1 {
            @apply block text-[0.938rem] font-medium text-menuprimecolor border-b-menubordercolor w-48 h-[4.25rem] px-4 py-6 border-b border-solid #{!important};
        }
        .slide-menu {
            @apply hidden;

            &.double-menu-active {
                @apply visible block h-full absolute overflow-x-hidden overflow-y-scroll rounded-none border-t-0 top-0;
            }
        }
        &[class="light"] {
            &[data-menu-styles="dark"] {
                .app-sidebar .main-sidebar-header {
                    .header-logo {
                        .desktop-logo,.desktop-dark,.toggle-logo {
                            @apply hidden #{!important};
                        }
                        .toggle-dark {
                            @apply block #{!important};
                        }
                    }
                }
            }
            &[data-menu-styles="color"],&[data-menu-styles="gradient"] {
                .app-sidebar .main-sidebar-header {
                    .header-logo {
                        .desktop-logo,.desktop-dark,.toggle-logo, .toggle-dark, .desktop-white {
                            @apply hidden #{!important};
                        }
                        .toggle-white {
                            @apply block #{!important};
                        }
                    }
                }
            }
        }
        &[class="dark"] {
            .app-sidebar .main-sidebar-header {
                .header-logo {
                    .desktop-logo,.desktop-dark,.toggle-logo {
                        @apply hidden #{!important};
                    }
                    .toggle-dark {
                        @apply block;
                    }
                }
            }
            &[data-menu-styles="light"] {
                .app-sidebar .main-sidebar-header {
                    .header-logo {
                        .desktop-logo,.desktop-dark,.toggle-dark {
                            @apply hidden #{!important}; 
                        }
                        .toggle-logo {
                            @apply block #{!important};
                        }
                    }
                }   
            }
            &[data-page-style="classic"] {
                .slide.has-sub .slide-menu {
                    @apply border-e-defaultborder border-e border-solid;
                }
            }
        }
        &[data-toggled="double-menu-close"] {
            .app-header{
                @apply ps-20;
            }
            .app-content {
                @apply ms-20;
            }
            .app-sidebar {
                .slide.has-sub .slide-menu {
                    @apply hidden #{!important};
                }
                .main-sidebar {
                    @apply shadow-defaultshadow;
                }
            }
        }
        &[data-toggled="double-menu-open"] {
            .app-sidebar .main-sidebar {
                .slide.side-menu__label1 {
                    @apply border-e-defaultborder border-e border-solid;
                }
            }
        }
    }
}
@media (min-width: 992px) {
    [data-vertical-style="doublemenu"][data-toggled="double-menu-close"][data-menu-styles="gradient"]{
         .app-sidebar .main-sidebar-header {
       @apply border-b-white/10 #{!important};
    }
}
}
@media (min-width: 992px) {
    [data-vertical-style="doublemenu"][data-toggled="double-menu-close"][data-menu-styles="color"]{
         .app-sidebar .main-sidebar-header {
       @apply border-b-white/10 #{!important};
    }
}
}
@media (min-width: 992px) {
    [data-vertical-style="doublemenu"][data-toggled="double-menu-close"][data-menu-styles="dark"]{
         .app-sidebar .main-sidebar-header {
       @apply border-b-white/10 #{!important};
    }
}
}
/* End:: double_menu */