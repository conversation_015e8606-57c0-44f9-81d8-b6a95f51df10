/* Start:: menu_hover */
[data-nav-style="menu-hover"][data-nav-layout="horizontal"] {
    @extend .menu-hover;
    @media (min-width: 992px) {
        .app-sidebar {
            .side-menu__item {
                @apply ps-3 pe-7 py-[0.93rem];
            }
            .side-menu__item {
                @apply flex #{!important};
            }
            .side-menu__icon {
                @apply me-2 mb-0;
            }
            .side-menu__angle { 
                @apply block #{!important};
            }
            .slide.has-sub .slide-menu {
                &.active {
                    @apply inset-x-auto #{!important};
                }
                &.child1 {
                    @apply top-full #{!important};
                }
            }
        }
    }
}
[data-nav-style="menu-hover"][data-toggled="menu-hover-closed"] {
    @extend .menu-hover;
}
[data-nav-style="menu-hover"][data-nav-layout="vertical"][data-toggled="menu-hover-closed"] {
    @media (min-width: 992px) {
        .app-sidebar {
            @apply absolute;
            .side-menu__item {
                @apply m-1 rounded-lg;
                &:last-child {
                    @apply m-1 #{!important};
                }
            }
            .side-menu__icon{
                @apply inline-flex mb-2 me-0 #{!important};
            }
        }
        .app-sidebar {
            .slide .slide-menu {
                &.child1,&.child2,&.child3 {
                    @apply rounded-[0_0.5rem_0.5rem_0];
                }
            } 
        }
        &[dir="rtl"] {
            .app-sidebar {
                .slide .slide-menu {
                    &.child1,&.child2,&.child3 {
                        @apply rounded-[0.5rem_0_0_0.5rem];
                    }
                } 
            }
        }
    }
}
.menu-hover {
    @media (min-width: 992px) {
        .app-sidebar {
            @apply w-[9.5rem];
            .main-sidebar {
                @apply overflow-visible h-[90%];
            }
            .main-sidebar-header {
                @apply w-[9.5rem] justify-center;
            }
            // .side-menu__icon {
            //     @apply me-0 mb-2;
            // }
            .slide {
                @apply p-0;
            }
            .slide-menu {
                &.child1,
                &.child2,
                &.child3 {
                    @apply min-w-[12rem] hidden #{!important};
                    .slide {
                        .side-menu__item {
                             @apply text-start;
                            &:before {
                                @apply hidden;
                            }
                        }
                    }
                    .slide.has-sub,.slide {
                        &.side-menu__label1 {
                            @apply hidden;
                        }
                    }
                }
            }
            .slide.has-sub:hover {
                .slide-menu.child1 {
                    @apply block #{!important};
                    .slide.has-sub:hover {
                        .slide-menu.child2 {
                            @apply block #{!important};
                            .slide.has-sub:hover {
                                .slide-menu.child3 {
                                    @apply block #{!important};
                                }
                            }
                        }
                    }
                }
            }
            .slide__category,
            .side-menu__angle {
                @apply hidden;
            }
            .slide-menu {
                &.child1,
                &.child2,
                &.child3 {
                    .side-menu__angle {
                        @apply block end-2 top-[0.65rem];
                    }
                }
            }
            .side-menu__item,
            .side-menu__label {
                @apply block text-center;
            }
            .slide.has-sub .slide-menu {
                @apply absolute bg-bodybg shadow-[0_0_0.375rem_rgba(0,0,0,0.1)] transition-none start-[9.5rem] top-auto #{!important};
                &.child2,
                &.child3 {
                    @apply start-48 #{!important};
                }
            }
            .simplebar-content-wrapper {
                position: initial;
            }
            .simplebar-mask {
                position: inherit;
            }
            .simplebar-placeholder {
                @apply h-auto #{!important};
            }
        }
        .app-header {
            @apply ps-[9.5rem];
        }
        .app-content {
            @apply ms-[9.5rem];
        }
    }
}
@media (min-width: 992px) {
    [data-nav-layout="vertical"] {
        &[data-nav-style="menu-hover"][data-toggled="menu-hover-closed"] { 
            .app-sidebar {
                .slide .side-menu__label {
                    .badge {
                        @apply hidden;
                    }
                }
            }
            &[data-bg-img="bgimg1"],&[data-bg-img="bgimg2"],&[data-bg-img="bgimg3"],&[data-bg-img="bgimg4"],&[data-bg-img="bgimg5"] {
                .app-sidebar {
                    .main-sidebar-header {
                        @apply backdrop-blur-[30px];
                    }
                }
            }
        }
    }
}
/* End:: menu_hover */