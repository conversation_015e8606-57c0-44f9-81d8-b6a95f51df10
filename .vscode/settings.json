{
  // Enable ESLint with performance optimizations
  "eslint.enable": true,
  "eslint.autoFixOnSave": false,
  "eslint.run": "onType",
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "eslint.workingDirectories": [{ "mode": "auto" }],

  // Enable TypeScript error checking with optimizations
  "typescript.validate.enable": true,
  "typescript.suggest.enabled": true,
  "typescript.updateImportsOnFileMove.enabled": "prompt",
  "typescript.preferences.includePackageJsonAutoImports": "off",

  // Enable JavaScript validation
  "javascript.validate.enable": true,
  "javascript.suggest.enabled": true,

  // Keep auto-save disabled for performance
  "files.autoSave": "off",

  // Enable format on save but limit ESLint auto-fixing
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
    // "source.organizeImports": "explicit" // Disabled for development - will run on build/commit instead
  },

  // Enable problems panel but limit auto-refresh
  "problems.autoReveal": true,

  // Optimize IntelliSense for performance
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },

  // Performance optimizations
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/dist/**": true,
    "**/build/**": true
  }
}
