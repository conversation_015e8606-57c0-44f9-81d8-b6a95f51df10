import forms from '@tailwindcss/forms';
import type { Config } from 'tailwindcss';
import plugin from 'tailwindcss/plugin';
// @ts-ignore - preline doesn't have TypeScript definitions
import preline from 'preline/plugin';

const config: Config = {
  darkMode: "class",
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./shared/**/*.{js,ts,jsx,tsx,mdx}",
    './node_modules/preline/preline.js',
  ],
  theme: {
    screens: {
      lg: "992px",
      md: "768px",
      sm: "480px",
      xl: "1200px",
      xxl: "1400px",
      xxxl: "1800px",
    },
    borderRadius: {
      none: "0",
      sm: "0.25rem",
      md: "0.5rem",
      lg: "0.75rem",
      xl: "1rem",
      full: "9999px",
    },
    fontFamily: {
      defaultfont: ["var(--font-poppins)", "Poppins", "sans-serif"],
      rubik: ["var(--font-rubik)", "Rubik", "sans-serif"],
      mont: ["Montserrat", "sans-serif"],
      remix: ["remixicon"],
      tabler: ["tabler-icons"],
      bootstrap: ["bootstrap-icons"],
    },
    fontSize: {
      defaultsize: '0.8125rem',
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
      '7xl': '4.5rem',
      '8xl': '6rem',
      '9xl': '8rem',
    },
    extend: {
      colors: {
        // ===== DARK THEME COLOR SCHEME =====

        // === NEW DARK THEME LAYERED BACKGROUND SYSTEM ===
        // Layer 1: Main Background
        background: "#0F0F0F",           // Main body background
        "secondary-btn": "#2D2D2D",

        // Layer 2: Navigation & Section Backgrounds
        nav: "#1D1D1D",                  // Navigation components, navbar/nav header
        section: "#272729",              // Filter headings background
        filter: "#1D1D1F",               // Filter background
        "table-section": "#1D1D1F",      // Table section background
        // Layer 3: Elevated Content
        elevated: "#272729",             // Table background, elevated above table-section
        "table-total": "#494C72",        // Table total/pagination background
        "table-head": "#313452",         // Table header (thead) background

        // Form and Modal Styling
        "form-input": "#2C2C2F",         // Form input background
        "form-head": "#272729",          // Form head/header background
        "form-bg": "#1D1D1F",            // Form background
        "modal-header": "#272729",       // Modal header background (same as elevated)

        // Legacy support (keeping for backward compatibility)
        elevated2: "#131315",            // Legacy elevated
        surface: "#2A2A2A",              // Legacy elevated surfaces
        // === Primary Golden/Bronze Theme ===
        primary: {
          50: "#FEF7E0",
          100: "#FDECC8",
          200: "#FBD88A",
          300: "#F9C74F",
          400: "#E1B649",              // Main golden color
          500: "#DAA520",              // Primary golden
          600: "#B8860B",              // Darker golden
          700: "#996F09",
          800: "#7A5607",
          900: "#5C4105",
          DEFAULT: "#E1B649",          // Default primary color for utilities
        },

        // === Secondary Color ===
        secondary: {
          50: "#F8FAFC",
          100: "#F1F5F9",
          200: "#E2E8F0",
          300: "#CBD5E1",
          400: "#94A3B8",
          500: "#6B7280",              // Secondary gray
          600: "#4B5563",
          700: "#374151",
          800: "#1F2937",
          900: "#111827",
          DEFAULT: "#6B7280",          // Default secondary color
        },

        // === Status Colors ===
        success: {
          50: "#ECFDF5",
          500: "#10B981",              // Success green
          600: "#059669",
          700: "#047857",
          DEFAULT: "#10B981",          // Default success color
        },

        warning: {
          50: "#FFFBEB",
          500: "#F59E0B",              // Warning orange
          600: "#D97706",
          700: "#B45309",
          DEFAULT: "#F59E0B",          // Default warning color
        },

        danger: {
          50: "#FEF2F2",
          500: "#EF4444",              // Error red
          600: "#DC2626",
          700: "#B91C1C",
          DEFAULT: "#EF4444",          // Default danger color
        },

        info: {
          50: "#EFF6FF",
          500: "#3B82F6",              // Info blue
          600: "#2563EB",
          700: "#1D4ED8",
          DEFAULT: "#3B82F6",          // Default info color
          // === Toast Notification Custom Backgrounds ===

        },

        // === Text Colors ===
        text: {
          primary: "#FFFFFF",           // Main text (filter headings, thead text, primary button text)
          secondary: "#AEAEAE",         // Filter input labels, form labels
          tertiary: "#616161",          // Filter input placeholders, form placeholders
          muted: "#999999",             // Table body text (text-gray-400)
          inverse: "#000000",           // Text on light backgrounds
          // Form and Modal specific text colors
          "form-input": "#FFFFFF",      // Form input text color (white)
          "form-label": "#AEAEAE",      // Form label text color (same as secondary)
          "form-placeholder": "#616161", // Form placeholder text color (same as tertiary)
          "modal-title": "#FFFFFF",     // Modal title text color (white)
          "form-heading": "#FFFFFF",    // Form heading/title text color (white)
        },

        // === Border & Divider Colors ===
        border: {
          primary: "#333333",           // Filter heading border-bottom
          secondary: "#FFFFFF33",       // Filter input borders (33% opacity white)
          tertiary: "#C4C4C41A",        // Table row border-bottom (10% opacity white)
          accent: "#E1B649",            // Highlighted borders (golden)
        },

        // === Accent Colors ===
        purple: {
          50: "#F5F3FF",
          500: "#8B5CF6",              // Purple accent (user type)
          600: "#7C3AED",
          700: "#6D28D9",
        },
        // === Legacy Support (CSS Variables from working version) ===
        primarytint1color: "rgb(var(--primary-tint1-rgb))",
        primarytint2color: "rgb(var(--primary-tint2-rgb))",
        primarytint3color: "rgb(var(--primary-tint3-rgb))",
        headerbg: "rgb(var(--header-bg))",
        menubg: "rgb(var(--menu-bg))",
        gray1: "var(--gray-1)",
        gray2: "var(--gray-2)",
        gray3: "var(--gray-3)",
        gray4: "var(--gray-4)",
        gray5: "var(--gray-5)",
        gray6: "var(--gray-6)",
        gray7: "var(--gray-7)",
        gray8: "var(--gray-8)",
        gray9: "var(--gray-9)",
        customwhite: "rgb(var(--custom-white))",
        // Updated to use layered background system with fallback to CSS variables
        bodybg: "#1D1D1D",               // Layer 1: Main body background (swapped from header color)
        bodybg2: "#0F0F0F",              // Layer 2: Section background (swapped from body color)
        // Legacy CSS variable support (for backward compatibility)
        bodybglegacy: "rgb(var(--body-bg))",
        bodybg2legacy: "rgb(var(--dark-bg))",
        primarylegacy: "rgb(var(--primary))",
        primaryrgb: "rgb(var(--primary-rgb))",
        secondarylegacy: "rgb(var(--secondary))",
        successlegacy: "rgb(var(--success))",
        infolegacy: "rgb(var(--info))",
        warninglegacy: "rgb(var(--warning))",
        dangerlegacy: "rgb(var(--danger))",
        light: "rgb(var(--light))",
        dark: "rgb(var(--dark))",
        defaulttextcolor: "rgb(var(--default-text-color))",
        defaultborder: "rgb(var(--default-border))",
        defaultbackground: "rgb(var(--default-background))",
        menuprimecolor: "rgb(var(--menu-prime-color))",
        menubordercolor: "rgb(var(--menu-border-color))",
        headerprimecolor: "rgb(var(--header-prime-color))",
        headerbordercolor: "rgb(var(--header-border-color))",
        listhoverfocusbg: "rgb(var(--list-hover-focus-bg))",
        textmuted: "rgb(var(--text-muted))",
        inputborder: "rgb(var(--input-border))",
        formcontrolbg: "rgb(var(--body-bg-rgb))",
        orangemain: "rgb(var(--orange))",
        pinkmain: "rgb(var(--pink))",
        tealmain: "rgb(var(--teal))",
        purplemain: "rgb(var(--purple))",
        redmain: "rgb(var(--red))",
        bluemain: "rgb(var(--blue))",
        greenmain: "rgb(var(--green))",
        cyanmain: "rgb(var(--cyan))",
        indigomain: "rgb(var(--indigo))",
        yellowmain: "rgb(var(--yellow))",
        facebook: "rgb(var(--facebook))",
        twitter: "rgb(var(--twitter))",
        github: "rgb(var(--github))",
        google: "rgb(var(--google))",
        youtube: "rgb(var(--youtube))",

        // === CUSTOM THEME COLORS (Consolidated) ===
        "golden": "#E1B649",             // Primary golden color
        "golden-dark": "#B8860B",        // Darker golden
        // === Toast Notification Colors (Improved UX Design) ===
        // Background colors - subtle and professional
        'success-notification': 'rgba(11, 83, 59, 0.99)', // Success green with 15% opacity
        'error-notification': 'rgb(105 0 0)',    // Error red with 15% opacity
        'warning-notification': 'rgba(114, 73, 2, 1)', // Warning orange with 15% opacity
        'info-notification': '#2D2D2F',                      // Slightly lighter dark gray

        // Border colors - vibrant for definition
        'success-notification-border': '#10B981',            // Bright success green
        'error-notification-border': '#EF4444',              // Bright error red
        'warning-notification-border': '#F59E0B',            // Bright warning orange
        'info-notification-border': '#3B82F6',               // Bright info blue

        // Icon colors - same as borders for consistency
        'success-notification-icon': '#10B981',              // Bright success green
        'error-notification-icon': '#EF4444',                // Bright error red
        'warning-notification-icon': '#F59E0B',              // Bright warning orange
        'info-notification-icon': '#3B82F6',                 // Bright info blue

        // Message text colors - lighter variants for readability
        'success-message': '#34D399',                        // Light success green
        'error-message': '#F87171',                          // Light error red
        'warning-message': '#FBBF24',                        // Light warning orange
        'info-message': '#D1D5DB',                           // Light gray
        // === Card Background Colors ===
        "bg-card-general": "#272727",    // General reports (bet-report, financial-report, login-history)
        "bg-card-cashier": "#1D1D1F",    // Cashier report & user details financial report cards

        // Legacy aliases (keeping for backward compatibility)
        "dark-main": "#0F0F0F",          // Main background (legacy)
        "dark-section": "#1D1D1D",       // Section background (legacy)
        "dark-surface": "#2A2A2A",       // Surface background (legacy)
      },
      gradientColorStops: {
        // === Legacy gradients from working version ===
        primary: 'rgb(var(--primary))',
        secondary: 'rgb(var(--secondary))',
        success: 'rgb(var(--success))',
        warning: 'rgb(var(--warning))',
        pinkmain: 'rgb(var(--pink))',
        tealmain: 'rgb(var(--teal))',
        danger: 'rgb(var(--danger))',
        info: 'rgb(var(--info))',
        orangemain: 'rgb(var(--orange))',
        purplemain: 'rgb(var(--purple))',
        light: 'rgb(var(--light))',
        dark: 'rgb(var(--dark))',

        // === ESSENTIAL GRADIENTS ===
        "golden": "#E1B649",
        "golden-dark": "#B8860B",
      },

      boxShadow: {
        // === Dark Theme Shadows ===

        // Default shadows
        'default': '0px 6px 16px 2px rgba(0, 0, 0, 0.3)',
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.4)',

        // Golden button shadows (multi-layered for depth) - Exact SignInFormUI styling
        'golden-button': [
          '4px 4px 8px 0px rgba(255, 255, 255, 0.45) inset',  // Inner highlight (exact from SignInFormUI)
          '0px 4px 12px 0px rgba(0, 0, 0, 0.25)',              // Outer shadow (exact from SignInFormUI)
          '-4px -4px 12px 0px #604400 inset'                   // Inner dark shadow (exact from SignInFormUI)
        ].join(', '),

        'golden-button-hover': [
          '4px 4px 8px 0px rgba(255, 255, 255, 0.55) inset',  // Brighter inner highlight
          '0px 6px 16px 0px rgba(0, 0, 0, 0.35)',              // Deeper outer shadow
          '-4px -4px 12px 0px #604400 inset'                   // Maintain inner dark shadow
        ].join(', '),

        // Card shadows
        'card': '0 4px 12px rgba(0, 0, 0, 0.4)',
        'card-hover': '0 8px 24px rgba(0, 0, 0, 0.5)',

        // Modal shadows
        'modal': '0 20px 40px rgba(0, 0, 0, 0.6)',

        // Legacy support
        'defaultshadow': '0px 6px 16px 2px rgba(0, 0, 0, 0.3)',
      },
      backgroundImage: {
        // === Legacy gradients from working version ===
        "instagram": "linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%)",
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-1": "linear-gradient(102deg,transparent 41%,primary/50 0)",
        'custom-gradient': 'linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)',
        // === ESSENTIAL GRADIENTS ===
        'golden-gradient': 'linear-gradient(135deg, #F9C74F 0%, #E1B649 50%, #B8860B 100%)',
        'golden-button': 'linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)',
        'golden-subtle': 'linear-gradient(135deg, #E1B649 0%, #996F09 100%)',

        // === REQUIRED GRADIENTS (Used in SCSS) ===
        'primarygradient': 'linear-gradient(to bottom right, #E1B649 0%, #6B7280 100%)',
        'primary1gradient': 'linear-gradient(to bottom right, rgba(225, 182, 73, 0.1) 0%, #AB54E3 100%)',
        'primary2gradient': 'linear-gradient(to bottom right, rgba(225, 182, 73, 0.2) 0%, #FF6C5D 100%)',
        'primary3gradient': 'linear-gradient(to bottom right, rgba(225, 182, 73, 0.3) 0%, #FF663C 100%)',
        'secondarygradient': 'linear-gradient(to bottom right, #6B7280 0%, #7289FF 100%)',
        'successgradient': 'linear-gradient(to bottom right, #10B981 0%, #047857 100%)',
        'warninggradient': 'linear-gradient(to bottom right, #F59E0B 0%, #B45309 100%)',
        'dangergradient': 'linear-gradient(to bottom right, #EF4444 0%, #B91C1C 100%)',
        'infogradient': 'linear-gradient(to bottom right, #3B82F6 0%, #1D4ED8 100%)',
        'orangegradient': 'linear-gradient(to bottom right, #F59E0B 0%, #EA580C 100%)',
        'purplegradient': 'linear-gradient(to bottom right, #8B5CF6 0%, #6D28D9 100%)',
        'tealgradient': 'linear-gradient(to bottom right, #14B8A6 0%, #0F766E 100%)',
        'lightgradient': 'linear-gradient(to bottom right, #F8FAFC 0%, #E2E8F0 100%)',
        'darkgradient': 'linear-gradient(to bottom right, #1F2937 0%, #111827 100%)',
        // Status gradients
        'success-gradient': 'linear-gradient(135deg, #10B981 0%, #047857 100%)',
        'warning-gradient': 'linear-gradient(135deg, #F59E0B 0%, #B45309 100%)',
        'danger-gradient': 'linear-gradient(135deg, #EF4444 0%, #B91C1C 100%)',
        'info-gradient': 'linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)',
        'purple-gradient': 'linear-gradient(135deg, #8B5CF6 0%, #6D28D9 100%)',
        'purple-header-gradient': 'linear-gradient(90deg, rgba(113, 95, 206, 0.4) 0%, rgba(113, 95, 206, 0) 25.28%)',
        'purple-header-gradient-1': 'linear-gradient(90deg, rgba(113, 95, 206, 0.4) 0.09%, rgba(113, 95, 206, 0) 100%)',

        // Layered background gradients
        'background-gradient': 'linear-gradient(135deg, #0F0F0F 0%, #1A1A1A 100%)',     // Layer 1 gradient
        'nav-gradient': 'linear-gradient(135deg, #1D1D1D 0%, #252525 100%)',           // Layer 2 nav gradient
        'section-gradient': 'linear-gradient(135deg, #1D1D1F 0%, #252527 100%)',       // Layer 2 section gradient
        'elevated-gradient': 'linear-gradient(135deg, #272729 0%, #2F2F31 100%)',      // Layer 3 elevated gradient

        // Legacy background gradients (keeping for backward compatibility)
        'dark-gradient': 'linear-gradient(135deg, #0F0F0F 0%, #1D1D1D 100%)',
        'surface-gradient': 'linear-gradient(135deg, #2A2A2A 0%, #404040 100%)',
        'profile-gradient': 'linear-gradient(90deg, rgba(113, 95, 206, 0.4) 0%, rgba(113, 95, 206, 0) 59.95%)',
        // Utility gradients
        'shimmer': 'linear-gradient(45deg, rgba(255, 255, 255, .1) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .1) 50%, rgba(255, 255, 255, .1) 75%, transparent 75%, transparent)',
      },
      backgroundSize: {
        'custom-size': '1rem 1rem',
      },

      // === Ring Colors for Focus States ===
      ringColor: {
        'primary': '#E1B649',
        'secondary': '#6B7280',
        'success': '#10B981',
        'warning': '#F59E0B',
        'danger': '#EF4444',
        'info': '#3B82F6',
      },
    },
  },
  variants: {},
  plugins: [
    forms({
      strategy: 'base', // Use base strategy for global form styles
    }),
    // require("tailwind-clip-path"),
    preline,
    plugin(function ({ addComponents, addBase, addUtilities }: any) {
      // === Base HTML Elements ===
      addBase({
        'h1': { fontSize: '2.5rem' },
        'h2': { fontSize: '2rem' },
        'h3': { fontSize: '1.75rem' },
        'h4': { fontSize: '1.5rem' },
        'h5': { fontSize: '1.25rem' },
        'h6': { fontSize: '1rem' },

        // === Form Elements Border Radius Override ===
        // Override @tailwindcss/forms default border radius to 4px (0.25rem)
        'input[type="text"], input:where(:not([type])), input[type="email"], input[type="url"], input[type="password"], input[type="number"], input[type="date"], input[type="datetime-local"], input[type="month"], input[type="search"], input[type="tel"], input[type="time"], input[type="week"], input[multiple], textarea, select': {
          borderRadius: '0.25rem !important', // 4px instead of 0.35rem (5.6px)
          color: 'white !important'
        },
        '[type=text]:is(.dark *), input:where(:not([type])):is(.dark *), [type=email]:is(.dark *), [type=url]:is(.dark *), [type=password]:is(.dark *), [type=number]:is(.dark *), [type=date]:is(.dark *), [type=datetime-local]:is(.dark *), [type=month]:is(.dark *), [type=search]:is(.dark *), [type=tel]:is(.dark *), [type=time]:is(.dark *), [type=week]:is(.dark *), [multiple]:is(.dark *), textarea:is(.dark *), select:is(.dark *) ': {
          borderColor: '#FFFFFF33 !important',
          color: 'white !important'
        },
      });

      // === Component Classes ===
      addComponents({
        // === Form Elements Border Radius Override (Component Level) ===
        // Higher specificity override for @tailwindcss/forms
        'input[type="text"]': {
          borderRadius: '0.25rem !important',
        },
        'input:where(:not([type]))': {
          borderRadius: '0.25rem !important',
        },
        'input[type="email"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="password"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="number"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="search"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="tel"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="url"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="date"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="datetime-local"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="month"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="time"]': {
          borderRadius: '0.25rem !important',
        },
        'input[type="week"]': {
          borderRadius: '0.25rem !important',
        },
        'select': {
          borderRadius: '0.25rem !important',
        },
        'textarea': {
          borderRadius: '0.25rem !important',
        },

        // Direction utilities
        ".dirrtl": { direction: "ltr" },
        ".dir-rtl": { direction: "rtl" },
        ".dir-ltr": { direction: "ltr" },

        // Typography utilities
        '.h1': { fontSize: '2.5rem' },
        '.h2': { fontSize: '2rem' },
        '.h3': { fontSize: '1.75rem' },
        '.h4': { fontSize: '1.5rem' },
        '.h5': { fontSize: '1.25rem' },
        '.h6': { fontSize: '1rem' },

        // Status color utilities
        '.text-danger': { color: '#EF4444' },
        '.bg-danger': { backgroundColor: '#EF4444' },
        '.bg-danger\\/20': { backgroundColor: 'rgba(239, 68, 68, 0.2)' },

        // Border utilities
        '.border-b-menubordercolor\\/10': { borderBottomColor: 'rgba(64, 64, 64, 0.1)' },
        '.border-e-menubordercolor\\/10': { borderInlineEndColor: 'rgba(64, 64, 64, 0.1)' },

        // Gradient utilities
        '.from-primary': { '--tw-gradient-from': '#E1B649' },
        '.to-primary': { '--tw-gradient-to': '#E1B649' },
        '.from-secondary': { '--tw-gradient-from': '#6B7280' },
        '.to-secondary': { '--tw-gradient-to': '#6B7280' },
      });

      // === Utility Classes ===
      addUtilities({
        // Background utilities for textmuted
        '.bg-textmuted': {
          backgroundColor: 'rgb(var(--text-muted))',
        },

        // Text utilities
        '.text-primary': {
          color: '#E1B649',
        },

        // Form element border radius utility
        '.rounded-input': {
          borderRadius: '0.25rem !important', // 4px border radius for form elements
        },

        // === NEW DARK THEME BACKGROUND UTILITIES ===
        // Layer 1: Main body background
        '.bg-background': {
          backgroundColor: '#0F0F0F',
        },

        // Layer 2: Navigation & Section backgrounds
        '.bg-nav': {
          backgroundColor: '#1D1D1D',
        },
        '.bg-section': {
          backgroundColor: '#272729',
        },
        '.bg-filter': {
          backgroundColor: '#1D1D1F',
        },
        '.bg-table-section': {
          backgroundColor: '#1D1D1F',
        },

        // Layer 3: Elevated content
        '.bg-elevated': {
          backgroundColor: '#272729',
        },
        '.bg-table-total': {
          backgroundColor: '#494C72',
        },
        '.bg-table-head': {
          backgroundColor: '#313452',
        },
        '.bg-form-input': {
          backgroundColor: '#2C2C2F',
        },
        '.bg-form-head': {
          backgroundColor: '#272729',
        },
        '.bg-form-bg': {
          backgroundColor: '#1D1D1F',
        },
        '.bg-modal-header': {
          backgroundColor: '#272729',
        },

        // === CARD BACKGROUND UTILITIES ===
        '.bg-card-general': {
          backgroundColor: '#272727',
        },
        '.bg-card-cashier': {
          backgroundColor: '#1D1D1F',
        },

        // === TEXTURE BACKGROUND UTILITIES ===
        '.bg-texture': {
          backgroundImage: 'url(/texture-cashier.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'right',
          backgroundBlendMode: 'darken',
        },
        '.bg-card-general-texture': {
          backgroundColor: '#16161642',
          backgroundImage: 'url(/texture-report-bg.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'right',
          backgroundBlendMode: 'exclusion',
        },
        '.bg-card-cashier-texture': {
          backgroundColor: '#4a4a4a3d',
          backgroundImage: 'url(/texture-background.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'right',
          backgroundBlendMode: 'exclusion',
        },
        '.bg-card-financial-texture': {
          backgroundColor: '#16161642',
          backgroundImage: 'url(/texture-background-v1.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'right',
          backgroundBlendMode: 'darken',
        },

        // === NEW TEXT COLOR UTILITIES ===
        '.text-filter-heading': {
          color: '#FFFFFF',
        },
        '.text-filter-label': {
          color: '#AEAEAE',
        },
        '.text-filter-placeholder': {
          color: '#616161 !important',
        },
        '.text-form-input': {
          color: '#FFFFFF',
        },
        '.text-form-label': {
          color: '#AEAEAE',
        },
        '.text-form-placeholder': {
          color: '#616161',
        },
        '.text-modal-title': {
          color: '#FFFFFF',
        },
        '.text-form-heading': {
          color: '#FFFFFF',
        },

        // === NEW BORDER COLOR UTILITIES ===
        '.border-filter-heading': {
          borderColor: '#333333 !important',
        },
        '.border-filter-input': {
          borderColor: '#FFFFFF33 !important',
        },
        '.border-table-row': {
          borderColor: '#C4C4C41A !important',
        },
      });

    }),
  ],
};

export default config;
