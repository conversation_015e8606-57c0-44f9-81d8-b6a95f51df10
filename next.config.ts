import type { NextConfig } from "next";
import path from "path";

const nextConfig: NextConfig = {
  /* config options here */
  // output: "export",  // Uncomment the following line only for building purposes. By default, this line should remain commented out.
  trailingSlash: false, // Temporarily disabled to fix routing issues
  basePath: "",
  assetPrefix: "",

  // Environment variables configuration
  env: {
    // Ensure these are available at build time and runtime
    NEXT_PUBLIC_ADMIN_BACKEND_URL: process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL || "https://adminapi.ingrandstation.com",
    NEXT_PUBLIC_STAGING_BACKEND_URL: process.env.NEXT_PUBLIC_STAGING_BACKEND_URL || "https://staging-reports-api.8dexsuperadmin.com",
    NEXT_PUBLIC_REPORTING_BACKEND_URL: process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || "https://reporting.ingrandstation.com",
  },
  images: {
    // Remove imgix loader for local images
    formats: ["image/webp", "image/avif"], // Modern formats for better performance
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year cache
    dangerouslyAllowSVG: true,
    contentDispositionType: "attachment",
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    remotePatterns: [
      {
        protocol: "https",
        hostname: "staging-reports-api.8dexsuperadmin.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "assets.roylfc.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
  typescript: {
    ignoreBuildErrors: false, // Enable TypeScript validation during build
    // Disable TypeScript checking during development for better performance
    // Type checking will still run during builds and commits
    tsconfigPath: process.env.NODE_ENV === 'development' ? './tsconfig.dev.json' : './tsconfig.json',
  },
  sassOptions: {
    includePaths: [path.join(__dirname, "public/assets/scss")],
    silenceDeprecations: ["legacy-js-api"],
    quietDeps: true,
  },
  reactStrictMode: false, // Disable Strict Mode if necessary
  eslint: {
    ignoreDuringBuilds: false, // Enable ESLint validation during build
    // Disable ESLint during development for better performance
    dirs: process.env.NODE_ENV === 'development' ? [] : ['app', 'shared'],
  },
  // External packages for server components (moved from experimental)
  // serverExternalPackages: ['@tanstack/react-query'], // Commented out to fix SSR issues

  // Performance optimizations - temporarily simplified to fix module resolution
  experimental: {
    // optimizeCss: true, // Disabled due to critters dependency issue
    // optimizePackageImports: ['@/shared/UI/components', '@/shared/utils'], // Temporarily disabled
    // Enable partial prerendering for better performance
    ppr: false, // Set to true when stable
    // Optimize server components
    // serverMinification: true, // Temporarily disabled
    // Enable turbo mode for faster builds
    // turbo: {
    //   rules: {
    //     '*.svg': {
    //       loaders: ['@svgr/webpack'],
    //       as: '*.js',
    //     },
    //   },
    // },
  },
  // Enable compression
  compress: true,

  // Headers for performance and security
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-DNS-Prefetch-Control",
            value: "on"
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block"
          },
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN"
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff"
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin"
          }
        ],
      },
      {
        source: "/api/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=300, stale-while-revalidate=60"
          }
        ],
      },
      {
        source: "/_next/static/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable"
          }
        ],
      },
    ];
  },

  // Webpack optimizations - temporarily disabled to fix module resolution issues
  // webpack: (config, { dev, isServer }) => {
  //   // Optimize bundle splitting
  //   if (!dev && !isServer) {
  //     config.optimization = {
  //       ...config.optimization,
  //       splitChunks: {
  //         chunks: 'all',
  //         cacheGroups: {
  //           vendor: {
  //             test: /[\\/]node_modules[\\/]/,
  //             name: 'vendors',
  //             chunks: 'all',
  //             priority: 10,
  //           },
  //           common: {
  //             name: 'common',
  //             minChunks: 2,
  //             chunks: 'all',
  //             priority: 5,
  //             reuseExistingChunk: true,
  //           },
  //           ui: {
  //             test: /[\\/]shared[\\/]UI[\\/]/,
  //             name: 'ui-components',
  //             chunks: 'all',
  //             priority: 8,
  //           },
  //           userManagement: {
  //             test: /[\\/]user-management[\\/]/,
  //             name: 'user-management',
  //             chunks: 'all',
  //             priority: 7,
  //           },
  //         },
  //       },
  //     };
  //   }

  //   return config;
  // },
};

export default nextConfig;
